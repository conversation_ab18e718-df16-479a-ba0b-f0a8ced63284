name: Deploy Zurich Frontend

on:
  push:
    branches:
      - zurich/dinesh/zurich_workflow_uc05
      - zurich/dinesh/zurich_workflow_uc05_dev
    paths:
      - 'rozie-zurich-uc05/zurich-workflow-app/frontend/**'
      - 'rozie-zurich-uc05/zurich-workflow-app/frontend.Dockerfile'
      - 'rozie-zurich-uc05/zurich-workflow-app/nginx/**'
      - '.github/workflows/deploy-zurich-frontend.yml'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'dev'
        type: choice
        options:
          - dev
          - staging
          - prod
      force_deploy:
        description: 'Force deploy even if no changes'
        required: false
        default: 'false'
        type: boolean

env:
  AWS_REGION: ca-central-1
  ECS_SERVICE: rozieai-zurich-uc05
  ECS_CLUSTER: dev-scc-demo
  ECS_TASK_DEFINITION: rozieai-zurich-uc05

jobs:
  deploy-frontend:
    name: Deploy Zurich Frontend
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || 'dev' }}

    steps:
    - uses: actions/checkout@v2
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v1

    - name: Build and push Frontend image
      working-directory: rozie-zurich-uc05/zurich-workflow-app
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        IMAGE_TAG: frontend-${{ github.sha }}
        ENVIRONMENT: ${{ github.event.inputs.environment || 'dev' }}
      run: |
        echo "🏗️ Building Frontend image for $ENVIRONMENT..."
        docker build -f frontend.Dockerfile -t $ECR_REGISTRY/rozieai-zrh-uc05-frontend-$ENVIRONMENT:$IMAGE_TAG .
        docker push $ECR_REGISTRY/rozieai-zrh-uc05-frontend-$ENVIRONMENT:$IMAGE_TAG
        echo "✅ Frontend image pushed: $ECR_REGISTRY/rozieai-zrh-uc05-frontend-$ENVIRONMENT:$IMAGE_TAG"

    - name: Build and push Nginx image
      working-directory: rozie-zurich-uc05/zurich-workflow-app
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        IMAGE_TAG: nginx-${{ github.sha }}
        ENVIRONMENT: ${{ github.event.inputs.environment || 'dev' }}
      run: |
        echo "🏗️ Building Nginx image for $ENVIRONMENT..."
        docker build -f nginx/Dockerfile -t $ECR_REGISTRY/rozieai-zrh-uc05-nginx-$ENVIRONMENT:$IMAGE_TAG ./nginx
        docker push $ECR_REGISTRY/rozieai-zrh-uc05-nginx-$ENVIRONMENT:$IMAGE_TAG
        echo "✅ Nginx image pushed: $ECR_REGISTRY/rozieai-zrh-uc05-nginx-$ENVIRONMENT:$IMAGE_TAG"

    - name: Download task definition
      run: |
        aws ecs describe-task-definition --task-definition $ECS_TASK_DEFINITION --query taskDefinition > task-definition.json

    - name: Update Frontend container in task definition
      id: task-def-frontend
      uses: aws-actions/amazon-ecs-render-task-definition@v1
      with:
        task-definition: task-definition.json
        container-name: frontend
        image: ${{ steps.login-ecr.outputs.registry }}/rozieai-zrh-uc05-frontend-${{ github.event.inputs.environment || 'dev' }}:frontend-${{ github.sha }}

    - name: Update Nginx container in task definition
      id: task-def-nginx
      uses: aws-actions/amazon-ecs-render-task-definition@v1
      with:
        task-definition: ${{ steps.task-def-frontend.outputs.task-definition }}
        container-name: nginx
        image: ${{ steps.login-ecr.outputs.registry }}/rozieai-zrh-uc05-nginx-${{ github.event.inputs.environment || 'dev' }}:nginx-${{ github.sha }}

    - name: Deploy to Amazon ECS
      uses: aws-actions/amazon-ecs-deploy-task-definition@v1
      with:
        task-definition: ${{ steps.task-def-nginx.outputs.task-definition }}
        service: ${{ env.ECS_SERVICE }}
        cluster: ${{ env.ECS_CLUSTER }}
        wait-for-service-stability: true

    - name: Frontend deployment success
      run: |
        echo "✅ Frontend deployment completed successfully!"
        echo "🌐 Frontend URL: https://rozieai-zrh-uc05.dev-scc-demo.rozie.ai/"
        echo "📦 Environment: ${{ github.event.inputs.environment || 'dev' }}"
        echo "🔧 Components deployed: Frontend + Nginx"
