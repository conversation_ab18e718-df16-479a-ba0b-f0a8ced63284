name: Deploy N8N Standalone

on:
  push:
    branches:
      - zurich/dinesh/zurich_workflow_uc05
      - zurich/dinesh/zurich_workflow_uc05_dev
    paths:
      - 'rozie-zurich-uc05/n8n-standalone/**'
      - '.github/workflows/deploy-n8n-standalone.yml'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'dev'
        type: choice
        options:
          - dev
          - staging
          - prod
      force_deploy:
        description: 'Force deploy even if no changes'
        required: false
        default: 'false'
        type: boolean

env:
  AWS_REGION: ca-central-1
  SERVICE_NAME: n8n-zurich-standalone

jobs:
  deploy-n8n-standalone:
    name: Deploy Standalone N8N
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || 'dev' }}
    env:
      environment: ${{ github.event.inputs.environment || 'dev' }}
      ECR_REPO_URI: n8nio/n8n:latest
      domain-name: n8n-zurich.dev-scc-demo.rozie.ai
      AWS_REGION: ca-central-1
      hosted-zone-id: ${{ secrets.HOSTED_ZONE_ID }}
      acm-cert-arn: ${{ secrets.ACM_CERT_ARN }}

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Serverless AWS Authentication
      run: sls config credentials --provider aws --key ${{ secrets.AWS_ACCESS_KEY }} --secret ${{ secrets.AWS_SECRET_KEY }}

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install Serverless Framework
      run: npm install -g serverless@3.38.0

    - name: Deploy N8N Infrastructure
      working-directory: rozie-zurich-uc05/n8n-standalone
      env:
        STAGE: ${{ github.event.inputs.environment || 'dev' }}
      run: |
        echo "🚀 Deploying N8N Standalone Infrastructure..."
        echo "Environment: $STAGE"
        echo "Region: $AWS_REGION"
        
        # Deploy infrastructure using serverless (matches dev.yml pattern)
        sls deploy --region ca-central-1 --stage $STAGE
        
        echo "✅ N8N Infrastructure deployment completed!"

    - name: Get deployment info
      working-directory: rozie-zurich-uc05/n8n-standalone
      env:
        STAGE: ${{ github.event.inputs.environment || 'dev' }}
      run: |
        echo "📋 Deployment Information:"
        echo "========================="
        
        # Get load balancer DNS
        LB_DNS=$(aws cloudformation describe-stacks \
          --stack-name n8n-zurich-standalone-$STAGE \
          --query 'Stacks[0].Outputs[?OutputKey==`N8NLoadBalancerDNS`].OutputValue' \
          --output text)
        
        echo "Load Balancer DNS: $LB_DNS"
        echo "N8N URL: https://n8n-zurich.dev-scc-demo.rozie.ai"
        echo "Health Check: https://n8n-zurich.dev-scc-demo.rozie.ai/healthz"
        
        # Save outputs for later steps
        echo "LB_DNS=$LB_DNS" >> $GITHUB_ENV

    - name: Wait for service to be stable
      env:
        STAGE: ${{ github.event.inputs.environment || 'dev' }}
      run: |
        echo "⏳ Waiting for N8N service to stabilize..."
        
        # Get cluster and service names
        CLUSTER_NAME="n8n-zurich-$STAGE"
        SERVICE_NAME="n8n-zurich-$STAGE"
        
        # Wait for service to be stable
        aws ecs wait services-stable \
          --cluster $CLUSTER_NAME \
          --services $SERVICE_NAME \
          --region $AWS_REGION
        
        echo "✅ N8N service is stable!"

    - name: Test N8N deployment
      run: |
        echo "🧪 Testing N8N deployment..."
        
        # Test health endpoint
        for i in {1..10}; do
          if curl -f -s "http://$LB_DNS/healthz"; then
            echo "✅ N8N health check passed!"
            break
          else
            echo "⏳ Waiting for N8N to be ready... (attempt $i/10)"
            sleep 30
          fi
        done

    - name: Deployment summary
      run: |
        echo "🎉 N8N Standalone Deployment Complete!"
        echo "======================================"
        echo "Environment: ${{ github.event.inputs.environment || 'dev' }}"
        echo "Region: $AWS_REGION"
        echo "Load Balancer: $LB_DNS"
        echo ""
        echo "🌐 Access URLs:"
        echo "N8N Interface: https://n8n-zurich.dev-scc-demo.rozie.ai"
        echo "Health Check: https://n8n-zurich.dev-scc-demo.rozie.ai/healthz"
        echo ""
        echo "📋 Next Steps:"
        echo "1. Configure DNS to point n8n-zurich.dev-scc-demo.rozie.ai to $LB_DNS"
        echo "2. Set up SSL certificate for HTTPS"
        echo "3. Test N8N workflows"
        echo ""
        echo "🔧 Management Commands:"
        echo "View logs: aws logs tail /ecs/n8n-zurich-${{ github.event.inputs.environment || 'dev' }} --follow"
        echo "Scale service: aws ecs update-service --cluster n8n-zurich-${{ github.event.inputs.environment || 'dev' }} --service n8n-zurich-${{ github.event.inputs.environment || 'dev' }} --desired-count 2"
