name: Deploy N8N Standalone

on:
  push:
    branches:
      - zurich/dinesh/zurich_workflow_uc05_n8n
    paths:
      - 'rozie-zurich-uc05/n8n-standalone/**'
      - '.github/workflows/deploy-n8n-standalone.yml'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'dev'
        type: choice
        options:
          - dev
          - staging
          - prod
      force_deploy:
        description: 'Force deploy even if no changes'
        required: false
        default: 'false'
        type: boolean

env:
  AWS_REGION: ca-central-1
  SERVICE_NAME: n8n-zurich-standalone

jobs:
  deploy-n8n-standalone:
    name: Deploy Standalone N8N
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || 'dev' }}
    env:
      environment: ${{ github.event.inputs.environment || 'dev' }}
      ecrrepository: n8n-zurich
      hosted-zone-id: ${{ secrets.HOSTED_ZONE_ID }}
      acm-cert-arn: ${{ secrets.ACM_CERT_ARN }}
      domain-name: n8n-zurich.dev-scc-demo.rozie.ai
      aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
      aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY }}
      aws-region: ${{ secrets.AWS_REGION }}
      IMAGE_TAG: v${{ github.sha }}

    steps:
    - uses: actions/checkout@v2
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY }}
        aws-region: ${{ secrets.AWS_REGION }}
    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v1
    - name: Create ECR repository for N8N
      run: |
        echo "$ecrrepository-$environment"
        aws ecr describe-repositories --repository-names $ecrrepository-$environment || aws ecr create-repository --registry-id ${{secrets.AWS_ACCOUNT_ID}} --repository-name $ecrrepository-$environment
    - name: Build, tag, and push N8N image to Amazon ECR
      id: build-image
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
      run: |
        cd rozie-zurich-uc05/n8n-standalone
        docker build -t "$ECR_REGISTRY/$ecrrepository-$environment:$IMAGE_TAG" -f Dockerfile .
        docker push "$ECR_REGISTRY/$ecrrepository-$environment:$IMAGE_TAG"
        echo "image=$ECR_REGISTRY/$ecrrepository-$environment:$IMAGE_TAG" >> $GITHUB_OUTPUT
    - uses: actions/setup-node@v1
      with:
        node-version: "18.x"
    - name: Install Serverless Framework
      run: npm install -g serverless@3.38.0
    - name: Serverless AWS Authentication
      run: sls config credentials --provider aws --key $aws-access-key-id --secret $aws-secret-access-key

    - name: Enable SLS debug mode
      run: export SLS_DEBUG=true
    - name: Deploy N8N Standalone Infrastructure
      env:
        ECR_REPO_URI: ${{ steps.build-image.outputs.image }}
      run: |
        echo "$ECR_REPO_URI"
        cd rozie-zurich-uc05/n8n-standalone && sls deploy --region ${{ env.aws-region }} --stage $environment -E ECR_REPO_URI=$ECR_REPO_URI -E hosted-zone-id=$hosted-zone-id -E acm-cert-arn=$acm-cert-arn -E domain-name=$domain-name
        
        echo "✅ N8N Infrastructure deployment completed!"

    - name: Get deployment info
      working-directory: rozie-zurich-uc05/n8n-standalone
      env:
        STAGE: ${{ github.event.inputs.environment || 'dev' }}
      run: |
        echo "📋 Deployment Information:"
        echo "========================="
        
        # Get load balancer DNS
        LB_DNS=$(aws cloudformation describe-stacks \
          --stack-name n8n-zurich-standalone-$STAGE \
          --query 'Stacks[0].Outputs[?OutputKey==`N8NLoadBalancerDNS`].OutputValue' \
          --output text)
        
        echo "Load Balancer DNS: $LB_DNS"
        echo "N8N URL: https://n8n-zurich.dev-scc-demo.rozie.ai"
        echo "Health Check: https://n8n-zurich.dev-scc-demo.rozie.ai/healthz"
        
        # Save outputs for later steps
        echo "LB_DNS=$LB_DNS" >> $GITHUB_ENV

    - name: Wait for service to be stable
      env:
        STAGE: ${{ github.event.inputs.environment || 'dev' }}
      run: |
        echo "⏳ Waiting for N8N service to stabilize..."
        
        # Get cluster and service names (match serverless.yml naming)
        CLUSTER_NAME="n8n-zurich-standalone-$STAGE-cluster"
        SERVICE_NAME="n8n-zurich-standalone-$STAGE-service"

        echo "Cluster: $CLUSTER_NAME"
        echo "Service: $SERVICE_NAME"

        # Wait for service to be stable
        aws ecs wait services-stable \
          --cluster $CLUSTER_NAME \
          --services $SERVICE_NAME \
          --region $AWS_REGION
        
        echo "✅ N8N service is stable!"

    - name: Test N8N deployment
      run: |
        echo "🧪 Testing N8N deployment..."
        
        # Test health endpoint
        for i in {1..10}; do
          if curl -f -s "http://$LB_DNS/healthz"; then
            echo "✅ N8N health check passed!"
            break
          else
            echo "⏳ Waiting for N8N to be ready... (attempt $i/10)"
            sleep 30
          fi
        done

    - name: Deployment summary
      run: |
        echo "🎉 N8N Standalone Deployment Complete!"
        echo "======================================"
        echo "Environment: ${{ github.event.inputs.environment || 'dev' }}"
        echo "Region: $AWS_REGION"
        echo "Load Balancer: $LB_DNS"
        echo ""
        echo "🌐 Access URLs:"
        echo "N8N Interface: https://n8n-zurich.dev-scc-demo.rozie.ai"
        echo "Health Check: https://n8n-zurich.dev-scc-demo.rozie.ai/healthz"
        echo ""
        echo "📋 Next Steps:"
        echo "1. Configure DNS to point n8n-zurich.dev-scc-demo.rozie.ai to $LB_DNS"
        echo "2. Set up SSL certificate for HTTPS"
        echo "3. Test N8N workflows"
        echo ""
        echo "🔧 Management Commands:"
        echo "View logs: aws logs tail /ecs/n8n-zurich-${{ github.event.inputs.environment || 'dev' }} --follow"
        echo "Scale service: aws ecs update-service --cluster n8n-zurich-${{ github.event.inputs.environment || 'dev' }} --service n8n-zurich-${{ github.event.inputs.environment || 'dev' }} --desired-count 2"
