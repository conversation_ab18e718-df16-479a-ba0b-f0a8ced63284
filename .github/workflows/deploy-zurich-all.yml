name: Deploy All Zurich Components

on:
  push:
    branches:
      - zurich/dinesh/zurich_workflow_uc05
      - zurich/dinesh/zurich_workflow_uc05_dev
    paths:
      - 'rozie-zurich-uc05/zurich-workflow-app/**'
      - '.github/workflows/deploy-zurich-all.yml'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'dev'
        type: choice
        options:
          - dev
          - staging
          - prod
      force_deploy:
        description: 'Force deploy all components'
        required: false
        default: 'false'
        type: boolean
      deploy_infrastructure:
        description: 'Deploy infrastructure using serverless'
        required: false
        default: 'false'
        type: boolean

env:
  AWS_REGION: ca-central-1
  ECS_SERVICE: rozieai-zurich-uc05
  ECS_CLUSTER: dev-scc-demo
  ECS_TASK_DEFINITION: rozieai-zurich-uc05

jobs:
  deploy-infrastructure:
    name: Deploy Infrastructure
    runs-on: ubuntu-latest
    if: github.event.inputs.deploy_infrastructure == 'true'
    environment: ${{ github.event.inputs.environment || 'dev' }}

    steps:
    - uses: actions/checkout@v2
    - uses: actions/setup-node@v1
      with:
        node-version: "18.x"
    - name: Install Serverless Framework
      run: npm install -g serverless@3.38.0
    - name: Serverless AWS Authentication
      run: sls config credentials --provider aws --key ${{ secrets.AWS_ACCESS_KEY }} --secret ${{ secrets.AWS_SECRET_KEY }}

    - name: Deploy Infrastructure
      working-directory: rozie-zurich-uc05
      env:
        STAGE: ${{ github.event.inputs.environment || 'dev' }}
      run: |
        echo "🏗️ Deploying infrastructure for $STAGE..."
        sls deploy --region ca-central-1 --stage $STAGE

  deploy-all-components:
    name: Deploy All Components
    runs-on: ubuntu-latest
    needs: [deploy-infrastructure]
    if: always() && (needs.deploy-infrastructure.result == 'success' || needs.deploy-infrastructure.result == 'skipped')
    environment: ${{ github.event.inputs.environment || 'dev' }}
    env:
      environment: ${{ github.event.inputs.environment || 'dev' }}
      ecrrepository: rozieai-zrh-uc05
      hosted-zone-id: ${{ secrets.HOSTED_ZONE_ID }}
      acm-cert-arn: ${{ secrets.ACM_CERT_ARN }}
      domain-name: rozieai-zrh-uc05.dev-scc-demo.rozie.ai
      aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
      aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY }}
      aws-region: ${{ secrets.AWS_REGION }}
      IMAGE_TAG: v${{ github.sha }}
      api_key: Test@123

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v1

    - name: Build and push all images
      working-directory: rozie-zurich-uc05/zurich-workflow-app
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        IMAGE_TAG: all-${{ github.sha }}
        ENVIRONMENT: ${{ github.event.inputs.environment || 'dev' }}
      run: |
        echo "🏗️ Building all images for $ENVIRONMENT..."
        
        # Build Backend
        echo "Building Backend..."
        docker build -f Dockerfile -t $ECR_REGISTRY/rozieai-zrh-uc05-backend-$ENVIRONMENT:$IMAGE_TAG .
        docker push $ECR_REGISTRY/rozieai-zrh-uc05-backend-$ENVIRONMENT:$IMAGE_TAG
        
        # Build Frontend
        echo "Building Frontend..."
        docker build -f frontend.Dockerfile -t $ECR_REGISTRY/rozieai-zrh-uc05-frontend-$ENVIRONMENT:$IMAGE_TAG .
        docker push $ECR_REGISTRY/rozieai-zrh-uc05-frontend-$ENVIRONMENT:$IMAGE_TAG
        
        # Build Nginx
        echo "Building Nginx..."
        docker build -f nginx/Dockerfile -t $ECR_REGISTRY/rozieai-zrh-uc05-nginx-$ENVIRONMENT:$IMAGE_TAG ./nginx
        docker push $ECR_REGISTRY/rozieai-zrh-uc05-nginx-$ENVIRONMENT:$IMAGE_TAG
        
        # Build Dashboard
        echo "Building Dashboard..."
        docker build -f zurich-dashboard/Dockerfile -t $ECR_REGISTRY/rozieai-zrh-uc05-dashboard-$ENVIRONMENT:$IMAGE_TAG ./zurich-dashboard
        docker push $ECR_REGISTRY/rozieai-zrh-uc05-dashboard-$ENVIRONMENT:$IMAGE_TAG
        
        echo "✅ All images built and pushed successfully!"

    - name: Download task definition
      run: |
        aws ecs describe-task-definition --task-definition $ECS_TASK_DEFINITION --query taskDefinition > task-definition.json

    - name: Update all containers in task definition
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        IMAGE_TAG: all-${{ github.sha }}
        ENVIRONMENT: ${{ github.event.inputs.environment || 'dev' }}
      run: |
        echo "🔄 Updating task definition with new images..."
        
        # Update Backend
        aws ecs render-task-definition \
          --task-definition task-definition.json \
          --container-overrides name=backend,imageUri=$ECR_REGISTRY/rozieai-zrh-uc05-backend-$ENVIRONMENT:$IMAGE_TAG \
          > task-definition-backend.json
        
        # Update Frontend
        aws ecs render-task-definition \
          --task-definition task-definition-backend.json \
          --container-overrides name=frontend,imageUri=$ECR_REGISTRY/rozieai-zrh-uc05-frontend-$ENVIRONMENT:$IMAGE_TAG \
          > task-definition-frontend.json
        
        # Update Nginx
        aws ecs render-task-definition \
          --task-definition task-definition-frontend.json \
          --container-overrides name=nginx,imageUri=$ECR_REGISTRY/rozieai-zrh-uc05-nginx-$ENVIRONMENT:$IMAGE_TAG \
          > task-definition-nginx.json
        
        # Update Dashboard
        aws ecs render-task-definition \
          --task-definition task-definition-nginx.json \
          --container-overrides name=dashboard,imageUri=$ECR_REGISTRY/rozieai-zrh-uc05-dashboard-$ENVIRONMENT:$IMAGE_TAG \
          > task-definition-final.json

    - name: Deploy to Amazon ECS
      run: |
        aws ecs update-service \
          --cluster $ECS_CLUSTER \
          --service $ECS_SERVICE \
          --task-definition task-definition-final.json \
          --force-new-deployment
        
        echo "⏳ Waiting for service to stabilize..."
        aws ecs wait services-stable \
          --cluster $ECS_CLUSTER \
          --services $ECS_SERVICE

    - name: Test all deployments
      run: |
        echo "🧪 Testing all deployments..."
        sleep 60
        
        # Test Backend
        echo "Testing Backend..."
        curl -f "https://rozieai-zrh-uc05.dev-scc-demo.rozie.ai/health" || echo "Backend test failed"
        
        # Test Frontend
        echo "Testing Frontend..."
        curl -f "https://rozieai-zrh-uc05.dev-scc-demo.rozie.ai/" || echo "Frontend test failed"
        
        # Test Dashboard
        echo "Testing Dashboard..."
        curl -f "https://rozieai-zrh-uc05.dev-scc-demo.rozie.ai/dashboard/" || echo "Dashboard test failed"

    - name: All components deployment success
      run: |
        echo "🎉 All components deployed successfully!"
        echo "=================================="
        echo "🌐 Frontend: https://rozieai-zrh-uc05.dev-scc-demo.rozie.ai/"
        echo "🔧 Backend API: https://rozieai-zrh-uc05.dev-scc-demo.rozie.ai/docs"
        echo "📊 Dashboard: https://rozieai-zrh-uc05.dev-scc-demo.rozie.ai/dashboard/"
        echo "🔍 Health: https://rozieai-zrh-uc05.dev-scc-demo.rozie.ai/health"
        echo "📦 Environment: ${{ github.event.inputs.environment || 'dev' }}"
