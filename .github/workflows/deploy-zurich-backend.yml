name: Deploy Zurich Backend

on:
  push:
    branches:
      - zurich/dinesh/zurich_workflow_uc05
      - zurich/dinesh/zurich_workflow_uc05_dev
    paths:
      - 'rozie-zurich-uc05/zurich-workflow-app/backend/**'
      - 'rozie-zurich-uc05/zurich-workflow-app/Dockerfile'
      - 'rozie-zurich-uc05/zurich-workflow-app/requirements.txt'
      - 'rozie-zurich-uc05/zurich-workflow-app/baml_models/**'
      - '.github/workflows/deploy-zurich-backend.yml'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'dev'
        type: choice
        options:
          - dev
          - staging
          - prod
      force_deploy:
        description: 'Force deploy even if no changes'
        required: false
        default: 'false'
        type: boolean

env:
  AWS_REGION: ca-central-1
  ECS_SERVICE: rozieai-zurich-uc05
  ECS_CLUSTER: dev-scc-demo
  ECS_TASK_DEFINITION: rozieai-zurich-uc05

jobs:
  deploy-backend:
    name: Deploy Zurich Backend
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || 'dev' }}

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v1

    - name: Build and push Backend image
      working-directory: rozie-zurich-uc05/zurich-workflow-app
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        IMAGE_TAG: backend-${{ github.sha }}
        ENVIRONMENT: ${{ github.event.inputs.environment || 'dev' }}
      run: |
        echo "🏗️ Building Backend image for $ENVIRONMENT..."
        docker build -f Dockerfile -t $ECR_REGISTRY/rozieai-zrh-uc05-backend-$ENVIRONMENT:$IMAGE_TAG .
        docker push $ECR_REGISTRY/rozieai-zrh-uc05-backend-$ENVIRONMENT:$IMAGE_TAG
        echo "✅ Backend image pushed: $ECR_REGISTRY/rozieai-zrh-uc05-backend-$ENVIRONMENT:$IMAGE_TAG"

    - name: Download task definition
      run: |
        aws ecs describe-task-definition --task-definition $ECS_TASK_DEFINITION --query taskDefinition > task-definition.json

    - name: Update Backend container in task definition
      id: task-def-backend
      uses: aws-actions/amazon-ecs-render-task-definition@v1
      with:
        task-definition: task-definition.json
        container-name: backend
        image: ${{ steps.login-ecr.outputs.registry }}/rozieai-zrh-uc05-backend-${{ github.event.inputs.environment || 'dev' }}:backend-${{ github.sha }}

    - name: Deploy to Amazon ECS
      uses: aws-actions/amazon-ecs-deploy-task-definition@v1
      with:
        task-definition: ${{ steps.task-def-backend.outputs.task-definition }}
        service: ${{ env.ECS_SERVICE }}
        cluster: ${{ env.ECS_CLUSTER }}
        wait-for-service-stability: true

    - name: Test Backend deployment
      run: |
        echo "🧪 Testing Backend deployment..."
        sleep 30
        
        # Test health endpoint
        for i in {1..10}; do
          if curl -f -s "https://rozieai-zrh-uc05.dev-scc-demo.rozie.ai/health"; then
            echo "✅ Backend health check passed!"
            break
          else
            echo "⏳ Waiting for backend to be ready... (attempt $i/10)"
            sleep 30
          fi
        done

    - name: Backend deployment success
      run: |
        echo "✅ Backend deployment completed successfully!"
        echo "🌐 Backend API: https://rozieai-zrh-uc05.dev-scc-demo.rozie.ai/docs"
        echo "🔍 Health Check: https://rozieai-zrh-uc05.dev-scc-demo.rozie.ai/health"
        echo "📦 Environment: ${{ github.event.inputs.environment || 'dev' }}"
        echo "🔧 Component deployed: Backend API"
