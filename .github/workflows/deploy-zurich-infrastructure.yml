name: Deploy Zurich Infrastructure

on:
  push:
    branches:
      - zurich/dinesh/zurich_workflow_uc05
      - zurich/dinesh/zurich_workflow_uc05_dev
    paths:
      - 'rozie-zurich-uc05/serverless.yml'
      - 'rozie-zurich-uc05/dev.yml'
      - '.github/workflows/deploy-zurich-infrastructure.yml'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'dev'
        type: choice
        options:
          - dev
          - staging
          - prod
      action:
        description: 'Action to perform'
        required: true
        default: 'deploy'
        type: choice
        options:
          - deploy
          - remove
          - info

env:
  AWS_REGION: ca-central-1

jobs:
  deploy-infrastructure:
    name: Manage Zurich Infrastructure
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || 'dev' }}

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install Serverless Framework
      run: |
        npm install -g serverless
        npm install -g serverless-plugin-aws-alerts

    - name: Deploy Infrastructure
      if: github.event.inputs.action == 'deploy' || github.event.inputs.action == ''
      working-directory: rozie-zurich-uc05
      env:
        STAGE: ${{ github.event.inputs.environment || 'dev' }}
      run: |
        echo "🏗️ Deploying Zurich infrastructure for $STAGE..."
        echo "Region: $AWS_REGION"
        
        # Deploy infrastructure using serverless
        serverless deploy --stage $STAGE --region $AWS_REGION --verbose
        
        echo "✅ Infrastructure deployment completed!"

    - name: Get Infrastructure Info
      if: github.event.inputs.action == 'info' || github.event.inputs.action == 'deploy' || github.event.inputs.action == ''
      working-directory: rozie-zurich-uc05
      env:
        STAGE: ${{ github.event.inputs.environment || 'dev' }}
      run: |
        echo "📋 Infrastructure Information:"
        echo "============================="
        
        # Get stack outputs
        aws cloudformation describe-stacks \
          --stack-name rozie-zurich-uc05-$STAGE \
          --query 'Stacks[0].Outputs' \
          --output table || echo "Stack not found or no outputs"
        
        # Get ECS cluster info
        aws ecs describe-clusters \
          --clusters dev-scc-demo \
          --query 'clusters[0]' \
          --output table || echo "ECS cluster not found"
        
        # Get ECS service info
        aws ecs describe-services \
          --cluster dev-scc-demo \
          --services rozieai-zurich-uc05 \
          --query 'services[0]' \
          --output table || echo "ECS service not found"

    - name: Remove Infrastructure
      if: github.event.inputs.action == 'remove'
      working-directory: rozie-zurich-uc05
      env:
        STAGE: ${{ github.event.inputs.environment || 'dev' }}
      run: |
        echo "🗑️ Removing Zurich infrastructure for $STAGE..."
        echo "⚠️ This will delete all resources!"
        
        # Remove infrastructure using serverless
        serverless remove --stage $STAGE --region $AWS_REGION --verbose
        
        echo "✅ Infrastructure removal completed!"

    - name: Infrastructure deployment summary
      if: github.event.inputs.action == 'deploy' || github.event.inputs.action == ''
      run: |
        echo "🎉 Infrastructure Management Complete!"
        echo "===================================="
        echo "Environment: ${{ github.event.inputs.environment || 'dev' }}"
        echo "Region: $AWS_REGION"
        echo "Action: ${{ github.event.inputs.action || 'deploy' }}"
        echo ""
        echo "🔧 Next Steps:"
        echo "1. Deploy application components using other workflows"
        echo "2. Test the deployed services"
        echo "3. Monitor CloudWatch logs for any issues"
        echo ""
        echo "📋 Useful Commands:"
        echo "View ECS services: aws ecs list-services --cluster dev-scc-demo"
        echo "View logs: aws logs tail /ecs/rozieai-zurich-uc05 --follow"
