name: Deploy Zurich Dashboard

on:
  push:
    branches:
      - zurich/dinesh/zurich_workflow_uc05
      - zurich/dinesh/zurich_workflow_uc05_dev
    paths:
      - 'rozie-zurich-uc05/zurich-workflow-app/zurich-dashboard/**'
      - '.github/workflows/deploy-zurich-dashboard.yml'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'dev'
        type: choice
        options:
          - dev
          - staging
          - prod
      force_deploy:
        description: 'Force deploy even if no changes'
        required: false
        default: 'false'
        type: boolean

env:
  AWS_REGION: ca-central-1
  ECS_SERVICE: rozieai-zurich-uc05
  ECS_CLUSTER: dev-scc-demo
  ECS_TASK_DEFINITION: rozieai-zurich-uc05

jobs:
  deploy-dashboard:
    name: Deploy Zurich Dashboard
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || 'dev' }}

    steps:
    - uses: actions/checkout@v2
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v1

    - name: Build and push Dashboard image
      working-directory: rozie-zurich-uc05/zurich-workflow-app
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        IMAGE_TAG: dashboard-${{ github.sha }}
        ENVIRONMENT: ${{ github.event.inputs.environment || 'dev' }}
      run: |
        echo "🏗️ Building Dashboard image for $ENVIRONMENT..."
        docker build -f zurich-dashboard/Dockerfile -t $ECR_REGISTRY/rozieai-zrh-uc05-dashboard-$ENVIRONMENT:$IMAGE_TAG ./zurich-dashboard
        docker push $ECR_REGISTRY/rozieai-zrh-uc05-dashboard-$ENVIRONMENT:$IMAGE_TAG
        echo "✅ Dashboard image pushed: $ECR_REGISTRY/rozieai-zrh-uc05-dashboard-$ENVIRONMENT:$IMAGE_TAG"

    - name: Download task definition
      run: |
        aws ecs describe-task-definition --task-definition $ECS_TASK_DEFINITION --query taskDefinition > task-definition.json

    - name: Update Dashboard container in task definition
      id: task-def-dashboard
      uses: aws-actions/amazon-ecs-render-task-definition@v1
      with:
        task-definition: task-definition.json
        container-name: dashboard
        image: ${{ steps.login-ecr.outputs.registry }}/rozieai-zrh-uc05-dashboard-${{ github.event.inputs.environment || 'dev' }}:dashboard-${{ github.sha }}

    - name: Deploy to Amazon ECS
      uses: aws-actions/amazon-ecs-deploy-task-definition@v1
      with:
        task-definition: ${{ steps.task-def-dashboard.outputs.task-definition }}
        service: ${{ env.ECS_SERVICE }}
        cluster: ${{ env.ECS_CLUSTER }}
        wait-for-service-stability: true

    - name: Test Dashboard deployment
      run: |
        echo "🧪 Testing Dashboard deployment..."
        sleep 30
        
        # Test dashboard endpoint
        for i in {1..10}; do
          if curl -f -s "https://rozieai-zrh-uc05.dev-scc-demo.rozie.ai/dashboard/" > /dev/null; then
            echo "✅ Dashboard health check passed!"
            break
          else
            echo "⏳ Waiting for dashboard to be ready... (attempt $i/10)"
            sleep 30
          fi
        done

    - name: Dashboard deployment success
      run: |
        echo "✅ Dashboard deployment completed successfully!"
        echo "🌐 Dashboard URL: https://rozieai-zrh-uc05.dev-scc-demo.rozie.ai/dashboard/"
        echo "📦 Environment: ${{ github.event.inputs.environment || 'dev' }}"
        echo "🔧 Component deployed: Support Dashboard"
