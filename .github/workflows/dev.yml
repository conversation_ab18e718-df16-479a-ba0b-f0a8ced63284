name: Deploy rozie mock company backend

on:
  workflow_dispatch:
  push:
    branches:
      - development
      - zurich/dinesh/zurich_workflow_uc05

jobs:
  # JOB to run change detection
  changes:
    runs-on: ubuntu-latest
    # Set job outputs to values from filter step
    outputs:
      customer-profile: ${{steps.filter.outputs.customer-profile}}
      flight-details: ${{steps.filter.outputs.flight-details}}
      flight-booking: ${{steps.filter.outputs.flight-booking}}
      customer-loyalty-program: ${{steps.filter.outputs.customer-loyalty-program}}
      PNR-details: ${{steps.filter.outputs.PNR-details}}
      appointment: ${{steps.filter.outputs.appointment}}
      email-sms-otp: ${{steps.filter.outputs.email-sms-otp}}
      flight-schedule: ${{steps.filter.outputs.flight-schedule}}
      flight-util: ${{steps.filter.outputs.flight-util}}
      flight-status : ${{steps.filter.outputs.flight-status}}
      baggage-details : ${{steps.filter.outputs.baggage-details}}
      connect-to-live-agent: ${{steps.filter.outputs.connect-to-live-agent}}
      manage-contact-attributes: ${{steps.filter.outputs.manage-contact-attributes}}
      scc-middleware-multiagent-framework: ${{steps.filter.outputs.scc-middleware-multiagent-framework}}
      scc-middleware-webhook: ${{steps.filter.outputs.scc-middleware-webhook}}
      case-management: ${{steps.filter.outputs.case-management}}
      studio-api-wrapper: ${{steps.filter.outputs.studio-api-wrapper}}
      mock-api-wrapper: ${{steps.filter.outputs.mock-api-wrapper}}
      rozie-air: ${{steps.filter.outputs.rozie-air}}
      rozie-air-resources: ${{steps.filter.outputs.rozie-air-resources}}
      rozie-brook: ${{steps.filter.outputs.rozie-brook}}
      rozie-air-amadeus: ${{steps.filter.outputs.rozie-air-amadeus}}
      zurich-ocr-engine: ${{steps.filter.outputs.zurich-ocr-engine}}
      rozie-zurich-uc05: ${{steps.filter.outputs.rozie-zurich-uc05}}
    # For pull requests it's not necessary to checkout the code
    steps:
      - uses: actions/checkout@v2
      - uses: dorny/paths-filter@v2
        id: filter
        with:
          base: ${{ github.ref }}
          filters: |
            customer-profile:
              - 'common-APIs/customer-profile/**'
            flight-details:
              - 'rozie-airline/flight-details/**'
            flight-booking:
              - 'rozie-airline/flight-booking/**'
            customer-loyalty-program:
              - 'rozie-airline/customer-loyalty-program/**'
            PNR-details:
              - 'rozie-airline/PNR-details/**'
            appointment:
              - 'rozie-salon/appointment/**'
            email-sms-otp:
              - 'rozie-airline/email-sms-otp/**' 
            flight-schedule:
              - 'rozie-airline/flight-schedule/**'
            flight-util:
              - 'rozie-airline/flight-util/**'
            flight-status:
              - 'rozie-airline/flight-status/**'
            baggage-details:
              - 'rozie-airline/baggage-details/**'
            connect-to-live-agent:
              - 'common-APIs/connect-to-live-agent/**'
            manage-contact-attributes:
              - 'common-APIs/manage-contact-attributes/**'
            scc-middleware-multiagent-framework:
              - 'multi-agent-framework/scc-middleware-multiagent-framework/**'
            scc-middleware-webhook:
              - 'multi-agent-framework/scc-middleware-webhook/**'
            case-management:
              - 'case-management/**'
            studio-api-wrapper:
              - 'studio-api-wrapper/**'
            mock-api-wrapper:
              - 'rozie-airline/mock-api-wrapper/**'
            rozie-air:
              - 'rozie-air/**'
            rozie-air-resources:
              - 'rozie-air-resources/**'
            rozie-brook:
              - 'rozie-brook/**'
            rozie-air-amadeus:
              - 'rozie-air-amadeus/**'
            zurich-ocr-engine:
              - 'zurich-ocr-engine/**'
            rozie-zurich-uc05:
              - 'rozie-zurich-uc05/**'


  # JOB to build customer-profile
  customer-profile:
    needs: changes
    if: ${{ needs.changes.outputs.customer-profile == 'true' }}
    name: Build and Deploy customer-profile lambda
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v1
        with:
          node-version: "18.x"
      - name: Install Serverless Framework
        run: npm install -g serverless@3.38.0
      - name: Serverless AWS Authentication
        run: sls config credentials --provider aws --key ${{ secrets.AWS_ACCESS_KEY }} --secret ${{ secrets.AWS_SECRET_KEY }}
      - name: Deploy dev customer-profile API
        run: cd common-APIs/customer-profile && sls deploy --region ca-central-1 --stage dev 

  # JOB to build flight-details
  flight-details:
    needs: changes
    if: ${{ needs.changes.outputs.flight-details == 'true' }}
    name: Build and Deploy flight-details lambda
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v1
        with:
          node-version: "18.x"
      - name: Install Serverless Framework
        run: npm install -g serverless@3.38.0
      - name: Serverless AWS Authentication
        run: sls config credentials --provider aws --key ${{ secrets.AWS_ACCESS_KEY }} --secret ${{ secrets.AWS_SECRET_KEY }}
      - name: Deploy dev flight-details API
        run: cd rozie-airline/flight-details && sls deploy --region ca-central-1 --stage dev 

  # JOB to build flight-booking
  flight-booking:
    needs: changes
    if: ${{ needs.changes.outputs.flight-booking == 'true' }}
    name: Build and Deploy flight-booking lambda
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v1
        with:
          node-version: "18.x"
      - name: Install Serverless Framework
        run: npm install -g serverless@3.38.0
      - name: Serverless AWS Authentication
        run: sls config credentials --provider aws --key ${{ secrets.AWS_ACCESS_KEY }} --secret ${{ secrets.AWS_SECRET_KEY }}
      - name: Deploy dev flight-booking API
        run: cd rozie-airline/flight-booking && pip install requests -t . && sls deploy --region ca-central-1 --stage dev 

  # JOB to build customer-loyalty-program
  customer-loyalty-program:
    needs: changes
    if: ${{ needs.changes.outputs.customer-loyalty-program == 'true' }}
    name: Build and Deploy customer-loyalty-program lambda
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v1
        with:
          node-version: "18.x"
      - name: Install Serverless Framework
        run: npm install -g serverless@3.38.0
      - name: Serverless AWS Authentication
        run: sls config credentials --provider aws --key ${{ secrets.AWS_ACCESS_KEY }} --secret ${{ secrets.AWS_SECRET_KEY }}
      - name: Deploy dev customer-loyalty-program API
        run: cd rozie-airline/customer-loyalty-program && sls deploy --region ca-central-1 --stage dev 
  
  # JOB to build PNR-details
  PNR-details:
    needs: changes
    if: ${{ needs.changes.outputs.PNR-details == 'true' }}
    name: Build and Deploy PNR-details lambda
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v1
        with:
          node-version: "18.x"
      - name: Install Serverless Framework
        run: npm install -g serverless@3.38.0
      - name: Serverless AWS Authentication
        run: sls config credentials --provider aws --key ${{ secrets.AWS_ACCESS_KEY }} --secret ${{ secrets.AWS_SECRET_KEY }}
      - name: Deploy dev PNR-details API
        run: cd rozie-airline/PNR-details && sls deploy --region ca-central-1 --stage dev 
  
  # JOB to build appointment
  appointment:
    needs: changes
    if: ${{ needs.changes.outputs.appointment == 'true' }}
    name: Build and Deploy appointment lambda
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v1
        with:
          node-version: "18.x"
      - name: Install Serverless Framework
        run: npm install -g serverless@3.38.0
      - name: Serverless AWS Authentication
        run: sls config credentials --provider aws --key ${{ secrets.AWS_ACCESS_KEY }} --secret ${{ secrets.AWS_SECRET_KEY }}
      - name: Deploy dev appointment API
        run: cd rozie-salon/appointment && sls deploy --region ca-central-1 --stage dev

  # JOB to build email-sms-otp
  email-sms-otp:
    needs: changes
    if: ${{ needs.changes.outputs.email-sms-otp == 'true' }}
    name: Build and Deploy email-sms-otp lambda
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v1
        with:
          node-version: "18.x"
      - name: Install Serverless Framework
        run: npm install -g serverless@3.38.0
      - name: Serverless AWS Authentication
        run: sls config credentials --provider aws --key ${{ secrets.AWS_ACCESS_KEY }} --secret ${{ secrets.AWS_SECRET_KEY }}
      - name: Deploy dev email-sms-otp API
        run: cd rozie-airline/email-sms-otp && sls deploy --region ca-central-1 --stage dev

  # JOB to build flight-schedule
  flight-schedule:
    needs: changes
    if: ${{ needs.changes.outputs.flight-schedule == 'true' }}
    name: Build and Deploy flight-schedule lambda
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v1
        with:
          node-version: "18.x"
      - name: Install Serverless Framework
        run: npm install -g serverless@3.38.0
      - name: Serverless AWS Authentication
        run: sls config credentials --provider aws --key ${{ secrets.AWS_ACCESS_KEY }} --secret ${{ secrets.AWS_SECRET_KEY }}
      - name: Deploy dev flight-schedule API
        run: cd rozie-airline/flight-schedule && sls deploy --region ca-central-1 --stage dev 

  # JOB to build flight-util
  flight-util:
    needs: changes
    if: ${{ needs.changes.outputs.flight-util == 'true' }}
    name: Build and Deploy flight-util lambda
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v1
        with:
          node-version: "18.x"
      - name: Install Serverless Framework
        run: npm install -g serverless@3.38.0
      - name: Serverless AWS Authentication
        run: sls config credentials --provider aws --key ${{ secrets.AWS_ACCESS_KEY }} --secret ${{ secrets.AWS_SECRET_KEY }}
      - name: Deploy dev flight-util API
        run: cd rozie-airline/flight-util && sls deploy --region ca-central-1 --stage dev 

   # JOB to build flight-status
  flight-status:
    needs: changes
    if: ${{ needs.changes.outputs.flight-status == 'true' }}
    name: Build and Deploy flight-status lambda
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v1
        with:
          node-version: "18.x"
      - name: Install Serverless Framework
        run: npm install -g serverless@3.38.0
      - name: Serverless AWS Authentication
        run: sls config credentials --provider aws --key ${{ secrets.AWS_ACCESS_KEY }} --secret ${{ secrets.AWS_SECRET_KEY }}
      - name: Deploy dev flight-status API
        run: cd rozie-airline/flight-status && sls deploy --region ca-central-1 --stage dev 

   # JOB to build baggage-details
  baggage-details:
    needs: changes
    if: ${{ needs.changes.outputs.baggage-details == 'true' }}
    name: Build and Deploy baggage-details lambda
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v1
        with:
          node-version: "18.x"
      - name: Install Serverless Framework
        run: npm install -g serverless@3.38.0
      - name: Serverless AWS Authentication
        run: sls config credentials --provider aws --key ${{ secrets.AWS_ACCESS_KEY }} --secret ${{ secrets.AWS_SECRET_KEY }}
      - name: Deploy dev baggage-details API
        run: cd rozie-airline/baggage-details && sls deploy --region ca-central-1 --stage dev

  # JOB to build connect-to-live-agent
  connect-to-live-agent:
    needs: changes
    if: ${{ needs.changes.outputs.connect-to-live-agent == 'true' }}
    name: Build and Deploy connect-to-live-agent lambda
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v1
        with:
          node-version: "18.x"
      - name: Install Serverless Framework
        run: npm install -g serverless@3.38.0
      - name: Serverless AWS Authentication
        run: sls config credentials --provider aws --key ${{ secrets.AWS_ACCESS_KEY }} --secret ${{ secrets.AWS_SECRET_KEY }}
      - name: Deploy dev connect-to-live-agent API
        run: cd common-APIs/connect-to-live-agent && npm install && sls deploy --region ca-central-1 --stage dev

  # JOB to build manage-contact-attributes
  manage-contact-attributes:
    needs: changes
    if: ${{ needs.changes.outputs.manage-contact-attributes == 'true' }}
    name: Build and Deploy manage-contact-attributes lambda
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v1
        with:
          node-version: "18.x"
      - name: Install Serverless Framework
        run: npm install -g serverless@3.38.0
      - name: Serverless AWS Authentication
        run: sls config credentials --provider aws --key ${{ secrets.AWS_ACCESS_KEY }} --secret ${{ secrets.AWS_SECRET_KEY }}
      - name: Deploy dev manage-contact-attributes API
        run: cd common-APIs/manage-contact-attributes && npm install && sls deploy --region ca-central-1 --stage dev

  # JOB to build scc-middleware-multiagent-framework
  scc-middleware-multiagent-framework:
    needs: changes
    if: ${{ needs.changes.outputs.scc-middleware-multiagent-framework == 'true' }}
    name: Build and Deploy scc-middleware-multiagent-framework lambda
    runs-on: ubuntu-latest
    environment: dev
    env:
      environment: dev
      ecrrepository: scc-middleware-maf
      hosted-zone-id: ${{ secrets.HOSTED_ZONE_ID }}
      acm-cert-arn: ${{ secrets.ACM_CERT_ARN }}
      domain-name: middleware.dev-scc-demo.rozie.ai
      aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
      aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY }}
      aws-region: ${{ secrets.AWS_REGION }}
      IMAGE_TAG: v${{ github.sha }}
      api_key: Test@123
      OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
    steps:
      - uses: actions/checkout@v2
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1
      - name: Create ECR image for insights
        run: | 
          echo "$ecrrepository-$environment"
          aws ecr describe-repositories --repository-names $ecrrepository-$environment || aws ecr create-repository --registry-id ${{secrets.AWS_ACCOUNT_ID}} --repository-name $ecrrepository-$environment
      - name: Build, tag, and push ECR insights image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        run: |
          echo "$configtable"
          cd multi-agent-framework/scc-middleware-multiagent-framework
          docker build --build-arg FASTAPI_ENV=$environment \
            --build-arg AWS_REGION=$aws-region \
            --build-arg APP_API_KEY=$api_key \
            --build-arg MULTI_AGENT_ACCESS_TOKEN=$api_key \
            --build-arg OPENAI_API_KEY=$OPENAI_API_KEY \
            -t "$ECR_REGISTRY/$ecrrepository-$environment:$IMAGE_TAG" -f Dockerfile .
          docker push "$ECR_REGISTRY/$ecrrepository-$environment:$IMAGE_TAG"
          echo "image=$ECR_REGISTRY/$ecrrepository-$environment:$IMAGE_TAG" >> $GITHUB_OUTPUT 
      - uses: actions/setup-node@v1
        with:
          node-version: "18.x"
      - name: Install Serverless Framework
        run: npm install -g serverless@3.38.0
      - name: Serverless AWS Authentication
        run: sls config credentials --provider aws --key $aws-access-key-id --secret $aws-secret-access-key
      - name: Enable SLS debug mode
        run: export SLS_DEBUG=true
      - name: Deploy dev scc-middleware-multiagent-framework API
        env:
          ECR_REPO_URI : ${{ steps.build-image.outputs.image }}
        run: |
          echo "$ECR_REPO_URI"
          cd multi-agent-framework/scc-middleware-multiagent-framework && sls deploy --region ${{ env.aws-region }} --stage $environment -E ECR_REPO_URI=$ECR_REPO_URI -E hosted-zone-id=$hosted-zone-id -E acm-cert-arn=$acm-cert-arn -E domain-name=$domain-name 

  # JOB to build scc-middleware-webhook
  scc-middleware-webhook:
    needs: changes
    if: ${{ needs.changes.outputs.scc-middleware-webhook == 'true' }}
    name: Build and Deploy scc-middleware-webhook lambda
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v1
        with:
          node-version: "18.x"
      - name: Install Serverless Framework
        run: npm install -g serverless@3.38.0
      - name: Serverless AWS Authentication
        run: sls config credentials --provider aws --key ${{ secrets.AWS_ACCESS_KEY }} --secret ${{ secrets.AWS_SECRET_KEY }}
      - name: Deploy dev scc-middleware-webhook API
        run: cd multi-agent-framework/scc-middleware-webhook && sls deploy --region ca-central-1 --stage dev 

  # JOB to build case-management
  case-management:
    needs: changes
    if: ${{ needs.changes.outputs.case-management == 'true' }}
    name: Build and Deploy case-management lambda
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v1
        with:
          node-version: "18.x"
      - name: Install Serverless Framework
        run: npm install -g serverless@3.38.0
      - name: Serverless AWS Authentication
        run: sls config credentials --provider aws --key ${{ secrets.AWS_ACCESS_KEY }} --secret ${{ secrets.AWS_SECRET_KEY }}
      - name: Deploy dev case-management API
        run: cd case-management && sls deploy --region ca-central-1 --stage dev 

  # JOB to build studio-api-wrapper
  studio-api-wrapper:
    needs: changes
    if: ${{ needs.changes.outputs.studio-api-wrapper == 'true' }}
    name: Build and Deploy studio-api-wrapper lambda
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v1
        with:
          node-version: "18.x"
      - name: Install Serverless Framework
        run: npm install -g serverless@3.38.0
      - name: Serverless AWS Authentication
        run: sls config credentials --provider aws --key ${{ secrets.AWS_ACCESS_KEY }} --secret ${{ secrets.AWS_SECRET_KEY }}
      - name: Deploy dev studio-api-wrapper API
        run: cd studio-api-wrapper && sls deploy --region ca-central-1 --stage dev

  # JOB to build mock-api-wrapper
  mock-api-wrapper:
    needs: changes
    if: ${{ needs.changes.outputs.mock-api-wrapper == 'true' }}
    name: Build and Deploy mock-api-wrapper lambda
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v1
        with:
          node-version: "18.x"
      - name: Install Serverless Framework
        run: npm install -g serverless@3.38.0
      - name: Serverless AWS Authentication
        run: sls config credentials --provider aws --key ${{ secrets.AWS_ACCESS_KEY }} --secret ${{ secrets.AWS_SECRET_KEY }}
      - name: Deploy dev mock-api-wrapper API
        run: cd rozie-airline/mock-api-wrapper && npm i && sls deploy --region ca-central-1 --stage dev

  # JOB to build rozie-air
  rozie-air:
    needs: changes
    if: ${{ needs.changes.outputs.rozie-air == 'true' }}
    name: Build and Deploy rozie-air lambda
    runs-on: ubuntu-latest
    environment: dev
    env:
      environment: dev
      ecrrepository: rozie-air
      hosted-zone-id: ${{ secrets.HOSTED_ZONE_ID }}
      acm-cert-arn: ${{ secrets.ACM_CERT_ARN }}
      domain-name: rozie-air.dev-scc-demo.rozie.ai
      aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
      aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY }}
      aws-region: ${{ secrets.AWS_REGION }}
      IMAGE_TAG: v${{ github.sha }}
      api_key: Test@123
      OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
    steps:
      - uses: actions/checkout@v2
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1
      - name: Create ECR image for insights
        run: | 
          echo "$ecrrepository-$environment"
          aws ecr describe-repositories --repository-names $ecrrepository-$environment || aws ecr create-repository --registry-id ${{secrets.AWS_ACCOUNT_ID}} --repository-name $ecrrepository-$environment
      - name: Build, tag, and push ECR insights image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        run: |
          echo "$configtable"
          cd rozie-air
          docker build --build-arg FASTAPI_ENV=$environment \
            --build-arg AWS_REGION=$aws-region \
            --build-arg APP_API_KEY=$api_key \
            --build-arg MULTI_AGENT_ACCESS_TOKEN=$api_key \
            --build-arg OPENAI_API_KEY=$OPENAI_API_KEY \
            -t "$ECR_REGISTRY/$ecrrepository-$environment:$IMAGE_TAG" -f Dockerfile .
          docker push "$ECR_REGISTRY/$ecrrepository-$environment:$IMAGE_TAG"
          echo "image=$ECR_REGISTRY/$ecrrepository-$environment:$IMAGE_TAG" >> $GITHUB_OUTPUT 
      - uses: actions/setup-node@v1
        with:
          node-version: "18.x"
      - name: Install Serverless Framework
        run: npm install -g serverless@3.38.0
      - name: Serverless AWS Authentication
        run: sls config credentials --provider aws --key $aws-access-key-id --secret $aws-secret-access-key
      - name: Enable SLS debug mode
        run: export SLS_DEBUG=true
      - name: Deploy dev rozie-air API
        env:
          ECR_REPO_URI : ${{ steps.build-image.outputs.image }}
        run: |
          echo "$ECR_REPO_URI"
          cd rozie-air && sls deploy --region ${{ env.aws-region }} --stage $environment -E ECR_REPO_URI=$ECR_REPO_URI -E hosted-zone-id=$hosted-zone-id -E acm-cert-arn=$acm-cert-arn -E domain-name=$domain-name 

  # JOB to build rozie-air-resources
  rozie-air-resources:
    needs: changes
    if: ${{ needs.changes.outputs.rozie-air-resources == 'true' }}
    name: Build and Deploy rozie-air-resources lambda
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v1
        with:
          node-version: "18.x"
      - name: Install Serverless Framework
        run: npm install -g serverless@3.38.0
      - name: Serverless AWS Authentication
        run: sls config credentials --provider aws --key ${{ secrets.AWS_ACCESS_KEY }} --secret ${{ secrets.AWS_SECRET_KEY }}
      - name: Deploy dev rozie-air-resources API
        run: cd rozie-air-resources && sls deploy --region ca-central-1 --stage dev 

 # JOB to build rozie-brook
  rozie-brook:
    needs: changes
    if: ${{ needs.changes.outputs.rozie-brook == 'true' }}
    name: Build and Deploy rozie-brook lambda
    runs-on: ubuntu-latest
    environment: dev
    env:
      environment: dev
      ecrrepository: rozie-brook
      hosted-zone-id: ${{ secrets.HOSTED_ZONE_ID }}
      acm-cert-arn: ${{ secrets.ACM_CERT_ARN }}
      domain-name: rozie-brook.dev-scc-demo.rozie.ai
      aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
      aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY }}
      aws-region: ${{ secrets.AWS_REGION }}
      IMAGE_TAG: v${{ github.sha }}
      api_key: Test@123
    steps:
      - uses: actions/checkout@v2
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1
      - name: Create ECR image for insights
        run: | 
          echo "$ecrrepository-$environment"
          aws ecr describe-repositories --repository-names $ecrrepository-$environment || aws ecr create-repository --registry-id ${{secrets.AWS_ACCOUNT_ID}} --repository-name $ecrrepository-$environment
      - name: Build, tag, and push ECR insights image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        run: |
          echo "$configtable"
          cd rozie-brook
          docker build --build-arg FASTAPI_ENV=$environment \
            --build-arg AWS_REGION=$aws-region \
            -t "$ECR_REGISTRY/$ecrrepository-$environment:$IMAGE_TAG" -f Dockerfile .
          docker push "$ECR_REGISTRY/$ecrrepository-$environment:$IMAGE_TAG"
          echo "image=$ECR_REGISTRY/$ecrrepository-$environment:$IMAGE_TAG" >> $GITHUB_OUTPUT 
      - uses: actions/setup-node@v1
        with:
          node-version: "18.x"
      - name: Install Serverless Framework
        run: npm install -g serverless@3.38.0
      - name: Serverless AWS Authentication
        run: sls config credentials --provider aws --key $aws-access-key-id --secret $aws-secret-access-key
      - name: Enable SLS debug mode
        run: export SLS_DEBUG=true
      - name: Deploy dev rozie-brook API
        env:
          ECR_REPO_URI : ${{ steps.build-image.outputs.image }}
        run: |
          echo "$ECR_REPO_URI"
          cd rozie-brook && sls deploy --region ${{ env.aws-region }} --stage $environment -E ECR_REPO_URI=$ECR_REPO_URI

  # JOB to build rozie-air-amadeus
  rozie-air-amadeus:
    needs: changes
    if: ${{ needs.changes.outputs.rozie-air-amadeus == 'true' }}
    name: Build and Deploy rozie-air-amadeus lambda
    runs-on: ubuntu-latest
    environment: dev
    env:
      environment: dev
      ecrrepository: rozie-air-amadeus
      hosted-zone-id: ${{ secrets.HOSTED_ZONE_ID }}
      acm-cert-arn: ${{ secrets.ACM_CERT_ARN }}
      domain-name: rozie-amadeus.dev-scc-demo.rozie.ai
      aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
      aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY }}
      aws-region: ${{ secrets.AWS_REGION }}
      IMAGE_TAG: v${{ github.sha }}
      api_key: Test@123
    steps:
      - uses: actions/checkout@v2
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1
      - name: Create ECR image for rozie-amadeus
        run: | 
          echo "$ecrrepository-$environment"
          aws ecr describe-repositories --repository-names $ecrrepository-$environment || aws ecr create-repository --registry-id ${{secrets.AWS_ACCOUNT_ID}} --repository-name $ecrrepository-$environment
      - name: Build, tag, and push ECR image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        run: |
          cd rozie-air-amadeus
          docker build --build-arg FASTAPI_ENV=$environment \
            --build-arg AWS_REGION=$aws-region \
            --build-arg APP_API_KEY=$api_key \
            -t "$ECR_REGISTRY/$ecrrepository-$environment:$IMAGE_TAG" -f Dockerfile .
          docker push "$ECR_REGISTRY/$ecrrepository-$environment:$IMAGE_TAG"
          echo "image=$ECR_REGISTRY/$ecrrepository-$environment:$IMAGE_TAG" >> $GITHUB_OUTPUT 
      - uses: actions/setup-node@v1
        with:
          node-version: "18.x"
      - name: Install Serverless Framework
        run: npm install -g serverless@3.38.0
      - name: Serverless AWS Authentication
        run: sls config credentials --provider aws --key $aws-access-key-id --secret $aws-secret-access-key
      - name: Enable SLS debug mode
        run: export SLS_DEBUG=true
      - name: Deploy dev rozie-air-amadeus API
        env:
          ECR_REPO_URI : ${{ steps.build-image.outputs.image }}
        run: |
          echo "$ECR_REPO_URI"
          cd rozie-air-amadeus && sls deploy --region ${{ env.aws-region }} --stage $environment -E ECR_REPO_URI=$ECR_REPO_URI -E hosted-zone-id=$hosted-zone-id -E acm-cert-arn=$acm-cert-arn -E domain-name=$domain-name 

  # JOB to build zurich-ocr-engine
  zurich-ocr-engine:
    needs: changes
    if: ${{ needs.changes.outputs.zurich-ocr-engine == 'true' }}
    name: Build and Deploy zurich-ocr-engine
    runs-on: ubuntu-latest
    environment: dev
    env:
      environment: dev
      ecrrepository: zurich-ocr-engine
      hosted-zone-id: ${{ secrets.HOSTED_ZONE_ID }}
      acm-cert-arn: ${{ secrets.ACM_CERT_ARN }}
      domain-name: zurich-ocr.dev-scc-demo.rozie.ai
      aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
      aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY }}
      aws-region: ${{ secrets.AWS_REGION }}
      IMAGE_TAG: v${{ github.sha }}
      OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
      GOOGLE_PROJECT_ID: ${{ secrets.GOOGLE_PROJECT_ID }}
      GOOGLE_CREDENTIALS_JSON: ${{ secrets.GOOGLE_CREDENTIALS_JSON }}
    steps:
      - uses: actions/checkout@v2
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1
      - name: Create ECR repository for zurich-ocr-engine
        run: |
          echo "$ecrrepository-$environment"
          aws ecr describe-repositories --repository-names $ecrrepository-$environment || aws ecr create-repository --registry-id ${{secrets.AWS_ACCOUNT_ID}} --repository-name $ecrrepository-$environment
      - name: Build, tag, and push ECR image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        run: |
          cd zurich-ocr-engine
          docker build --build-arg FASTAPI_ENV=$environment \
            --build-arg AWS_REGION=$aws-region \
            --build-arg OPENAI_API_KEY=$OPENAI_API_KEY \
            --build-arg GOOGLE_PROJECT_ID=$GOOGLE_PROJECT_ID \
            --build-arg GOOGLE_CREDENTIALS_JSON="$GOOGLE_CREDENTIALS_JSON" \
            -t "$ECR_REGISTRY/$ecrrepository-$environment:$IMAGE_TAG" -f Dockerfile .
          docker push "$ECR_REGISTRY/$ecrrepository-$environment:$IMAGE_TAG"
          echo "image=$ECR_REGISTRY/$ecrrepository-$environment:$IMAGE_TAG" >> $GITHUB_OUTPUT
      - uses: actions/setup-node@v1
        with:
          node-version: "18.x"
      - name: Install Serverless Framework
        run: npm install -g serverless@3.38.0
      - name: Serverless AWS Authentication
        run: sls config credentials --provider aws --key $aws-access-key-id --secret $aws-secret-access-key
      - name: Enable SLS debug mode
        run: export SLS_DEBUG=true
      - name: Deploy dev zurich-ocr-engine API
        env:
          ECR_REPO_URI : ${{ steps.build-image.outputs.image }}
        run: |
          echo "$ECR_REPO_URI"
          cd zurich-ocr-engine && sls deploy --region ${{ env.aws-region }} --stage $environment -E ECR_REPO_URI=$ECR_REPO_URI -E hosted-zone-id=$hosted-zone-id -E acm-cert-arn=$acm-cert-arn -E domain-name=$domain-name

  # JOB to build rozie-zurich-uc05
  rozie-zurich-uc05:
    needs: changes
    if: ${{ needs.changes.outputs.rozie-zurich-uc05 == 'true' }}
    name: Build and Deploy rozie-zurich-uc05 ECS services
    runs-on: ubuntu-latest
    environment: dev
    env:
      environment: dev
      ecrrepository: rozieai-zrh-uc05
      hosted-zone-id: ${{ secrets.HOSTED_ZONE_ID }}
      acm-cert-arn: ${{ secrets.ACM_CERT_ARN }}
      domain-name: rozieai-zrh-uc05.dev-scc-demo.rozie.ai
      aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
      aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY }}
      aws-region: ${{ secrets.AWS_REGION }}
      IMAGE_TAG: v${{ github.sha }}
      api_key: Test@123
    steps:
      - uses: actions/checkout@v2
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1
      - name: Create ECR repositories for all services
        run: |
          echo "Creating ECR repositories for all services..."
          aws ecr describe-repositories --repository-names $ecrrepository-backend-$environment || aws ecr create-repository --registry-id ${{secrets.AWS_ACCOUNT_ID}} --repository-name $ecrrepository-backend-$environment
          aws ecr describe-repositories --repository-names $ecrrepository-frontend-$environment || aws ecr create-repository --registry-id ${{secrets.AWS_ACCOUNT_ID}} --repository-name $ecrrepository-frontend-$environment
          aws ecr describe-repositories --repository-names $ecrrepository-dashboard-$environment || aws ecr create-repository --registry-id ${{secrets.AWS_ACCOUNT_ID}} --repository-name $ecrrepository-dashboard-$environment
          aws ecr describe-repositories --repository-names $ecrrepository-n8n-$environment || aws ecr create-repository --registry-id ${{secrets.AWS_ACCOUNT_ID}} --repository-name $ecrrepository-n8n-$environment
      - name: Build, tag, and push backend image to Amazon ECR
        id: build-backend-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        run: |
          cd rozie-zurich-uc05/zurich-workflow-app
          docker build \
            -t "$ECR_REGISTRY/$ecrrepository-backend-$environment:$IMAGE_TAG" -f Dockerfile .
          docker push "$ECR_REGISTRY/$ecrrepository-backend-$environment:$IMAGE_TAG"
          echo "image=$ECR_REGISTRY/$ecrrepository-backend-$environment:$IMAGE_TAG" >> $GITHUB_OUTPUT
      - name: Build, tag, and push frontend image to Amazon ECR
        id: build-frontend-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        run: |
          cd rozie-zurich-uc05/zurich-workflow-app
          docker build \
            -t "$ECR_REGISTRY/$ecrrepository-frontend-$environment:$IMAGE_TAG" -f frontend.Dockerfile .
          docker push "$ECR_REGISTRY/$ecrrepository-frontend-$environment:$IMAGE_TAG"
          echo "image=$ECR_REGISTRY/$ecrrepository-frontend-$environment:$IMAGE_TAG" >> $GITHUB_OUTPUT
      - name: Build, tag, and push dashboard image to Amazon ECR
        id: build-dashboard-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        run: |
          cd rozie-zurich-uc05/zurich-workflow-app/zurich-dashboard
          docker build \
            -t "$ECR_REGISTRY/$ecrrepository-dashboard-$environment:$IMAGE_TAG" -f Dockerfile .
          docker push "$ECR_REGISTRY/$ecrrepository-dashboard-$environment:$IMAGE_TAG"
          echo "image=$ECR_REGISTRY/$ecrrepository-dashboard-$environment:$IMAGE_TAG" >> $GITHUB_OUTPUT
      - name: Build, tag, and push N8N image to Amazon ECR
        id: build-n8n-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        run: |
          cd rozie-zurich-uc05/zurich-workflow-app
          docker build \
            -t "$ECR_REGISTRY/$ecrrepository-n8n-$environment:$IMAGE_TAG" -f n8n.Dockerfile .
          docker push "$ECR_REGISTRY/$ecrrepository-n8n-$environment:$IMAGE_TAG"
          echo "image=$ECR_REGISTRY/$ecrrepository-n8n-$environment:$IMAGE_TAG" >> $GITHUB_OUTPUT
      - uses: actions/setup-node@v1
        with:
          node-version: "18.x"
      - name: Install Serverless Framework
        run: npm install -g serverless@3.38.0
      - name: Serverless AWS Authentication
        run: sls config credentials --provider aws --key $aws-access-key-id --secret $aws-secret-access-key
      - name: Enable SLS debug mode
        run: export SLS_DEBUG=true
      - name: Deploy dev rozie-zurich-uc05 infrastructure
        env:
          ECR_REPO_URI : ${{ steps.build-backend-image.outputs.image }}
          ECR_REPO_URI_FRONTEND : ${{ steps.build-frontend-image.outputs.image }}
          ECR_REPO_URI_DASHBOARD : ${{ steps.build-dashboard-image.outputs.image }}
          ECR_REPO_URI_N8N : ${{ steps.build-n8n-image.outputs.image }}
        run: |
          echo "Backend: $ECR_REPO_URI"
          echo "Frontend: $ECR_REPO_URI_FRONTEND"
          echo "Dashboard: $ECR_REPO_URI_DASHBOARD"
          echo "N8N: $ECR_REPO_URI_N8N"
          cd rozie-zurich-uc05 && sls deploy --region ca-central-1 --stage dev


