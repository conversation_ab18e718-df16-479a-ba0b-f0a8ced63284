name: Test Zurich Workflows

on:
  workflow_dispatch:
    inputs:
      test_message:
        description: 'Test message to display'
        required: false
        default: 'Testing Zurich workflows'
        type: string
  push:
    branches:
      - zurich/dinesh/zurich_workflow_uc05
      - zurich/dinesh/zurich_workflow_uc05_dev
      - main
      - master
      - development
    paths:
      - '.github/workflows/**'

jobs:
  test-workflows:
    name: Test Workflow Visibility
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v2
    
    - name: Display test message
      run: |
        echo "🎉 Zurich Workflows Test"
        echo "======================="
        echo "Message: ${{ github.event.inputs.test_message || 'Automatic trigger' }}"
        echo "Branch: ${{ github.ref_name }}"
        echo "Commit: ${{ github.sha }}"
        echo ""
        echo "✅ If you can see this, all Zurich workflows should be visible!"
        echo ""
        echo "Available Workflows:"
        echo "- Deploy All Zurich Components"
        echo "- Deploy Zurich Frontend"
        echo "- Deploy Zurich Backend" 
        echo "- Deploy Zurich Dashboard"
        echo "- Deploy Zurich Infrastructure"
        echo "- Deploy N8N Standalone"
        echo ""
        echo "🚀 Ready to deploy Zurich services!"

    - name: List workflow files
      run: |
        echo "📋 Workflow files in repository:"
        ls -la .github/workflows/
        echo ""
        echo "🔍 Zurich-specific workflows:"
        ls -la .github/workflows/deploy-zurich* .github/workflows/deploy-n8n* || echo "No Zurich workflows found"

    - name: Check repository structure
      run: |
        echo "📁 Repository structure:"
        echo "rozie-zurich-uc05 directory:"
        ls -la rozie-zurich-uc05/ || echo "rozie-zurich-uc05 directory not found"
        echo ""
        echo "zurich-workflow-app directory:"
        ls -la rozie-zurich-uc05/zurich-workflow-app/ || echo "zurich-workflow-app directory not found"
        echo ""
        echo "n8n-standalone directory:"
        ls -la rozie-zurich-uc05/n8n-standalone/ || echo "n8n-standalone directory not found"
