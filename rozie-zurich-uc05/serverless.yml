service: ${self:custom.serviceName}

provider:
  name: aws
  runtime: nodejs14.x
  stage: ${opt:stage, "dev"}
  region: ${opt:region, "ca-central-1"}
  iamRoleStatements:
    - Effect: "Allow"
      Action:
        - ec2:RunInstances
        - ec2:DescribeInstances
        - ec2:TerminateInstances
        - ec2:StartInstances
        - ec2:StopInstances
        - ecs:CreateCluster
        - ecs:DescribeClusters
        - ecs:CreateService
        - ecs:UpdateService
        - ecs:DescribeServices
        - ecs:RunTask
        - ecs:StopTask
        - ecs:DeleteCluster
        - ecs:DeleteService
        - secretsmanager:GetSecretValue
        - cloudformation:CreateStack
        - cloudformation:UpdateStack
        - cloudformation:DescribeStacks
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - execute-api:Invoke
        - ec2:CreateFlowLogs
        - ec2:DescribeFlowLogs
        - ec2:*
      Resource: "*"

custom:
  serviceName: rozieai-zrh-uc05
  ecrRepoUri: ${env:ECR_REPO_URI}
  domainName: ${env:domain-name}
  awsRegion: ${env:AWS_REGION}
  publicHostedZoneID: ${env:hosted-zone-id}
  acmCertificateArn: ${env:acm-cert-arn}

resources:
  Resources:
    ECSCluster:
      Type: AWS::ECS::Cluster
      Properties:
        ClusterName: ${self:service}-${self:provider.stage}-cluster
        CapacityProviders:
          - FARGATE
        DefaultCapacityProviderStrategy:
          - CapacityProvider: FARGATE
            Weight: 1

    ECRECSExecutionRole:
      Type: AWS::IAM::Role
      Properties:
        RoleName: ${self:service}-${self:provider.stage}-ecs-execution
        AssumeRolePolicyDocument:
          Version: "2012-10-17"
          Statement:
            - Effect: Allow
              Principal:
                Service: ecs-tasks.amazonaws.com
              Action: sts:AssumeRole
        Policies:
          - PolicyName: ${self:service}-${self:provider.stage}-policy
            PolicyDocument:
              Version: "2012-10-17"
              Statement:
                - Effect: Allow
                  Action:
                    - logs:CreateLogGroup
                    - logs:CreateLogStream
                    - logs:PutLogEvents
                    - dynamodb:*
                  Resource: "*"
                - Effect: Allow
                  Action:
                    - ecr:GetDownloadUrlForLayer
                  Resource: "*"
                - Effect: Allow
                  Action:
                    - ecr:GetAuthorizationToken
                  Resource: "*"
                - Effect: Allow
                  Action:
                    - ecr:InitiateLayerUpload
                  Resource: "*"
                - Effect: Allow
                  Action:
                    - ecr:BatchGetImage
                  Resource: "*"
                - Effect: Allow
                  Action: ec2:*
                  Resource: "*"
                - Effect: Allow
                  Action: ecs:*
                  Resource: "*"
                - Effect: Allow
                  Action: vpc:*
                  Resource: "*"
                - Effect: Allow
                  Action: ec2:AmazonEC2ContainerRegistryFullAccess
                  Resource: "*"
                - Effect: Allow
                  Action: ec2:AmazonElasticContainerRegistryPublicFullAccess
                  Resource: "*"
                - Effect: Allow
                  Action:
                    - execute-api:Invoke
                  Resource: "*"
                - Effect: Allow
                  Action:
                    - secretsmanager:GetSecretValue
                  Resource: "*"

    CloudWatchLogsGroup:
      Type: AWS::Logs::LogGroup
      Properties:
        LogGroupName: "${self:service}-${self:provider.stage}"
        RetentionInDays: 30

    VPC:
      Type: AWS::EC2::VPC
      DependsOn: InternetGateway
      Properties:
        CidrBlock: 10.0.0.0/16
        EnableDnsSupport: true
        EnableDnsHostnames: true

    AZ1PublicSubnet1:
      Type: AWS::EC2::Subnet
      Properties:
        AvailabilityZone:
          Fn::Select:
            - 0
            - Fn::GetAZs: { Ref: "AWS::Region" }
        VpcId: !Ref "VPC"
        CidrBlock: 10.0.0.0/18
        MapPublicIpOnLaunch: true

    AZ2PublicSubnet1:
      Type: AWS::EC2::Subnet
      Properties:
        AvailabilityZone:
          Fn::Select:
            - 1
            - Fn::GetAZs: { Ref: "AWS::Region" }
        VpcId: !Ref "VPC"
        CidrBlock: *********/18
        MapPublicIpOnLaunch: true

    AZ1PrivateSubnet1:
      Type: AWS::EC2::Subnet
      Properties:
        AvailabilityZone:
          Fn::Select:
            - 0
            - Fn::GetAZs: { Ref: "AWS::Region" }
        VpcId: !Ref "VPC"
        CidrBlock: **********/18
        MapPublicIpOnLaunch: false

    AZ2PrivateSubnet1:
      Type: AWS::EC2::Subnet
      Properties:
        AvailabilityZone:
          Fn::Select:
            - 1
            - Fn::GetAZs: { Ref: "AWS::Region" }
        VpcId: !Ref "VPC"
        CidrBlock: **********/18
        MapPublicIpOnLaunch: false

    InternetGateway:
      Type: AWS::EC2::InternetGateway

    GatewayAttachement:
      Type: AWS::EC2::VPCGatewayAttachment
      Properties:
        VpcId: !Ref "VPC"
        InternetGatewayId: !Ref "InternetGateway"

    NatGatewayEIP:
      Type: AWS::EC2::EIP
      DependsOn: GatewayAttachement
      Properties:
        Domain: vpc

    NatGateway:
      Type: AWS::EC2::NatGateway
      Properties:
        AllocationId: !GetAtt NatGatewayEIP.AllocationId
        SubnetId: !Ref AZ1PublicSubnet1

    PublicRouteTable:
      Type: AWS::EC2::RouteTable
      Properties:
        VpcId: !Ref "VPC"

    RouteToInternet:
      Type: AWS::EC2::Route
      DependsOn: GatewayAttachement
      Properties:
        RouteTableId: !Ref "PublicRouteTable"
        DestinationCidrBlock: "0.0.0.0/0"
        GatewayId: !Ref "InternetGateway"

    PrivateRouteTable:
      Type: AWS::EC2::RouteTable
      Properties:
        VpcId: !Ref "VPC"

    PrivateRouteToNATGW:
      Type: AWS::EC2::Route
      Properties:
        RouteTableId: !Ref PrivateRouteTable
        DestinationCidrBlock: 0.0.0.0/0
        NatGatewayId: !Ref NatGateway

    AZ1PublicSubnet1RouteTableAssociation:
      Type: AWS::EC2::SubnetRouteTableAssociation
      Properties:
        SubnetId: !Ref AZ1PublicSubnet1
        RouteTableId: !Ref PublicRouteTable

    AZ2PublicSubnet1RouteTableAssociation:
      Type: AWS::EC2::SubnetRouteTableAssociation
      Properties:
        SubnetId: !Ref AZ2PublicSubnet1
        RouteTableId: !Ref PublicRouteTable

    AZ1PrivateSubnet1RouteTableAssociation:
      Type: AWS::EC2::SubnetRouteTableAssociation
      Properties:
        SubnetId: !Ref AZ1PrivateSubnet1
        RouteTableId: !Ref PrivateRouteTable

    AZ2PrivateSubnet1RouteTableAssociation:
      Type: AWS::EC2::SubnetRouteTableAssociation
      Properties:
        SubnetId: !Ref AZ2PrivateSubnet1
        RouteTableId: !Ref PrivateRouteTable

    InstanceSecurityGroup:
      Type: AWS::EC2::SecurityGroup
      Properties:
        GroupDescription: Allow HTTP and HTTPS traffic
        GroupName: ${self:service}-${self:provider.stage}-sg
        VpcId: !Ref VPC
        SecurityGroupIngress:
          - IpProtocol: -1
            CidrIp: 0.0.0.0/0
        SecurityGroupEgress:
          - IpProtocol: -1
            CidrIp: 0.0.0.0/0

    ApplicationLoadBalancer:
      Type: AWS::ElasticLoadBalancingV2::LoadBalancer
      Properties:
        Name: "${self:service}-${self:provider.stage}-alb"
        Type: "application"
        Scheme: "internet-facing"
        Subnets:
          - !Ref AZ1PublicSubnet1
          - !Ref AZ2PublicSubnet1
        SecurityGroups:
          - !Ref InstanceSecurityGroup
        LoadBalancerAttributes:
          - Key: "idle_timeout.timeout_seconds"
            Value: 4000

    LoadBalancerListener:
      Type: AWS::ElasticLoadBalancingV2::Listener
      DependsOn:
        - ApplicationLoadBalancer
      Properties:
        LoadBalancerArn: !Ref ApplicationLoadBalancer
        Protocol: HTTPS
        Port: 443
        SslPolicy: "ELBSecurityPolicy-TLS13-1-2-2021-06"
        Certificates:
          - CertificateArn: ${self:custom.acmCertificateArn}
        DefaultActions:
          - Type: forward
            TargetGroupArn: !Ref ElsaTargetGroup

    LoadBalancerListenerForHTTP:
      Type: AWS::ElasticLoadBalancingV2::Listener
      DependsOn:
        - ApplicationLoadBalancer
        - LoadBalancerListener
      Properties:
        LoadBalancerArn: !Ref ApplicationLoadBalancer
        Protocol: HTTP
        Port: 80
        DefaultActions:
          - Type: forward
            TargetGroupArn: !Ref ElsaTargetGroup

    ElsaTargetGroup:
      Type: AWS::ElasticLoadBalancingV2::TargetGroup
      DependsOn: ApplicationLoadBalancer
      Properties:
        Name: "${self:service}-${self:provider.stage}-tg"
        Port: 80
        Protocol: HTTP
        VpcId: !Ref VPC
        HealthCheckEnabled: true
        HealthCheckIntervalSeconds: 60
        HealthCheckPath: "/readiness"
        HealthCheckPort: "traffic-port"
        HealthCheckProtocol: HTTP
        HealthCheckTimeoutSeconds: 5
        HealthyThresholdCount: 2
        UnhealthyThresholdCount: 2
        IpAddressType: "ipv4"
        ProtocolVersion: "HTTP1"
        Matcher:
          HttpCode: "200"
        TargetType: "ip"



    TaskDefinition:
      Type: AWS::ECS::TaskDefinition
      DependsOn:
        - CloudWatchLogsGroup
      Properties:
        Family: ${self:service}-${self:provider.stage}
        NetworkMode: awsvpc
        RuntimePlatform:
          CpuArchitecture: X86_64
          OperatingSystemFamily: LINUX
        RequiresCompatibilities:
          - FARGATE
        ExecutionRoleArn: !GetAtt ECRECSExecutionRole.Arn
        TaskRoleArn: !GetAtt ECRECSExecutionRole.Arn
        Cpu: 2048
        Memory: 4096
        ContainerDefinitions:
          - Name: ${self:service}-nginx-${self:provider.stage}
            Image: ${env:ECR_REPO_URI_NGINX}
            Memory: 256
            Cpu: 128
            Essential: true
            PortMappings:
              - ContainerPort: 80
                Protocol: "tcp"
            LogConfiguration:
              LogDriver: awslogs
              Options:
                awslogs-group: "${self:service}-${self:provider.stage}"
                awslogs-region: !Ref AWS::Region
                awslogs-stream-prefix: "nginx-${self:service}-${self:provider.stage}"
          - Name: ${self:service}-backend-${self:provider.stage}
            Image: ${self:custom.ecrRepoUri}
            Memory: 2048
            Cpu: 1024
            Essential: true
            PortMappings:
              - ContainerPort: 8000
                Protocol: "tcp"
            Environment:
              - Name: OPENAI_API_KEY
                Value: ${ssm:/zurich-workflow/openai-api-key}
              - Name: SUPABASE_URL
                Value: ${ssm:/zurich-workflow/supabase-url}
              - Name: SUPABASE_ANON_KEY
                Value: ${ssm:/zurich-workflow/supabase-anon-key}
              - Name: SUPABASE_SERVICE_ROLE_KEY
                Value: ${ssm:/zurich-workflow/supabase-service-role-key~true}
              - Name: SUPPORT_SUBDOMAIN
                Value: ${ssm:/zurich-workflow/support-subdomain}
              - Name: SUPPORT_EMAIL
                Value: ${ssm:/zurich-workflow/support-email}
              - Name: SUPPORT_TOKEN
                Value: ${ssm:/zurich-workflow/support-token~true}
              - Name: BAML_ENVIRONMENT
                Value: development
              - Name: FASTAPI_ENV
                Value: production
              - Name: LOG_LEVEL
                Value: INFO
            LogConfiguration:
              LogDriver: awslogs
              Options:
                awslogs-group: "${self:service}-${self:provider.stage}"
                awslogs-region: !Ref AWS::Region
                awslogs-stream-prefix: "backend-${self:service}-${self:provider.stage}"
          - Name: ${self:service}-frontend-${self:provider.stage}
            Image: ${env:ECR_REPO_URI_FRONTEND}
            Memory: 512
            Cpu: 256
            Essential: true
            PortMappings:
              - ContainerPort: 3000
                Protocol: "tcp"
            LogConfiguration:
              LogDriver: awslogs
              Options:
                awslogs-group: "${self:service}-${self:provider.stage}"
                awslogs-region: !Ref AWS::Region
                awslogs-stream-prefix: "frontend-${self:service}-${self:provider.stage}"
          - Name: ${self:service}-dashboard-${self:provider.stage}
            Image: ${env:ECR_REPO_URI_DASHBOARD}
            Memory: 512
            Cpu: 256
            Essential: true
            PortMappings:
              - ContainerPort: 2000
                Protocol: "tcp"
            LogConfiguration:
              LogDriver: awslogs
              Options:
                awslogs-group: "${self:service}-${self:provider.stage}"
                awslogs-region: !Ref AWS::Region
                awslogs-stream-prefix: "dashboard-${self:service}-${self:provider.stage}"
          - Name: ${self:service}-n8n-${self:provider.stage}
            Image: ${env:ECR_REPO_URI_N8N}
            Memory: 768
            Cpu: 384
            Essential: true
            PortMappings:
              - ContainerPort: 5678
                Protocol: "tcp"
            Environment:
              - Name: N8N_HOST
                Value: "0.0.0.0"
              - Name: N8N_PORT
                Value: "5678"
              - Name: N8N_PROTOCOL
                Value: "http"
            LogConfiguration:
              LogDriver: awslogs
              Options:
                awslogs-group: "${self:service}-${self:provider.stage}"
                awslogs-region: !Ref AWS::Region
                awslogs-stream-prefix: "n8n-${self:service}-${self:provider.stage}"

    EcrEcsSlsTask:
      Type: AWS::ECS::Service
      DependsOn:
        - TaskDefinition
        - ElsaTargetGroup
        - ApplicationLoadBalancer
        - LoadBalancerListener
      Properties:
        Cluster: !Ref ECSCluster
        DesiredCount: 1
        TaskDefinition: !Ref TaskDefinition
        ServiceName: ${self:service}-${self:provider.stage}-service
        PlatformVersion: LATEST
        EnableECSManagedTags: true
        DeploymentConfiguration:
          MaximumPercent: 200
          MinimumHealthyPercent: 70
          Alarms:
            AlarmNames:
              - "${self:service}-${self:provider.stage}-service-alarm"
            Enable: true
            Rollback: true
          DeploymentCircuitBreaker:
            Enable: false
            Rollback: false
        NetworkConfiguration:
          AwsvpcConfiguration:
            SecurityGroups:
              - !Ref InstanceSecurityGroup
            Subnets:
              - !Ref AZ1PrivateSubnet1
              - !Ref AZ2PrivateSubnet1
        CapacityProviderStrategy:
          - CapacityProvider: FARGATE
            Weight: 1
        LoadBalancers:
          - ContainerName: ${self:service}-nginx-${self:provider.stage}
            ContainerPort: 80
            TargetGroupArn: !Ref ElsaTargetGroup

    APIRecordSet:
      Type: AWS::Route53::RecordSet
      Properties:
        HostedZoneId: ${self:custom.publicHostedZoneID}
        Name: ${self:custom.domainName}
        Type: A
        AliasTarget:
          DNSName: !GetAtt 'ApplicationLoadBalancer.DNSName'
          HostedZoneId: !GetAtt 'ApplicationLoadBalancer.CanonicalHostedZoneID'
          EvaluateTargetHealth: false


    SecurityGroupForEndpoints:
      Type: 'AWS::EC2::SecurityGroup'
      Properties:
        GroupDescription: 'Allow HTTPS traffic from the VPC'
        VpcId: !Ref VPC
        SecurityGroupIngress:
          - IpProtocol: tcp
            FromPort: 443
            ToPort: 443
            CidrIp: !GetAtt VPC.CidrBlock

    ECRECSInterfaceEndpoint: &ECRECSInterfaceEndpoint
      Type: 'AWS::EC2::VPCEndpoint'
      Properties: &ECRECSInterfaceEndpointProperties
        VpcEndpointType: 'Interface'
        VpcId: !Ref VPC
        PrivateDnsEnabled: true
        SubnetIds:
          - !Ref AZ1PrivateSubnet1
          - !Ref AZ2PrivateSubnet1
        SecurityGroupIds:
          - !Ref SecurityGroupForEndpoints
        ServiceName: !Sub 'com.amazonaws.${AWS::Region}.ecr.api'

    ECRDKRInterfaceEndpoint:
      <<: *ECRECSInterfaceEndpoint
      Properties:
        <<: *ECRECSInterfaceEndpointProperties
        ServiceName: !Sub 'com.amazonaws.${AWS::Region}.ecr.dkr'

    CWLInterfaceEndpoint:
      <<: *ECRECSInterfaceEndpoint
      Properties:
        <<: *ECRECSInterfaceEndpointProperties
        ServiceName: !Sub 'com.amazonaws.${AWS::Region}.logs'

    S3GatewayEndpoint:
      Type: 'AWS::EC2::VPCEndpoint'
      Properties:
        VpcEndpointType: 'Gateway'
        VpcId: !Ref VPC
        ServiceName: !Sub 'com.amazonaws.${AWS::Region}.s3'
        PolicyDocument:
          Version: "2012-10-17"
          Statement:
            - Sid: "Allow-access-to-specific-bucket"
              Effect: "Allow"
              Principal: "*"
              Action:
                - "*"
              Resource:
                - "*"
        RouteTableIds:
          - !Ref PrivateRouteTable
