service: ${self:custom.serviceName}

provider:
  name: aws
  runtime: nodejs14.x
  stage: ${opt:stage, "dev"}
  region: ${opt:region, "ca-central-1"}
  iamRoleStatements:
    - Effect: "Allow"
      Action:
        - ec2:RunInstances
        - ec2:DescribeInstances
        - ec2:TerminateInstances
        - ec2:StartInstances
        - ec2:StopInstances
        - ecs:CreateCluster
        - ecs:DescribeClusters
        - ecs:CreateService
        - ecs:UpdateService
        - ecs:DescribeServices
        - ecs:RunTask
        - ecs:StopTask
        - ecs:DeleteCluster
        - ecs:DeleteService
        - secretsmanager:GetSecretValue
        - cloudformation:CreateStack
        - cloudformation:UpdateStack
        - cloudformation:DescribeStacks
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - execute-api:Invoke
        - ec2:CreateFlowLogs
        - ec2:DescribeFlowLogs
        - ec2:*
      Resource: "*"

custom:
  serviceName: rozieai-zrh-uc05
  ecrRepoUri: ${env:ECR_REPO_URI}
  domainName: ${env:domain-name}
  awsRegion: ${env:AWS_REGION}
  publicHostedZoneID: ${env:hosted-zone-id}
  acmCertificateArn: ${env:acm-cert-arn}

resources:
  Resources:
    ECSCluster:
      Type: AWS::ECS::Cluster
      Properties:
        ClusterName: ${self:service}-${self:provider.stage}-cluster
        CapacityProviders:
          - FARGATE
        DefaultCapacityProviderStrategy:
          - CapacityProvider: FARGATE
            Weight: 1

    ECRECSExecutionRole:
      Type: AWS::IAM::Role
      Properties:
        RoleName: ${self:service}-${self:provider.stage}-ecs-execution
        AssumeRolePolicyDocument:
          Version: "2012-10-17"
          Statement:
            - Effect: Allow
              Principal:
                Service: ecs-tasks.amazonaws.com
              Action: sts:AssumeRole
        Policies:
          - PolicyName: ${self:service}-${self:provider.stage}-policy
            PolicyDocument:
              Version: "2012-10-17"
              Statement:
                - Effect: Allow
                  Action:
                    - logs:CreateLogGroup
                    - logs:CreateLogStream
                    - logs:PutLogEvents
                    - dynamodb:*
                  Resource: "*"
                - Effect: Allow
                  Action:
                    - ecr:GetDownloadUrlForLayer
                  Resource: "*"
                - Effect: Allow
                  Action:
                    - ecr:GetAuthorizationToken
                  Resource: "*"
                - Effect: Allow
                  Action: 
                    - ecr:InitiateLayerUpload
                  Resource: "*"
                - Effect: Allow
                  Action: 
                    - ecr:BatchGetImage
                  Resource: "*"
                - Effect: Allow
                  Action: ec2:*
                  Resource: "*"
                - Effect: Allow
                  Action: ecs:*
                  Resource: "*"
                - Effect: Allow
                  Action: vpc:*
                  Resource: "*"
                - Effect: Allow
                  Action: ec2:AmazonEC2ContainerRegistryFullAccess
                  Resource: "*"
                - Effect: Allow
                  Action: ec2:AmazonElasticContainerRegistryPublicFullAccess
                  Resource: "*"
                - Effect: Allow
                  Action:
                    - execute-api:Invoke
                  Resource: "*"
                - Effect: Allow
                  Action:
                    - secretsmanager:GetSecretValue
                  Resource: "*"

    CloudWatchLogsGroup:
      Type: AWS::Logs::LogGroup
      Properties:
        LogGroupName: "${self:service}-${self:provider.stage}"
        RetentionInDays: 30
        
    VPC:
      Type: AWS::EC2::VPC
      DependsOn: InternetGateway
      Properties:
        CidrBlock: 10.0.0.0/16
        EnableDnsSupport: true
        EnableDnsHostnames: true

    AZ1PublicSubnet1:
      Type: AWS::EC2::Subnet
      Properties:
        AvailabilityZone:
          Fn::Select:
            - 0
            - Fn::GetAZs: { Ref: "AWS::Region" }
        VpcId: !Ref "VPC"
        CidrBlock: 10.0.0.0/18
        MapPublicIpOnLaunch: true

    AZ2PublicSubnet1:
      Type: AWS::EC2::Subnet
      Properties:
        AvailabilityZone:
          Fn::Select:
            - 1
            - Fn::GetAZs: { Ref: "AWS::Region" }
        VpcId: !Ref "VPC"
        CidrBlock: *********/18
        MapPublicIpOnLaunch: true

    AZ1PrivateSubnet1:
      Type: AWS::EC2::Subnet
      Properties:
        AvailabilityZone:
          Fn::Select:
            - 0
            - Fn::GetAZs: { Ref: "AWS::Region" }
        VpcId: !Ref "VPC"
        CidrBlock: **********/18

    AZ2PrivateSubnet1:
      Type: AWS::EC2::Subnet
      Properties:
        AvailabilityZone:
          Fn::Select:
            - 1
            - Fn::GetAZs: { Ref: "AWS::Region" }
        VpcId: !Ref "VPC"
        CidrBlock: **********/18

    InternetGateway:
      Type: AWS::EC2::InternetGateway

    GatewayAttachement:
      Type: AWS::EC2::VPCGatewayAttachment
      Properties:
        VpcId: !Ref "VPC"
        InternetGatewayId: !Ref "InternetGateway"

    PublicRouteTable:
      Type: AWS::EC2::RouteTable
      Properties:
        VpcId: !Ref "VPC"
 
    RouteToInternet:
      Type: AWS::EC2::Route
      DependsOn: GatewayAttachement
      Properties:
        RouteTableId: !Ref "PublicRouteTable"
        DestinationCidrBlock: "0.0.0.0/0"
        GatewayId: !Ref "InternetGateway"

    AZ1PublicSubnet1RouteTableAssociation:
      Type: AWS::EC2::SubnetRouteTableAssociation
      Properties:
        SubnetId: !Ref AZ1PublicSubnet1
        RouteTableId: !Ref PublicRouteTable

    AZ2PublicSubnet1RouteTableAssociation:
      Type: AWS::EC2::SubnetRouteTableAssociation
      Properties:
        SubnetId: !Ref AZ2PublicSubnet1
        RouteTableId: !Ref PublicRouteTable

    PrivateRouteTable:
      Type: AWS::EC2::RouteTable
      Properties:
        VpcId: !Ref "VPC"

    AZ1PrivateSubnet1RouteTableAssociation:
      Type: AWS::EC2::SubnetRouteTableAssociation
      Properties:
        SubnetId: !Ref AZ1PrivateSubnet1
        RouteTableId: !Ref PrivateRouteTable

    AZ2PrivateSubnet1RouteTableAssociation:
      Type: AWS::EC2::SubnetRouteTableAssociation
      Properties:
        SubnetId: !Ref AZ2PrivateSubnet1
        RouteTableId: !Ref PrivateRouteTable

    InstanceSecurityGroup:
      Type: AWS::EC2::SecurityGroup
      Properties:
        GroupDescription: Allow HTTP and HTTPS traffic
        GroupName: ${self:service}-${self:provider.stage}-sg
        VpcId: !Ref VPC
        SecurityGroupIngress:
          - IpProtocol: -1
            CidrIp: 0.0.0.0/0
        SecurityGroupEgress:
          - IpProtocol: -1
            CidrIp: 0.0.0.0/0

    ApplicationLoadBalancer:
      Type: AWS::ElasticLoadBalancingV2::LoadBalancer
      Properties:
        Name: "${self:service}-${self:provider.stage}-alb"
        Type: "application"
        Scheme: "internet-facing"
        Subnets:
          - !Ref AZ1PublicSubnet1
          - !Ref AZ2PublicSubnet1
        SecurityGroups:
          - !Ref InstanceSecurityGroup
        LoadBalancerAttributes:
          - Key: "idle_timeout.timeout_seconds"
            Value: 4000

    LoadBalancerListener:
      Type: AWS::ElasticLoadBalancingV2::Listener
      DependsOn:
        - ApplicationLoadBalancer
      Properties:
        LoadBalancerArn: !Ref ApplicationLoadBalancer
        Protocol: HTTP
        Port: 80
        DefaultActions:
          - Type: forward
            TargetGroupArn: !Ref ElsaTargetGroup

    ElsaTargetGroup:
      Type: AWS::ElasticLoadBalancingV2::TargetGroup
      DependsOn: ApplicationLoadBalancer
      Properties:
        Name: "${self:service}-${self:provider.stage}-tg"
        Port: 8000
        Protocol: HTTP
        VpcId: !Ref VPC
        HealthCheckEnabled: true
        HealthCheckIntervalSeconds: 60
        HealthCheckPath: "/readiness"
        HealthCheckPort: "traffic-port"
        HealthCheckProtocol: HTTP
        HealthCheckTimeoutSeconds: 5
        HealthyThresholdCount: 2
        UnhealthyThresholdCount: 2
        IpAddressType: "ipv4"
        ProtocolVersion: "HTTP1"
        Matcher:
          HttpCode: "200"
        TargetType: "ip"

    FrontendTargetGroup:
      Type: AWS::ElasticLoadBalancingV2::TargetGroup
      DependsOn: ApplicationLoadBalancer
      Properties:
        Name: "${self:service}-frontend-${self:provider.stage}-tg"
        Port: 80
        Protocol: HTTP
        VpcId: !Ref VPC
        HealthCheckEnabled: true
        HealthCheckIntervalSeconds: 60
        HealthCheckPath: "/readiness.html"
        HealthCheckPort: "traffic-port"
        HealthCheckProtocol: HTTP
        HealthCheckTimeoutSeconds: 5
        HealthyThresholdCount: 2
        UnhealthyThresholdCount: 2
        IpAddressType: "ipv4"
        ProtocolVersion: "HTTP1"
        Matcher:
          HttpCode: "200"
        TargetType: "ip"

    DashboardTargetGroup:
      Type: AWS::ElasticLoadBalancingV2::TargetGroup
      DependsOn: ApplicationLoadBalancer
      Properties:
        Name: "${self:service}-dashboard-${self:provider.stage}-tg"
        Port: 2000
        Protocol: HTTP
        VpcId: !Ref VPC
        HealthCheckEnabled: true
        HealthCheckIntervalSeconds: 60
        HealthCheckPath: "/api/readiness"
        HealthCheckPort: "traffic-port"
        HealthCheckProtocol: HTTP
        HealthCheckTimeoutSeconds: 5
        HealthyThresholdCount: 2
        UnhealthyThresholdCount: 2
        IpAddressType: "ipv4"
        ProtocolVersion: "HTTP1"
        Matcher:
          HttpCode: "200"
        TargetType: "ip"

    TaskDefinition:
      Type: AWS::ECS::TaskDefinition
      DependsOn:
        - CloudWatchLogsGroup
      Properties:
        Family: ${self:service}-${self:provider.stage}
        NetworkMode: awsvpc
        RuntimePlatform:
          CpuArchitecture: X86_64
          OperatingSystemFamily: LINUX
        RequiresCompatibilities:
          - FARGATE
        ExecutionRoleArn: !GetAtt ECRECSExecutionRole.Arn
        TaskRoleArn: !GetAtt ECRECSExecutionRole.Arn
        Cpu: 1024
        Memory: 2048
        ContainerDefinitions:
          - Name: ${self:service}-${self:provider.stage}
            Image: ${self:custom.ecrRepoUri}
            Memory: 2048
            Cpu: 1024
            Essential: true
            PortMappings:
              - ContainerPort: 8000
                Protocol: "tcp"
            Environment:
              - Name: FASTAPI_ENV
                Value: ${env:FASTAPI_ENV, "development"}
              - Name: LOG_LEVEL
                Value: ${env:LOG_LEVEL, "INFO"}
              - Name: PORT
                Value: "8000"
              - Name: BAML_ENVIRONMENT
                Value: ${env:BAML_ENVIRONMENT, "development"}
              - Name: SUPABASE_URL
                Value: ${env:SUPABASE_URL}
              - Name: SUPABASE_ANON_KEY
                Value: ${env:SUPABASE_ANON_KEY}
              - Name: SUPPORT_SUBDOMAIN
                Value: ${env:SUPPORT_SUBDOMAIN}
              - Name: SUPPORT_EMAIL
                Value: ${env:SUPPORT_EMAIL}
            Secrets:
              - Name: OPENAI_API_KEY
                ValueFrom: !Sub "${self:service}-${self:provider.stage}-secrets:OPENAI_API_KEY::"
              - Name: SUPPORT_TOKEN
                ValueFrom: !Sub "${self:service}-${self:provider.stage}-secrets:SUPPORT_TOKEN::"
              - Name: SUPABASE_SERVICE_ROLE_KEY
                ValueFrom: !Sub "${self:service}-${self:provider.stage}-secrets:SUPABASE_SERVICE_ROLE_KEY::"
              - Name: JWT_SECRET
                ValueFrom: !Sub "${self:service}-${self:provider.stage}-secrets:JWT_SECRET::"
              - Name: SESSION_SECRET
                ValueFrom: !Sub "${self:service}-${self:provider.stage}-secrets:SESSION_SECRET::"
            LogConfiguration:
              LogDriver: awslogs
              Options:
                awslogs-group: "${self:service}-${self:provider.stage}"
                awslogs-region: !Ref AWS::Region
                awslogs-stream-prefix: "${self:service}-${self:provider.stage}"

    # N8N Task Definition
    N8NTaskDefinition:
      Type: AWS::ECS::TaskDefinition
      DependsOn:
        - CloudWatchLogsGroup
      Properties:
        Family: ${self:service}-n8n-${self:provider.stage}
        NetworkMode: awsvpc
        RuntimePlatform:
          CpuArchitecture: X86_64
          OperatingSystemFamily: LINUX
        RequiresCompatibilities:
          - FARGATE
        ExecutionRoleArn: !GetAtt ECRECSExecutionRole.Arn
        TaskRoleArn: !GetAtt ECRECSExecutionRole.Arn
        Cpu: 512
        Memory: 1024
        ContainerDefinitions:
          - Name: n8n-${self:service}-${self:provider.stage}
            Image: n8nio/n8n:latest
            Memory: 1024
            Cpu: 512
            Essential: true
            PortMappings:
              - ContainerPort: 5678
                Protocol: "tcp"
            Environment:
              - Name: N8N_HOST
                Value: "0.0.0.0"
              - Name: N8N_PORT
                Value: "5678"
              - Name: N8N_PROTOCOL
                Value: "http"
              - Name: WEBHOOK_URL
                Value: !Sub "http://${ApplicationLoadBalancer.DNSName}:5678"
              - Name: N8N_EDITOR_BASE_URL
                Value: !Sub "http://${ApplicationLoadBalancer.DNSName}:5678"
            Secrets:
              - Name: N8N_DB_POSTGRESDB_HOST
                ValueFrom: !Sub "${self:service}-${self:provider.stage}-secrets:N8N_DB_POSTGRESDB_HOST::"
              - Name: N8N_DB_POSTGRESDB_PORT
                ValueFrom: !Sub "${self:service}-${self:provider.stage}-secrets:N8N_DB_POSTGRESDB_PORT::"
              - Name: N8N_DB_POSTGRESDB_DATABASE
                ValueFrom: !Sub "${self:service}-${self:provider.stage}-secrets:N8N_DB_POSTGRESDB_DATABASE::"
              - Name: N8N_DB_POSTGRESDB_USER
                ValueFrom: !Sub "${self:service}-${self:provider.stage}-secrets:N8N_DB_POSTGRESDB_USER::"
              - Name: N8N_DB_POSTGRESDB_PASSWORD
                ValueFrom: !Sub "${self:service}-${self:provider.stage}-secrets:N8N_DB_POSTGRESDB_PASSWORD::"
            LogConfiguration:
              LogDriver: awslogs
              Options:
                awslogs-group: "${self:service}-${self:provider.stage}"
                awslogs-region: !Ref AWS::Region
                awslogs-stream-prefix: "n8n-${self:service}-${self:provider.stage}"

    # N8N Target Group
    N8NTargetGroup:
      Type: AWS::ElasticLoadBalancingV2::TargetGroup
      DependsOn: ApplicationLoadBalancer
      Properties:
        Name: "${self:service}-n8n-${self:provider.stage}-tg"
        Port: 5678
        Protocol: HTTP
        VpcId: !Ref VPC
        HealthCheckEnabled: true
        HealthCheckIntervalSeconds: 60
        HealthCheckPath: "/healthz"
        HealthCheckPort: "traffic-port"
        HealthCheckProtocol: HTTP
        HealthCheckTimeoutSeconds: 5
        HealthyThresholdCount: 2
        UnhealthyThresholdCount: 2
        IpAddressType: "ipv4"
        ProtocolVersion: "HTTP1"
        Matcher:
          HttpCode: "200"
        TargetType: "ip"

    # N8N Load Balancer Listener
    N8NLoadBalancerListener:
      Type: AWS::ElasticLoadBalancingV2::Listener
      DependsOn:
        - ApplicationLoadBalancer
      Properties:
        LoadBalancerArn: !Ref ApplicationLoadBalancer
        Protocol: HTTP
        Port: 5678
        DefaultActions:
          - Type: forward
            TargetGroupArn: !Ref N8NTargetGroup

    EcrEcsSlsTask:
      Type: AWS::ECS::Service
      DependsOn:
        - TaskDefinition
        - ElsaTargetGroup
        - ApplicationLoadBalancer
        - LoadBalancerListener
      Properties:
        Cluster: !Ref ECSCluster
        DesiredCount: 1
        TaskDefinition: !Ref TaskDefinition
        ServiceName: ${self:service}-${self:provider.stage}-service
        PlatformVersion: LATEST
        EnableECSManagedTags: true
        DeploymentConfiguration:
          MaximumPercent: 200
          MinimumHealthyPercent: 70
          Alarms:
            AlarmNames:
              - "${self:service}-${self:provider.stage}-service-alarm"
            Enable: true
            Rollback: true
          DeploymentCircuitBreaker:
            Enable: false
            Rollback: false
        NetworkConfiguration:
          AwsvpcConfiguration:
            SecurityGroups:
              - !Ref InstanceSecurityGroup
            Subnets:
              - !Ref AZ1PrivateSubnet1
              - !Ref AZ2PrivateSubnet1
        CapacityProviderStrategy:
          - CapacityProvider: FARGATE
            Weight: 1
        LoadBalancers:
          - ContainerName: ${self:service}-${self:provider.stage}
            ContainerPort: 8000
            TargetGroupArn: !Ref ElsaTargetGroup

    # N8N ECS Service
    N8NEcrEcsSlsTask:
      Type: AWS::ECS::Service
      DependsOn:
        - N8NTaskDefinition
        - N8NTargetGroup
        - ApplicationLoadBalancer
        - N8NLoadBalancerListener
      Properties:
        Cluster: !Ref ECSCluster
        DesiredCount: 1
        TaskDefinition: !Ref N8NTaskDefinition
        ServiceName: ${self:service}-n8n-${self:provider.stage}-service
        PlatformVersion: LATEST
        EnableECSManagedTags: true
        DeploymentConfiguration:
          MaximumPercent: 200
          MinimumHealthyPercent: 70
          DeploymentCircuitBreaker:
            Enable: false
            Rollback: false
        NetworkConfiguration:
          AwsvpcConfiguration:
            SecurityGroups:
              - !Ref InstanceSecurityGroup
            Subnets:
              - !Ref AZ1PrivateSubnet1
              - !Ref AZ2PrivateSubnet1
        CapacityProviderStrategy:
          - CapacityProvider: FARGATE
            Weight: 1
        LoadBalancers:
          - ContainerName: n8n-${self:service}-${self:provider.stage}
            ContainerPort: 5678
            TargetGroupArn: !Ref N8NTargetGroup

    # Secrets Manager Secret
    AppSecrets:
      Type: AWS::SecretsManager::Secret
      Properties:
        Name: ${self:service}-${self:provider.stage}-secrets
        Description: "Application secrets for Zurich Workflow"
        SecretString: !Sub |
          {
            "OPENAI_API_KEY": "${env:OPENAI_API_KEY}",
            "SUPPORT_TOKEN": "${env:SUPPORT_TOKEN}",
            "SUPABASE_SERVICE_ROLE_KEY": "${env:SUPABASE_SERVICE_ROLE_KEY}",
            "JWT_SECRET": "${env:JWT_SECRET}",
            "SESSION_SECRET": "${env:SESSION_SECRET}",
            "N8N_DB_POSTGRESDB_HOST": "aws-0-ca-central-1.pooler.supabase.com",
            "N8N_DB_POSTGRESDB_PORT": "6543",
            "N8N_DB_POSTGRESDB_DATABASE": "postgres",
            "N8N_DB_POSTGRESDB_USER": "postgres.tlduggpohclrgxbvuzhd",
            "N8N_DB_POSTGRESDB_PASSWORD": "${env:SUPABASE_DB_PASSWORD}"
          }

    # S3 Gateway Endpoint
    S3GatewayEndpoint:
      Type: 'AWS::EC2::VPCEndpoint'
      Properties:
        VpcEndpointType: 'Gateway'
        VpcId: !Ref VPC
        ServiceName: !Sub 'com.amazonaws.${AWS::Region}.s3'
        PolicyDocument:
          Version: "2012-10-17"
          Statement:
            - Sid: "Allow-access-to-specific-bucket"
              Effect: "Allow"
              Principal: "*"
              Action:
                - "*"
              Resource:
                - "*"
        RouteTableIds:
          - !Ref PublicRouteTable
          - !Ref PrivateRouteTable

  Outputs:
    ECSClusterName:
      Description: "ECS Cluster Name"
      Value: !Ref ECSCluster
      Export:
        Name: !Sub "${self:service}-${self:provider.stage}-cluster-name"

    ApplicationLoadBalancerDNS:
      Description: "Application Load Balancer DNS Name"
      Value: !GetAtt ApplicationLoadBalancer.DNSName
      Export:
        Name: !Sub "${self:service}-${self:provider.stage}-alb-dns"

    BackendTargetGroupArn:
      Description: "Backend Target Group ARN"
      Value: !Ref ElsaTargetGroup
      Export:
        Name: !Sub "${self:service}-${self:provider.stage}-backend-tg-arn"

    N8NTargetGroupArn:
      Description: "N8N Target Group ARN"
      Value: !Ref N8NTargetGroup
      Export:
        Name: !Sub "${self:service}-${self:provider.stage}-n8n-tg-arn"

    VPCId:
      Description: "VPC ID"
      Value: !Ref VPC
      Export:
        Name: !Sub "${self:service}-${self:provider.stage}-vpc-id"

    PrivateSubnet1Id:
      Description: "Private Subnet 1 ID"
      Value: !Ref AZ1PrivateSubnet1
      Export:
        Name: !Sub "${self:service}-${self:provider.stage}-private-subnet-1-id"

    PrivateSubnet2Id:
      Description: "Private Subnet 2 ID"
      Value: !Ref AZ2PrivateSubnet1
      Export:
        Name: !Sub "${self:service}-${self:provider.stage}-private-subnet-2-id"
