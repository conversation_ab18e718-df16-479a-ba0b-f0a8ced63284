// Supabase Configuration for Claim Upload
// Update these values with your actual Supabase project details

const SUPABASE_CONFIG = {
    url: 'https://your-supabase-url.supabase.co',
    anon<PERSON>ey: 'your-supabase-anon-key',
    
    // Storage bucket for claim attachments
    bucketName: 'claims-attachments',
    
    // File upload settings
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedFileTypes: [
        'application/pdf',
        'image/jpeg',
        'image/jpg', 
        'image/png',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ],
    allowedExtensions: ['.pdf', '.jpg', '.jpeg', '.png', '.doc', '.docx']
};

// Export for use in claim-upload.html
window.SUPABASE_CONFIG = SUPABASE_CONFIG;
