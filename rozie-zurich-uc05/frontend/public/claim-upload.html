<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Upload - Zurich Insurance</title>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="config.js"></script>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; background-color: #f4f4f4; padding: 20px;">
    <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <!-- Header -->
        <div style="text-align: center; border-bottom: 3px solid #005AAF; padding-bottom: 20px; margin-bottom: 30px;">
            <h1 style="color: #005AAF; margin: 0; font-size: 28px;">Zurich Insurance</h1>
            <p style="color: #666; margin: 5px 0 0 0; font-size: 16px;">Claims Document Upload</p>
        </div>

        <!-- Main Content -->
        <div>
            <h2 style="color: #005AAF; margin: 0 0 20px 0;">Upload Required Documents</h2>
            
            <p>Please upload the required documents for your claim. All files will be securely stored and reviewed by our claims processing team.</p>

            <!-- Claim Details -->
            <div style="margin: 25px 0;">
                <table style="width: 100%; border-collapse: collapse; border: 1px solid #ddd; border-radius: 5px; overflow: hidden;">
                    <tr>
                        <td style="padding: 8px 15px; border-bottom: 1px solid #eee; font-weight: bold; background-color: #f8f9fa;">Claim Reference:</td>
                        <td style="padding: 8px 15px; border-bottom: 1px solid #eee; background-color: #f8f9fa;" id="claimId">Loading...</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 15px; font-weight: bold;">Upload Date:</td>
                        <td style="padding: 8px 15px;" id="uploadDate">Loading...</td>
                    </tr>
                </table>
            </div>

            <!-- Upload Section -->
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3 style="color: #005AAF; margin: 0 0 15px 0;">Select Documents to Upload</h3>
                
                <div style="margin: 15px 0;">
                    <input type="file" id="fileInput" multiple accept=".pdf,.jpg,.jpeg,.png,.doc,.docx" 
                           style="width: 100%; padding: 10px; border: 2px dashed #005AAF; border-radius: 5px; background-color: white;">
                    <p style="margin: 10px 0 0 0; font-size: 14px; color: #666;">
                        Accepted formats: PDF, JPG, PNG, DOC, DOCX (Max 10MB per file)
                    </p>
                </div>

                <div style="text-align: center; margin: 20px 0;">
                    <button id="uploadBtn" onclick="uploadFiles()" 
                            style="background-color: #005AAF; color: white; padding: 12px 30px; border: none; border-radius: 5px; font-weight: bold; cursor: pointer; font-size: 16px;">
                        Upload Documents
                    </button>
                </div>

                <!-- Progress Bar -->
                <div id="progressContainer" style="display: none; margin: 15px 0;">
                    <div style="background-color: #e0e0e0; border-radius: 10px; overflow: hidden;">
                        <div id="progressBar" style="background-color: #28a745; height: 20px; width: 0%; transition: width 0.3s;"></div>
                    </div>
                    <p id="progressText" style="text-align: center; margin: 10px 0 0 0; font-size: 14px;">Uploading...</p>
                </div>

                <!-- Upload Status -->
                <div id="uploadStatus" style="display: none; margin: 15px 0; padding: 15px; border-radius: 5px;"></div>
            </div>

            <!-- Success Message (Hidden by default) -->
            <div id="successMessage" style="display: none; background-color: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3 style="color: #155724; margin: 0 0 15px 0; display: flex; align-items: center;">
                    <span style="background-color: #28a745; color: white; border-radius: 50%; width: 24px; height: 24px; display: inline-flex; align-items: center; justify-content: center; margin-right: 10px; font-weight: bold;">✓</span>
                    Upload Completed Successfully
                </h3>
                <p style="margin: 0; color: #155724;">
                    Your documents have been uploaded successfully. We will review your submission and update you on the claim status within 2-3 business days.
                </p>
            </div>

            <!-- Important Notes -->
            <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3 style="color: #856404; margin: 0 0 15px 0;">Important Notes</h3>
                <ul style="margin: 0; padding-left: 20px; color: #856404;">
                    <li style="margin-bottom: 8px;">Ensure all documents are clear and legible</li>
                    <li style="margin-bottom: 8px;">Include your claim reference number on all documents</li>
                    <li style="margin-bottom: 8px;">Maximum file size: 10MB per document</li>
                    <li style="margin-bottom: 8px;">You will receive a confirmation once upload is complete</li>
                </ul>
            </div>

            <!-- Contact Information -->
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 25px 0;">
                <h3 style="color: #005AAF; margin: 0 0 10px 0;">Need Assistance?</h3>
                <p style="margin: 0 0 10px 0;">If you experience any issues with the upload process:</p>
                <p style="margin: 0 0 10px 0;"><strong>Email:</strong> <EMAIL></p>
                <p style="margin: 0 0 10px 0;"><strong>Reference:</strong> <span id="claimIdFooter">Your Claim ID</span></p>
                <p style="margin: 0;">Our technical support team is available Monday through Friday, 8:00 AM to 6:00 PM.</p>
            </div>
        </div>

        <!-- Footer -->
        <div style="border-top: 1px solid #eee; padding-top: 20px; margin-top: 30px; text-align: center; color: #666; font-size: 14px;">
            <p style="margin: 0;">Secure document upload powered by Zurich Insurance.</p>
            <p style="margin: 5px 0 0 0;">© 2025 Zurich Insurance Company. All rights reserved.</p>
        </div>
    </div>

    <script>
        // Configuration
        const SUPABASE_URL = 'https://your-supabase-url.supabase.co';
        const SUPABASE_ANON_KEY = 'your-supabase-anon-key';
        
        // Initialize Supabase client
        const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        
        // Global variables
        let claimId = '';
        let webhookUrl = '';
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            initializePage();
        });
        
        function initializePage() {
            // Get URL parameters
            const urlParams = new URLSearchParams(window.location.search);
            claimId = urlParams.get('claimId') || 'Unknown';
            const encodedWebhookUrl = urlParams.get('webhookUrl') || '';

            // Decode webhook URL
            try {
                webhookUrl = atob(encodedWebhookUrl);
            } catch (e) {
                console.error('Error decoding webhook URL:', e);
                webhookUrl = '';
            }

            // Update UI with claim ID
            document.getElementById('claimId').textContent = claimId;
            document.getElementById('claimIdFooter').textContent = claimId;

            // Set current date
            const currentDate = new Date().toLocaleString('en-US', {
                year: 'numeric',
                month: 'long',
                day: '2-digit'
            });
            document.getElementById('uploadDate').textContent = currentDate;
        }

        async function uploadFiles() {
            const fileInput = document.getElementById('fileInput');
            const files = fileInput.files;

            if (files.length === 0) {
                showStatus('Please select at least one file to upload.', 'error');
                return;
            }

            // Validate files
            for (let file of files) {
                if (file.size > 10 * 1024 * 1024) { // 10MB limit
                    showStatus(`File "${file.name}" is too large. Maximum size is 10MB.`, 'error');
                    return;
                }
            }

            // Show progress
            showProgress(true);
            updateProgress(0, 'Starting upload...');

            try {
                const uploadedFiles = [];

                for (let i = 0; i < files.length; i++) {
                    const file = files[i];
                    const progress = ((i + 1) / files.length) * 90; // Reserve 10% for webhook

                    updateProgress(progress, `Uploading ${file.name}...`);

                    // Generate unique filename
                    const timestamp = Date.now();
                    const fileName = `${timestamp}_${file.name}`;
                    const filePath = `claims-attachments/claim/${claimId}/${fileName}`;

                    // Upload to Supabase
                    const { data, error } = await supabase.storage
                        .from('claims-attachments')
                        .upload(filePath, file);

                    if (error) {
                        throw new Error(`Failed to upload ${file.name}: ${error.message}`);
                    }

                    uploadedFiles.push({
                        name: file.name,
                        path: filePath,
                        size: file.size,
                        type: file.type
                    });
                }

                // Trigger webhook
                updateProgress(95, 'Notifying claims team...');
                await triggerWebhook(uploadedFiles);

                // Complete
                updateProgress(100, 'Upload completed successfully!');
                setTimeout(() => {
                    showProgress(false);
                    showSuccessMessage();
                }, 1000);

            } catch (error) {
                console.error('Upload error:', error);
                showProgress(false);
                showStatus(`Upload failed: ${error.message}`, 'error');
            }
        }

        async function triggerWebhook(uploadedFiles) {
            if (!webhookUrl) {
                console.warn('No webhook URL provided');
                return;
            }

            try {
                const response = await fetch(webhookUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        claimId: claimId,
                        uploadedFiles: uploadedFiles,
                        uploadTimestamp: new Date().toISOString(),
                        status: 'completed'
                    })
                });

                if (!response.ok) {
                    throw new Error(`Webhook failed: ${response.status}`);
                }

                console.log('Webhook triggered successfully');
            } catch (error) {
                console.error('Webhook error:', error);
                // Don't fail the entire upload if webhook fails
            }
        }

        function showProgress(show) {
            const container = document.getElementById('progressContainer');
            container.style.display = show ? 'block' : 'none';
        }

        function updateProgress(percent, text) {
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');

            progressBar.style.width = percent + '%';
            progressText.textContent = text;
        }

        function showStatus(message, type) {
            const statusDiv = document.getElementById('uploadStatus');
            statusDiv.style.display = 'block';
            statusDiv.textContent = message;

            if (type === 'error') {
                statusDiv.style.backgroundColor = '#f8d7da';
                statusDiv.style.borderColor = '#f5c6cb';
                statusDiv.style.color = '#721c24';
            } else {
                statusDiv.style.backgroundColor = '#d4edda';
                statusDiv.style.borderColor = '#c3e6cb';
                statusDiv.style.color = '#155724';
            }
        }

        function showSuccessMessage() {
            document.getElementById('successMessage').style.display = 'block';
            document.getElementById('uploadBtn').style.display = 'none';
            document.getElementById('fileInput').style.display = 'none';
        }
