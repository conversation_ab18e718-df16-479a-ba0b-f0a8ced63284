#!/bin/bash

# =============================================================================
# N8N STANDALONE DEPLOYMENT SCRIPT
# =============================================================================
# This script deploys N8N as a standalone service using AWS ECS Fargate
# =============================================================================

set -e

# Configuration
STAGE=${1:-dev}
REGION=${2:-ca-central-1}
SERVICE_NAME="n8n-zurich-standalone"

echo "🚀 Deploying N8N Standalone Service"
echo "===================================="
echo "Stage: $STAGE"
echo "Region: $REGION"
echo "Service: $SERVICE_NAME"
echo ""

# Check if serverless is installed
if ! command -v serverless &> /dev/null; then
    echo "❌ Serverless Framework not found. Installing..."
    npm install -g serverless
fi

# Check AWS credentials
if ! aws sts get-caller-identity &> /dev/null; then
    echo "❌ AWS credentials not configured. Please run 'aws configure' first."
    exit 1
fi

echo "✅ Prerequisites check passed"
echo ""

# Deploy infrastructure
echo "🏗️ Deploying infrastructure..."
serverless deploy --stage $STAGE --region $REGION --verbose

# Get deployment outputs
echo ""
echo "📋 Getting deployment information..."
STACK_NAME="$SERVICE_NAME-$STAGE"

LB_DNS=$(aws cloudformation describe-stacks \
    --stack-name $STACK_NAME \
    --query 'Stacks[0].Outputs[?OutputKey==`N8NLoadBalancerDNS`].OutputValue' \
    --output text \
    --region $REGION)

CLUSTER_NAME=$(aws cloudformation describe-stacks \
    --stack-name $STACK_NAME \
    --query 'Stacks[0].Outputs[?OutputKey==`N8NClusterName`].OutputValue' \
    --output text \
    --region $REGION)

echo "Load Balancer DNS: $LB_DNS"
echo "ECS Cluster: $CLUSTER_NAME"
echo ""

# Wait for service to be stable
echo "⏳ Waiting for service to stabilize..."
aws ecs wait services-stable \
    --cluster $CLUSTER_NAME \
    --services "n8n-zurich-$STAGE" \
    --region $REGION

echo "✅ Service is stable!"
echo ""

# Test the deployment
echo "🧪 Testing deployment..."
for i in {1..10}; do
    if curl -f -s "http://$LB_DNS/healthz" > /dev/null; then
        echo "✅ Health check passed!"
        break
    else
        echo "⏳ Waiting for service to be ready... (attempt $i/10)"
        sleep 30
    fi
done

echo ""
echo "🎉 N8N Standalone Deployment Complete!"
echo "======================================"
echo ""
echo "🌐 Access Information:"
echo "Load Balancer: http://$LB_DNS"
echo "N8N Interface: https://n8n-zurich.dev-scc-demo.rozie.ai (after DNS setup)"
echo "Health Check: http://$LB_DNS/healthz"
echo ""
echo "📋 Next Steps:"
echo "1. Configure DNS:"
echo "   - Point n8n-zurich.dev-scc-demo.rozie.ai to $LB_DNS"
echo "2. Set up SSL certificate for HTTPS"
echo "3. Test N8N workflows"
echo ""
echo "🔧 Management Commands:"
echo "View logs:"
echo "  aws logs tail /ecs/n8n-zurich-$STAGE --follow --region $REGION"
echo ""
echo "Scale service:"
echo "  aws ecs update-service --cluster $CLUSTER_NAME --service n8n-zurich-$STAGE --desired-count 2 --region $REGION"
echo ""
echo "Destroy deployment:"
echo "  serverless remove --stage $STAGE --region $REGION"
