version: '3.8'

# =============================================================================
# STANDALONE N8N DEPLOYMENT FOR ZURICH WORKFLOW
# =============================================================================
# This is a completely independent N8N deployment that can be deployed
# separately from the main Zurich workflow application
# =============================================================================

services:
  # =============================================================================
  # N8N WORKFLOW AUTOMATION (STANDALONE)
  # =============================================================================
  n8n:
    image: n8nio/n8n:latest
    container_name: n8n-zurich-standalone
    restart: unless-stopped
    ports:
      - "5678:5678"
    user: "1000:1000"
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./workflows:/home/<USER>/.n8n/workflows
      - ./credentials:/home/<USER>/.n8n/credentials
    environment:
      # Production N8N Configuration for Zurich
      - N8N_HOST=n8n-zurich.dev-scc-demo.rozie.ai
      - N8N_PORT=443
      - N8N_PROTOCOL=https
      - N8N_EDITOR_BASE_URL=https://n8n-zurich.dev-scc-demo.rozie.ai
      - WEBHOOK_URL=https://n8n-zurich.dev-scc-demo.rozie.ai
      - N8N_BASIC_AUTH_ACTIVE=false
      - N8N_LISTEN_ADDRESS=0.0.0.0
      - N8N_DISABLE_UI=false
      - N8N_METRICS=true
      - N8N_SECURE_COOKIE=false
      - N8N_LOG_LEVEL=info
      - GENERIC_TIMEZONE=UTC
      - N8N_TEMPLATES_ENABLED=true
      - N8N_USER_FOLDER=/home/<USER>
      - N8N_PUSH_BACKEND=websocket
      - N8N_SKIP_WEBHOOK_DEREGISTRATION_SHUTDOWN=true
      
      # Database Configuration (SQLite for simplicity)
      - DB_TYPE=sqlite
      - DB_SQLITE_DATABASE=/home/<USER>/.n8n/database.sqlite
      
      # Workflow Configuration
      - WORKFLOWS_FOLDER=/home/<USER>/.n8n/workflows
      - N8N_DEFAULT_BINARY_DATA_MODE=filesystem
      
      # Security Configuration
      - N8N_BLOCK_ENV_ACCESS_IN_NODE=false
      - N8N_FUNCTION_ALLOW_BUILTIN=*
      - N8N_FUNCTION_ALLOW_EXTERNAL=*
      
    networks:
      - n8n-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:5678/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # =============================================================================
  # NGINX REVERSE PROXY FOR N8N
  # =============================================================================
  nginx:
    image: nginx:alpine
    container_name: n8n-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - n8n
    networks:
      - n8n-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 5s
      retries: 3

# =============================================================================
# NETWORKS
# =============================================================================
networks:
  n8n-network:
    driver: bridge

# =============================================================================
# VOLUMES
# =============================================================================
volumes:
  n8n_data:
    driver: local
