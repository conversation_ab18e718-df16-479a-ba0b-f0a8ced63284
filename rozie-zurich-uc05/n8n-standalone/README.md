# N8N Standalone Deployment for Zurich Workflow

This directory contains the standalone N8N deployment configuration, completely separate from the main Zurich workflow application.

**🚀 Ready for deployment on branch: zurich/dinesh/zurich_workflow_uc05_n8n**

**🔧 Fixed workflow cluster/service naming - redeploying with correct ECS references**

## 🎯 **Overview**

- **Standalone N8N Service**: Independent deployment using AWS ECS Fargate
- **Dedicated Infrastructure**: Own VPC, load balancer, and security groups
- **Automatic Deployment**: GitHub Actions workflow for CI/CD
- **Production Ready**: Health checks, logging, and monitoring

## 🚀 **Quick Start**

### **Option 1: GitHub Actions (Recommended)**

1. **Push changes** to trigger automatic deployment:
   ```bash
   git push origin zurich/dinesh/zurich_workflow_uc05
   ```

2. **Manual deployment** via GitHub Actions:
   - Go to GitHub → Actions → "Deploy N8N Standalone"
   - Click "Run workflow"
   - Select environment (dev/staging/prod)
   - Click "Run workflow"

### **Option 2: Local Deployment**

```bash
# Navigate to N8N standalone directory
cd rozie-zurich-uc05/n8n-standalone

# Deploy to dev environment
./deploy.sh dev

# Deploy to staging
./deploy.sh staging ca-central-1
```

## 📋 **What Gets Deployed**

### **Infrastructure Components**
- **ECS Cluster**: `n8n-zurich-{stage}`
- **ECS Service**: Fargate-based N8N container
- **Application Load Balancer**: Public-facing with health checks
- **VPC**: Dedicated network with public subnets
- **Security Groups**: Restricted access for N8N and ALB
- **CloudWatch Logs**: Centralized logging

### **N8N Configuration**
- **Domain**: `n8n-zurich.dev-scc-demo.rozie.ai`
- **Protocol**: HTTPS (after DNS/SSL setup)
- **Database**: SQLite (persistent via EFS)
- **Authentication**: Disabled for development
- **Webhooks**: Enabled with proper URL configuration

## 🌐 **Access URLs**

After deployment:

```bash
# N8N Interface (after DNS setup)
https://n8n-zurich.dev-scc-demo.rozie.ai

# Health Check
https://n8n-zurich.dev-scc-demo.rozie.ai/healthz

# Direct Load Balancer Access
http://{load-balancer-dns}/
```

## 🔧 **Configuration Files**

| File | Purpose |
|------|---------|
| `serverless.yml` | AWS infrastructure definition |
| `docker-compose.yml` | Local development setup |
| `nginx.conf` | Reverse proxy configuration |
| `deploy.sh` | Manual deployment script |

## 📊 **Monitoring & Management**

### **View Logs**
```bash
aws logs tail /ecs/n8n-zurich-dev --follow --region ca-central-1
```

### **Scale Service**
```bash
aws ecs update-service \
  --cluster n8n-zurich-dev \
  --service n8n-zurich-dev \
  --desired-count 2 \
  --region ca-central-1
```

### **Check Service Status**
```bash
aws ecs describe-services \
  --cluster n8n-zurich-dev \
  --services n8n-zurich-dev \
  --region ca-central-1
```

## 🔒 **Security Configuration**

- **Network**: Private subnets with NAT gateway for production
- **Access**: Load balancer security group restricts access
- **Authentication**: Can be enabled via environment variables
- **SSL**: Configure ACM certificate for HTTPS

## 🛠️ **Development**

### **Local Testing**
```bash
# Start N8N locally
docker-compose up -d

# Access local N8N
open http://localhost:5678
```

### **Environment Variables**
Key N8N configuration options:

```yaml
N8N_HOST: n8n-zurich.dev-scc-demo.rozie.ai
N8N_PROTOCOL: https
N8N_BASIC_AUTH_ACTIVE: false
N8N_LOG_LEVEL: info
```

## 🚨 **Troubleshooting**

### **Common Issues**

1. **Service won't start**
   - Check CloudWatch logs
   - Verify security group rules
   - Ensure proper IAM permissions

2. **Health check failing**
   - Verify N8N is responding on port 5678
   - Check target group health in AWS console

3. **DNS not resolving**
   - Configure Route 53 record pointing to load balancer
   - Verify domain configuration

### **Useful Commands**

```bash
# Get load balancer DNS
aws cloudformation describe-stacks \
  --stack-name n8n-zurich-standalone-dev \
  --query 'Stacks[0].Outputs[?OutputKey==`N8NLoadBalancerDNS`].OutputValue' \
  --output text

# Force service update
aws ecs update-service \
  --cluster n8n-zurich-dev \
  --service n8n-zurich-dev \
  --force-new-deployment
```

## 🗑️ **Cleanup**

To remove the entire N8N deployment:

```bash
# Using serverless
serverless remove --stage dev --region ca-central-1

# Or using AWS CLI
aws cloudformation delete-stack --stack-name n8n-zurich-standalone-dev
```

## 📞 **Support**

- **GitHub Issues**: Report problems in the main repository
- **Documentation**: Check N8N official docs for workflow help
- **AWS Console**: Monitor resources in ECS and CloudFormation
