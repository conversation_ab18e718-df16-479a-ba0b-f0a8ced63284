# N8N Standalone Dockerfile
# Based on official N8N image with custom configuration

FROM n8nio/n8n:latest

# Set working directory
WORKDIR /home/<USER>

# Switch to root to install packages
USER root

# Install additional packages if needed
RUN apk add --no-cache curl wget

# Create necessary directories
RUN mkdir -p /home/<USER>/.n8n/workflows
RUN mkdir -p /home/<USER>/.n8n/credentials

# Copy any custom workflows or configurations
# COPY workflows/ /home/<USER>/.n8n/workflows/
# COPY credentials/ /home/<USER>/.n8n/credentials/

# Set proper ownership
RUN chown -R node:node /home/<USER>/.n8n

# Switch back to node user
USER node

# Expose N8N port
EXPOSE 5678

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:5678/healthz || exit 1

# Start N8N
CMD ["n8n"]
