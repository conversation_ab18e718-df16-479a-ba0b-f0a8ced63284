# N8N Standalone Dockerfile
# Use official N8N image directly

FROM n8nio/n8n:latest

# Switch to root to install additional packages
USER root

# Install curl and wget for health checks
RUN apk add --no-cache curl wget

# Set environment variables for N8N
ENV N8N_HOST=0.0.0.0
ENV N8N_PORT=5678
ENV N8N_PROTOCOL=http
ENV WEBHOOK_URL=https://n8n-zurich.dev-scc-demo.rozie.ai/
ENV N8N_ENFORCE_SETTINGS_FILE_PERMISSIONS=false

# Create necessary directories and set permissions
RUN mkdir -p /home/<USER>/.n8n && \
    chown -R node:node /home/<USER>/.n8n && \
    chmod -R 755 /home/<USER>/.n8n

# Switch back to node user
USER node

# Set working directory
WORKDIR /home/<USER>

# Expose N8N port
EXPOSE 5678

# Health check using curl
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:5678/healthz || exit 1

# Use the same entrypoint as the official image
ENTRYPOINT ["tini", "--", "/docker-entrypoint.sh"]
CMD ["n8n"]
