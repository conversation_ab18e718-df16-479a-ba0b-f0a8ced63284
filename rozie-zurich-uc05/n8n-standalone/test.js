{{(() => {
  const email = $node["Gmail Trigger"].json;
  const from = email?.from?.value?.[0] || {};
  const subject = email?.subject || "No Subject";
  const body = email?.text || email?.html || "No content";
  const receivedDate = email?.date || new Date().toISOString();

  const attachments = [];

  $items().forEach(item => {
    // const binary = $node["Gmail Trigger"].binary || {};
    const binary = item.binary || {};
    for (const key of Object.keys(binary)) {
      const file = binary[key];
      if (!file) continue;

      attachments.push({
        filename: file.fileName || key || "Unnamed file",
        contentType: file.mimeType || "application/octet-stream",
        size:
          typeof file.fileSize === "number"
            ? file.fileSize
            : parseInt((file.fileSize || "").replace(/[^\d]/g, "")) || 0
      });
    }
  });

  return {
    subject,
    body,
    senderEmail: from.address || "<EMAIL>",
    senderName: from.name || (from.address?.split("@")[0] || "unknown"),
    receivedDate,
    attachments
  };
})()}}