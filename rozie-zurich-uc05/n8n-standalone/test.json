{"name": "Claim Document Request Flow", "nodes": [{"parameters": {}, "id": "1", "name": "Start Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"fromEmail": "<EMAIL>", "toEmail": "={{$json[\"claimantEmail\"]}}", "subject": "Zurich Claim - Document Request", "message": "Dear {{$json.claimantName}},<br><br>We are reviewing your claim (ID: {{$json.claimId}}). Please reply to this email with the requested documentation or upload using our secure portal.<br><br>Thank you.<br>Zurich Claims Team."}, "id": "2", "name": "Send Request Email", "type": "n8n-nodes-base.gmail", "typeVersion": 1, "position": [520, 300], "credentials": {"gmailOAuth2Api": {"id": "gmail_cred", "name": "Gmail Account"}}}, {"parameters": {"waitFor": "webhook", "webhookOptions": {}}, "id": "3", "name": "Wait for Reply", "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [780, 300]}, {"parameters": {"operation": "insert", "schema": "public", "table": "claim_resume_urls", "columns": ["claim_id", "claimant_email", "thread_id", "resume_url"], "values": [["={{$json.claimId}}", "={{$json.claimantEmail}}", "={{$node[\"Send Request Email\"].json.threadId}}", "={{$node[\"Wait for Reply\"].context.resumeWebhookUrl}}"]]}, "id": "4", "name": "Store Resume URL", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [780, 480], "credentials": {"supabaseApi": {"id": "supabase_cred", "name": "Supabase Project"}}}, {"parameters": {}, "id": "5", "name": "Continue Processing", "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [1040, 300]}], "connections": {"Start Trigger": {"main": [[{"node": "Send Request Email", "type": "main", "index": 0}]]}, "Send Request Email": {"main": [[{"node": "Wait for Reply", "type": "main", "index": 0}, {"node": "Store Resume URL", "type": "main", "index": 0}]]}, "Wait for Reply": {"main": [[{"node": "Continue Processing", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "id": "claim-document-request"}