service: ${self:custom.serviceName}

provider:
  name: aws
  runtime: nodejs14.x
  stage: ${opt:stage, "dev"}
  region: ${opt:region, "ca-central-1"}
  iamRoleStatements:
    - Effect: "Allow"
      Action:
        - ec2:RunInstances
        - ec2:DescribeInstances
        - ec2:TerminateInstances
        - ec2:StartInstances
        - ec2:StopInstances
        - ecs:CreateCluster
        - ecs:DescribeClusters
        - ecs:CreateService
        - ecs:UpdateService
        - ecs:DescribeServices
        - ecs:RunTask
        - ecs:StopTask
        - ecs:DeleteCluster
        - ecs:DeleteService
        - secretsmanager:GetSecretValue
        - cloudformation:CreateStack
        - cloudformation:UpdateStack
        - cloudformation:DescribeStacks
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - execute-api:Invoke
        - ec2:CreateFlowLogs
        - ec2:DescribeFlowLogs
        - ec2:*
      Resource: "*"

custom:
  serviceName: n8n-zurich-standalone
  ecrRepoUri: ${env:ECR_REPO_URI}
  domainName: ${env:domain-name}
  awsRegion: ${env:AWS_REGION}
  publicHostedZoneID: ${env:hosted-zone-id}
  acmCertificateArn: ${env:acm-cert-arn}

resources:
  Resources:
    # RDS PostgreSQL Database for N8N persistence
    DBSubnetGroup:
      Type: AWS::RDS::DBSubnetGroup
      Properties:
        DBSubnetGroupDescription: Subnet group for N8N RDS instance
        SubnetIds:
          - !Ref AZ1PrivateSubnet1
          - !Ref AZ2PrivateSubnet1
        Tags:
          - Key: Name
            Value: n8n-db-subnet-group-${self:provider.stage}

    RDSSecurityGroup:
      Type: AWS::EC2::SecurityGroup
      Properties:
        GroupDescription: Security group for N8N RDS PostgreSQL
        VpcId: !Ref VPC
        SecurityGroupIngress:
          - IpProtocol: tcp
            FromPort: 5432
            ToPort: 5432
            SourceSecurityGroupId: !Ref InstanceSecurityGroup
        Tags:
          - Key: Name
            Value: n8n-rds-sg-${self:provider.stage}



    DBSecret:
      Type: AWS::SecretsManager::Secret
      Properties:
        Name: ${self:service}-${self:provider.stage}-db-secret
        Description: N8N Database credentials
        GenerateSecretString:
          SecretStringTemplate: '{"username": "n8nuser"}'
          GenerateStringKey: 'password'
          PasswordLength: 32
          ExcludeCharacters: '"@/\'

    RDSInstance:
      Type: AWS::RDS::DBInstance
      DeletionPolicy: Snapshot
      Properties:
        DBInstanceIdentifier: ${self:service}-${self:provider.stage}-db
        DBInstanceClass: db.t3.micro
        Engine: postgres
        EngineVersion: '15'
        AllocatedStorage: 20
        StorageType: gp2
        StorageEncrypted: true
        DBName: n8n
        MasterUsername: '{{resolve:secretsmanager:n8n-zurich-standalone-dev-db-secret:SecretString:username}}'
        MasterUserPassword: '{{resolve:secretsmanager:n8n-zurich-standalone-dev-db-secret:SecretString:password}}'
        VPCSecurityGroups:
          - !Ref RDSSecurityGroup
        DBSubnetGroupName: !Ref DBSubnetGroup
        BackupRetentionPeriod: 7
        MultiAZ: false
        PubliclyAccessible: false
        DeletionProtection: false
        Tags:
          - Key: Name
            Value: n8n-database-${self:provider.stage}

    # EFS File System for N8N file persistence
    EFSFileSystem:
      Type: AWS::EFS::FileSystem
      Properties:
        PerformanceMode: generalPurpose
        Encrypted: true
        FileSystemTags:
          - Key: Name
            Value: n8n-efs-${self:provider.stage}

    EFSSecurityGroup:
      Type: AWS::EC2::SecurityGroup
      Properties:
        GroupDescription: Security group for N8N EFS
        VpcId: !Ref VPC
        SecurityGroupIngress:
          - IpProtocol: tcp
            FromPort: 2049
            ToPort: 2049
            SourceSecurityGroupId: !Ref InstanceSecurityGroup
        Tags:
          - Key: Name
            Value: n8n-efs-sg-${self:provider.stage}

    EFSMountTarget1:
      Type: AWS::EFS::MountTarget
      Properties:
        FileSystemId: !Ref EFSFileSystem
        SubnetId: !Ref AZ1PrivateSubnet1
        SecurityGroups:
          - !Ref EFSSecurityGroup

    EFSMountTarget2:
      Type: AWS::EFS::MountTarget
      Properties:
        FileSystemId: !Ref EFSFileSystem
        SubnetId: !Ref AZ2PrivateSubnet1
        SecurityGroups:
          - !Ref EFSSecurityGroup

    EFSAccessPoint:
      Type: AWS::EFS::AccessPoint
      Properties:
        FileSystemId: !Ref EFSFileSystem
        PosixUser:
          Uid: 1000
          Gid: 1000
        RootDirectory:
          Path: "/n8n"
          CreationInfo:
            OwnerUid: 1000
            OwnerGid: 1000
            Permissions: "0755"
        AccessPointTags:
          - Key: Name
            Value: n8n-efs-access-point-${self:provider.stage}

    ECSCluster:
      Type: AWS::ECS::Cluster
      Properties:
        ClusterName: ${self:service}-${self:provider.stage}-cluster
        CapacityProviders:
          - FARGATE
        DefaultCapacityProviderStrategy:
          - CapacityProvider: FARGATE
            Weight: 1

    ECRECSExecutionRole:
      Type: AWS::IAM::Role
      Properties:
        RoleName: ${self:service}-${self:provider.stage}-ecs-execution
        AssumeRolePolicyDocument:
          Version: "2012-10-17"
          Statement:
            - Effect: Allow
              Principal:
                Service: ecs-tasks.amazonaws.com
              Action: sts:AssumeRole
        Policies:
          - PolicyName: ${self:service}-${self:provider.stage}-policy
            PolicyDocument:
              Version: "2012-10-17"
              Statement:
                - Effect: Allow
                  Action:
                    - logs:CreateLogGroup
                    - logs:CreateLogStream
                    - logs:PutLogEvents
                    - dynamodb:*
                  Resource: "*"
                - Effect: Allow
                  Action:
                    - ecr:GetDownloadUrlForLayer
                  Resource: "*"
                - Effect: Allow
                  Action:
                    - ecr:GetAuthorizationToken
                  Resource: "*"
                - Effect: Allow
                  Action:
                    - ecr:InitiateLayerUpload
                  Resource: "*"
                - Effect: Allow
                  Action:
                    - ecr:BatchGetImage
                  Resource: "*"
                - Effect: Allow
                  Action: ec2:*
                  Resource: "*"
                - Effect: Allow
                  Action: ecs:*
                  Resource: "*"
                - Effect: Allow
                  Action: vpc:*
                  Resource: "*"
                - Effect: Allow
                  Action: ec2:AmazonEC2ContainerRegistryFullAccess
                  Resource: "*"
                - Effect: Allow
                  Action: ec2:AmazonElasticContainerRegistryPublicFullAccess
                  Resource: "*"
                - Effect: Allow
                  Action:
                    - execute-api:Invoke
                  Resource: "*"
                - Effect: Allow
                  Action:
                    - secretsmanager:GetSecretValue
                    - secretsmanager:DescribeSecret
                  Resource: "*"
                - Effect: Allow
                  Action:
                    - ssm:GetParameter
                    - ssm:GetParameters
                    - ssm:GetParametersByPath
                  Resource: "*"
                - Effect: Allow
                  Action:
                    - elasticfilesystem:ClientMount
                    - elasticfilesystem:ClientWrite
                    - elasticfilesystem:ClientRootAccess
                    - elasticfilesystem:DescribeFileSystems
                    - elasticfilesystem:DescribeAccessPoints
                  Resource: "*"

    CloudWatchLogsGroup:
      Type: AWS::Logs::LogGroup
      Properties:
        LogGroupName: "${self:service}-${self:provider.stage}"
        RetentionInDays: 30

    VPC:
      Type: AWS::EC2::VPC
      Properties:
        CidrBlock: 10.0.0.0/16
        EnableDnsSupport: true
        EnableDnsHostnames: true

    AZ1PublicSubnet1:
      Type: AWS::EC2::Subnet
      Properties:
        AvailabilityZone:
          Fn::Select:
            - 0
            - Fn::GetAZs: { Ref: "AWS::Region" }
        VpcId: !Ref "VPC"
        CidrBlock: 10.0.0.0/18
        MapPublicIpOnLaunch: true

    AZ2PublicSubnet1:
      Type: AWS::EC2::Subnet
      Properties:
        AvailabilityZone:
          Fn::Select:
            - 1
            - Fn::GetAZs: { Ref: "AWS::Region" }
        VpcId: !Ref "VPC"
        CidrBlock: *********/18
        MapPublicIpOnLaunch: true

    AZ1PrivateSubnet1:
      Type: AWS::EC2::Subnet
      Properties:
        AvailabilityZone:
          Fn::Select:
            - 0
            - Fn::GetAZs: { Ref: "AWS::Region" }
        VpcId: !Ref "VPC"
        CidrBlock: **********/18
        MapPublicIpOnLaunch: false

    AZ2PrivateSubnet1:
      Type: AWS::EC2::Subnet
      Properties:
        AvailabilityZone:
          Fn::Select:
            - 1
            - Fn::GetAZs: { Ref: "AWS::Region" }
        VpcId: !Ref "VPC"
        CidrBlock: **********/18
        MapPublicIpOnLaunch: false

    InternetGateway:
      Type: AWS::EC2::InternetGateway

    GatewayAttachement:
      Type: AWS::EC2::VPCGatewayAttachment
      Properties:
        VpcId: !Ref "VPC"
        InternetGatewayId: !Ref "InternetGateway"

    NatGatewayEIP:
      Type: AWS::EC2::EIP
      DependsOn: GatewayAttachement
      Properties:
        Domain: vpc

    NatGateway:
      Type: AWS::EC2::NatGateway
      Properties:
        AllocationId: !GetAtt NatGatewayEIP.AllocationId
        SubnetId: !Ref AZ1PublicSubnet1

    PublicRouteTable:
      Type: AWS::EC2::RouteTable
      Properties:
        VpcId: !Ref "VPC"

    RouteToInternet:
      Type: AWS::EC2::Route
      DependsOn: GatewayAttachement
      Properties:
        RouteTableId: !Ref "PublicRouteTable"
        DestinationCidrBlock: "0.0.0.0/0"
        GatewayId: !Ref "InternetGateway"

    PrivateRouteTable:
      Type: AWS::EC2::RouteTable
      Properties:
        VpcId: !Ref "VPC"

    PrivateRouteToNATGW:
      Type: AWS::EC2::Route
      Properties:
        RouteTableId: !Ref PrivateRouteTable
        DestinationCidrBlock: 0.0.0.0/0
        NatGatewayId: !Ref NatGateway

    AZ1PublicSubnet1RouteTableAssociation:
      Type: AWS::EC2::SubnetRouteTableAssociation
      Properties:
        SubnetId: !Ref AZ1PublicSubnet1
        RouteTableId: !Ref PublicRouteTable

    AZ2PublicSubnet1RouteTableAssociation:
      Type: AWS::EC2::SubnetRouteTableAssociation
      Properties:
        SubnetId: !Ref AZ2PublicSubnet1
        RouteTableId: !Ref PublicRouteTable

    AZ1PrivateSubnet1RouteTableAssociation:
      Type: AWS::EC2::SubnetRouteTableAssociation
      Properties:
        SubnetId: !Ref AZ1PrivateSubnet1
        RouteTableId: !Ref PrivateRouteTable

    AZ2PrivateSubnet1RouteTableAssociation:
      Type: AWS::EC2::SubnetRouteTableAssociation
      Properties:
        SubnetId: !Ref AZ2PrivateSubnet1
        RouteTableId: !Ref PrivateRouteTable

    InstanceSecurityGroup:
      Type: AWS::EC2::SecurityGroup
      Properties:
        GroupDescription: Allow HTTP and HTTPS traffic
        GroupName: ${self:service}-${self:provider.stage}-sg
        VpcId: !Ref VPC
        SecurityGroupIngress:
          - IpProtocol: -1
            CidrIp: 0.0.0.0/0
        SecurityGroupEgress:
          - IpProtocol: -1
            CidrIp: 0.0.0.0/0

    # Application Load Balancer (same pattern as other services)
    ApplicationLoadBalancer:
      Type: AWS::ElasticLoadBalancingV2::LoadBalancer
      Properties:
        Name: "${self:service}-${self:provider.stage}-alb"
        Type: "application"
        Scheme: "internet-facing"
        Subnets:
          - !Ref AZ1PublicSubnet1
          - !Ref AZ2PublicSubnet1
        SecurityGroups:
          - !Ref InstanceSecurityGroup
        LoadBalancerAttributes:
          - Key: "idle_timeout.timeout_seconds"
            Value: 4000

    # Target Group (exact same as rozie-air, only port changed to 5678)
    ElsaTargetGroup:
      Type: AWS::ElasticLoadBalancingV2::TargetGroup
      DependsOn: ApplicationLoadBalancer
      Properties:
        Name: "${self:service}-${self:provider.stage}-tg"
        Port: 5678
        Protocol: HTTP
        VpcId: !Ref VPC
        HealthCheckEnabled: true
        HealthCheckIntervalSeconds: 60
        HealthCheckPath: "/healthz"
        HealthCheckPort: "traffic-port"
        HealthCheckProtocol: HTTP
        HealthCheckTimeoutSeconds: 5
        HealthyThresholdCount: 2
        UnhealthyThresholdCount: 2
        IpAddressType: "ipv4"
        ProtocolVersion: "HTTP1"
        Matcher:
          HttpCode: "200"
        TargetType: "ip"

    # Load Balancer Listener (same pattern as other services)
    LoadBalancerListener:
      Type: AWS::ElasticLoadBalancingV2::Listener
      DependsOn:
        - ApplicationLoadBalancer
      Properties:
        LoadBalancerArn: !Ref ApplicationLoadBalancer
        Protocol: HTTPS
        Port: 443
        SslPolicy: "ELBSecurityPolicy-TLS13-1-2-2021-06"
        Certificates:
          - CertificateArn: ${self:custom.acmCertificateArn}
        DefaultActions:
          - Type: forward
            TargetGroupArn: !Ref ElsaTargetGroup

    LoadBalancerListenerForHTTP:
      Type: AWS::ElasticLoadBalancingV2::Listener
      DependsOn:
        - ApplicationLoadBalancer
        - LoadBalancerListener
      Properties:
        LoadBalancerArn: !Ref ApplicationLoadBalancer
        Protocol: HTTP
        Port: 80
        DefaultActions:
          - Type: forward
            TargetGroupArn: !Ref ElsaTargetGroup

    # Task Definition (same as rozie-air, only port changed to 5678)
    TaskDefinition:
      Type: AWS::ECS::TaskDefinition
      DependsOn:
        - CloudWatchLogsGroup
      Properties:
        Family: ${self:service}-${self:provider.stage}
        NetworkMode: awsvpc
        RuntimePlatform:
          CpuArchitecture: X86_64
          OperatingSystemFamily: LINUX
        RequiresCompatibilities:
          - FARGATE
        ExecutionRoleArn: !GetAtt ECRECSExecutionRole.Arn
        TaskRoleArn: !GetAtt ECRECSExecutionRole.Arn
        Cpu: 256
        Memory: 512
        Volumes:
          - Name: n8n-data
            EFSVolumeConfiguration:
              FileSystemId: !Ref EFSFileSystem
              AccessPointId: !Ref EFSAccessPoint
              TransitEncryption: ENABLED
        ContainerDefinitions:
          - Name: ${self:service}-${self:provider.stage}
            Image: ${self:custom.ecrRepoUri}
            Memory: 512
            Cpu: 256
            Essential: true
            PortMappings:
              - ContainerPort: 5678
                Protocol: "tcp"
            Environment:
              - Name: N8N_HOST
                Value: "0.0.0.0"
              - Name: N8N_PORT
                Value: "5678"
              - Name: N8N_PROTOCOL
                Value: "http"
              - Name: WEBHOOK_URL
                Value: "https://${self:custom.domainName}/"
              - Name: N8N_ENFORCE_SETTINGS_FILE_PERMISSIONS
                Value: "false"
              - Name: DB_TYPE
                Value: "postgresdb"
              - Name: DB_POSTGRESDB_HOST
                Value: !GetAtt RDSInstance.Endpoint.Address
              - Name: DB_POSTGRESDB_PORT
                Value: "5432"
              - Name: DB_POSTGRESDB_DATABASE
                Value: "n8n"
              - Name: DB_POSTGRESDB_USER
                Value: '{{resolve:secretsmanager:n8n-zurich-standalone-dev-db-secret:SecretString:username}}'
              - Name: DB_POSTGRESDB_PASSWORD
                Value: '{{resolve:secretsmanager:n8n-zurich-standalone-dev-db-secret:SecretString:password}}'
              - Name: DB_POSTGRESDB_SSL_ENABLED
                Value: "true"
              - Name: DB_POSTGRESDB_SSL_REJECT_UNAUTHORIZED
                Value: "false"
            MountPoints:
              - SourceVolume: n8n-data
                ContainerPath: /home/<USER>/.n8n
                ReadOnly: false
            LogConfiguration:
              LogDriver: awslogs
              Options:
                awslogs-group: "${self:service}-${self:provider.stage}"
                awslogs-region: !Ref AWS::Region
                awslogs-stream-prefix: ecs

    # ECS Service (exact same as rozie-air, only container port changed to 5678)
    EcrEcsSlsTask:
      Type: AWS::ECS::Service
      DependsOn:
        - TaskDefinition
        - ElsaTargetGroup
        - ApplicationLoadBalancer
        - LoadBalancerListener
      Properties:
        Cluster: !Ref ECSCluster
        DesiredCount: 1
        TaskDefinition: !Ref TaskDefinition
        ServiceName: ${self:service}-${self:provider.stage}-service
        PlatformVersion: LATEST
        EnableECSManagedTags: true
        DeploymentConfiguration:
          MaximumPercent: 200
          MinimumHealthyPercent: 70
          Alarms:
            AlarmNames:
              - "${self:service}-${self:provider.stage}-service-alarm"
            Enable: true
            Rollback: true
          DeploymentCircuitBreaker:
            Enable: false
            Rollback: false
        NetworkConfiguration:
          AwsvpcConfiguration:
            SecurityGroups:
              - !Ref InstanceSecurityGroup
            Subnets:
              - !Ref AZ1PrivateSubnet1
              - !Ref AZ2PrivateSubnet1
        CapacityProviderStrategy:
          - CapacityProvider: FARGATE
            Weight: 1
        LoadBalancers:
          - ContainerName: ${self:service}-${self:provider.stage}
            ContainerPort: 5678
            TargetGroupArn: !Ref ElsaTargetGroup




    # Route53 DNS Record (same pattern as other services)
    APIRecordSet:
      Type: AWS::Route53::RecordSet
      Properties:
        HostedZoneId: ${self:custom.publicHostedZoneID}
        Name: ${self:custom.domainName}
        Type: A
        AliasTarget:
          DNSName: !GetAtt 'ApplicationLoadBalancer.DNSName'
          HostedZoneId: !GetAtt 'ApplicationLoadBalancer.CanonicalHostedZoneID'
          EvaluateTargetHealth: false

  Outputs:
    LoadBalancerDNS:
      Description: DNS name of the load balancer
      Value: !GetAtt ApplicationLoadBalancer.DNSName
      Export:
        Name: LoadBalancerDNS-${self:provider.stage}

    ClusterName:
      Description: Name of the ECS cluster
      Value: !Ref ECSCluster
      Export:
        Name: ClusterName-${self:provider.stage}

    RDSEndpoint:
      Description: RDS PostgreSQL endpoint for N8N
      Value: !GetAtt RDSInstance.Endpoint.Address
      Export:
        Name: N8NRDSEndpoint-${self:provider.stage}

    EFSFileSystemId:
      Description: EFS File System ID for N8N data persistence
      Value: !Ref EFSFileSystem
      Export:
        Name: N8NEFSFileSystemId-${self:provider.stage}

    EFSAccessPointId:
      Description: EFS Access Point ID for N8N
      Value: !Ref EFSAccessPoint
      Export:
        Name: N8NEFSAccessPointId-${self:provider.stage}

    DatabaseSecretArn:
      Description: ARN of the database credentials secret
      Value: !Ref DBSecret
      Export:
        Name: N8NDBSecretArn-${self:provider.stage}

    N8NURL:
      Description: N8N Application URL
      Value: !Sub "https://${self:custom.domainName}"
      Export:
        Name: N8NURL-${self:provider.stage}
