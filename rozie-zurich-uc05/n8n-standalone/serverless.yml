service: ${self:custom.serviceName}

provider:
  name: aws
  runtime: nodejs14.x
  stage: ${opt:stage, "dev"}
  region: ${opt:region, "ca-central-1"}
  iamRoleStatements:
    - Effect: "Allow"
      Action:
        - ec2:RunInstances
        - ec2:DescribeInstances
        - ec2:TerminateInstances
        - ec2:StartInstances
        - ec2:StopInstances
        - ecs:CreateCluster
        - ecs:DescribeClusters
        - ecs:CreateService
        - ecs:UpdateService
        - ecs:DescribeServices
        - ecs:RunTask
        - ecs:StopTask
        - ecs:DeleteCluster
        - ecs:DeleteService
        - secretsmanager:GetSecretValue
        - cloudformation:CreateStack
        - cloudformation:UpdateStack
        - cloudformation:DescribeStacks
        - logs:CreateLogGroup
        - logs:CreateLogStream
        - logs:PutLogEvents
        - execute-api:Invoke
        - ec2:CreateFlowLogs
        - ec2:DescribeFlowLogs
        - ec2:*
      Resource: "*"

custom:
  serviceName: n8n-zurich-standalone
  ecrRepoUri: ${env:ECR_REPO_URI}
  domainName: ${env:domain-name}
  awsRegion: ${env:AWS_REGION}
  publicHostedZoneID: ${env:hosted-zone-id}
  acmCertificateArn: ${env:acm-cert-arn}

resources:
  Resources:
    ECSCluster:
      Type: AWS::ECS::Cluster
      Properties:
        ClusterName: ${self:service}-${self:provider.stage}-cluster
        CapacityProviders:
          - FARGATE
        DefaultCapacityProviderStrategy:
          - CapacityProvider: FARGATE
            Weight: 1

    ECRECSExecutionRole:
      Type: AWS::IAM::Role
      Properties:
        RoleName: ${self:service}-${self:provider.stage}-ecs-execution
        AssumeRolePolicyDocument:
          Version: "2012-10-17"
          Statement:
            - Effect: Allow
              Principal:
                Service: ecs-tasks.amazonaws.com
              Action: sts:AssumeRole
        Policies:
          - PolicyName: ${self:service}-${self:provider.stage}-policy
            PolicyDocument:
              Version: "2012-10-17"
              Statement:
                - Effect: Allow
                  Action:
                    - logs:CreateLogGroup
                    - logs:CreateLogStream
                    - logs:PutLogEvents
                    - dynamodb:*
                  Resource: "*"
                - Effect: Allow
                  Action:
                    - ecr:GetDownloadUrlForLayer
                  Resource: "*"
                - Effect: Allow
                  Action:
                    - ecr:GetAuthorizationToken
                  Resource: "*"
                - Effect: Allow
                  Action:
                    - ecr:InitiateLayerUpload
                  Resource: "*"
                - Effect: Allow
                  Action:
                    - ecr:BatchGetImage
                  Resource: "*"
                - Effect: Allow
                  Action: ec2:*
                  Resource: "*"
                - Effect: Allow
                  Action: ecs:*
                  Resource: "*"
                - Effect: Allow
                  Action: vpc:*
                  Resource: "*"
                - Effect: Allow
                  Action: ec2:AmazonEC2ContainerRegistryFullAccess
                  Resource: "*"
                - Effect: Allow
                  Action: ec2:AmazonElasticContainerRegistryPublicFullAccess
                  Resource: "*"
                - Effect: Allow
                  Action:
                    - execute-api:Invoke
                  Resource: "*"
                - Effect: Allow
                  Action:
                    - secretsmanager:GetSecretValue
                  Resource: "*"

    CloudWatchLogsGroup:
      Type: AWS::Logs::LogGroup
      Properties:
        LogGroupName: "${self:service}-${self:provider.stage}"
        RetentionInDays: 30

    VPC:
      Type: AWS::EC2::VPC
      DependsOn: InternetGateway
      Properties:
        CidrBlock: 10.0.0.0/16
        EnableDnsSupport: true
        EnableDnsHostnames: true

    # Public Subnets
    N8NPublicSubnet1:
      Type: AWS::EC2::Subnet
      Properties:
        VpcId: !Ref N8NVPC
        CidrBlock: ********/24
        AvailabilityZone: !Select [0, !GetAZs '']
        MapPublicIpOnLaunch: true
        Tags:
          - Key: Name
            Value: n8n-zurich-public-subnet-1-${self:provider.stage}

    N8NPublicSubnet2:
      Type: AWS::EC2::Subnet
      Properties:
        VpcId: !Ref N8NVPC
        CidrBlock: ********/24
        AvailabilityZone: !Select [1, !GetAZs '']
        MapPublicIpOnLaunch: true
        Tags:
          - Key: Name
            Value: n8n-zurich-public-subnet-2-${self:provider.stage}

    # Internet Gateway
    N8NInternetGateway:
      Type: AWS::EC2::InternetGateway
      Properties:
        Tags:
          - Key: Name
            Value: n8n-zurich-igw-${self:provider.stage}

    N8NInternetGatewayAttachment:
      Type: AWS::EC2::VPCGatewayAttachment
      Properties:
        VpcId: !Ref N8NVPC
        InternetGatewayId: !Ref N8NInternetGateway

    # Route Table
    N8NPublicRouteTable:
      Type: AWS::EC2::RouteTable
      Properties:
        VpcId: !Ref N8NVPC
        Tags:
          - Key: Name
            Value: n8n-zurich-public-rt-${self:provider.stage}

    N8NDefaultPublicRoute:
      Type: AWS::EC2::Route
      DependsOn: N8NInternetGatewayAttachment
      Properties:
        RouteTableId: !Ref N8NPublicRouteTable
        DestinationCidrBlock: 0.0.0.0/0
        GatewayId: !Ref N8NInternetGateway

    N8NPublicSubnet1RouteTableAssociation:
      Type: AWS::EC2::SubnetRouteTableAssociation
      Properties:
        RouteTableId: !Ref N8NPublicRouteTable
        SubnetId: !Ref N8NPublicSubnet1

    N8NPublicSubnet2RouteTableAssociation:
      Type: AWS::EC2::SubnetRouteTableAssociation
      Properties:
        RouteTableId: !Ref N8NPublicRouteTable
        SubnetId: !Ref N8NPublicSubnet2

    # Security Groups
    N8NSecurityGroup:
      Type: AWS::EC2::SecurityGroup
      Properties:
        GroupDescription: Security group for N8N ECS service
        VpcId: !Ref N8NVPC
        SecurityGroupIngress:
          - IpProtocol: tcp
            FromPort: 5678
            ToPort: 5678
            SourceSecurityGroupId: !Ref N8NLoadBalancerSecurityGroup
        Tags:
          - Key: Name
            Value: n8n-zurich-sg-${self:provider.stage}

    N8NLoadBalancerSecurityGroup:
      Type: AWS::EC2::SecurityGroup
      Properties:
        GroupDescription: Security group for N8N Load Balancer
        VpcId: !Ref N8NVPC
        SecurityGroupIngress:
          - IpProtocol: tcp
            FromPort: 80
            ToPort: 80
            CidrIp: 0.0.0.0/0
          - IpProtocol: tcp
            FromPort: 443
            ToPort: 443
            CidrIp: 0.0.0.0/0
        Tags:
          - Key: Name
            Value: n8n-zurich-alb-sg-${self:provider.stage}

    # Application Load Balancer
    N8NLoadBalancer:
      Type: AWS::ElasticLoadBalancingV2::LoadBalancer
      Properties:
        Name: n8n-zurich-alb-${self:provider.stage}
        Scheme: internet-facing
        Type: application
        Subnets:
          - !Ref N8NPublicSubnet1
          - !Ref N8NPublicSubnet2
        SecurityGroups:
          - !Ref N8NLoadBalancerSecurityGroup

    # Target Group
    N8NTargetGroup:
      Type: AWS::ElasticLoadBalancingV2::TargetGroup
      Properties:
        Name: n8n-zurich-tg-${self:provider.stage}
        Port: 5678
        Protocol: HTTP
        VpcId: !Ref N8NVPC
        TargetType: ip
        HealthCheckPath: /healthz
        HealthCheckProtocol: HTTP
        HealthCheckIntervalSeconds: 30
        HealthCheckTimeoutSeconds: 5
        HealthyThresholdCount: 2
        UnhealthyThresholdCount: 3

    # Load Balancer Listener
    N8NLoadBalancerListener:
      Type: AWS::ElasticLoadBalancingV2::Listener
      Properties:
        DefaultActions:
          - Type: forward
            TargetGroupArn: !Ref N8NTargetGroup
        LoadBalancerArn: !Ref N8NLoadBalancer
        Port: 80
        Protocol: HTTP

    # ECS Task Definition
    N8NTaskDefinition:
      Type: AWS::ECS::TaskDefinition
      Properties:
        Family: n8n-zurich-${self:provider.stage}
        NetworkMode: awsvpc
        RequiresCompatibilities:
          - FARGATE
        Cpu: 512
        Memory: 1024
        ExecutionRoleArn: !Ref N8NExecutionRole
        TaskRoleArn: !Ref N8NTaskRole
        ContainerDefinitions:
          - Name: n8n
            Image: n8nio/n8n:latest
            Essential: true
            PortMappings:
              - ContainerPort: 5678
                Protocol: tcp
            Environment:
              - Name: N8N_HOST
                Value: ${self:custom.domainName}
              - Name: N8N_PORT
                Value: "443"
              - Name: N8N_PROTOCOL
                Value: https
              - Name: N8N_EDITOR_BASE_URL
                Value: https://${self:custom.domainName}
              - Name: WEBHOOK_URL
                Value: https://${self:custom.domainName}
              - Name: N8N_BASIC_AUTH_ACTIVE
                Value: "false"
              - Name: N8N_LISTEN_ADDRESS
                Value: "0.0.0.0"
              - Name: N8N_DISABLE_UI
                Value: "false"
              - Name: N8N_METRICS
                Value: "true"
              - Name: N8N_LOG_LEVEL
                Value: info
              - Name: GENERIC_TIMEZONE
                Value: UTC
            LogConfiguration:
              LogDriver: awslogs
              Options:
                awslogs-group: !Ref N8NLogGroup
                awslogs-region: !Ref AWS::Region
                awslogs-stream-prefix: n8n

    # ECS Service
    N8NService:
      Type: AWS::ECS::Service
      DependsOn: N8NLoadBalancerListener
      Properties:
        ServiceName: n8n-zurich-${self:provider.stage}
        Cluster: !Ref N8NCluster
        TaskDefinition: !Ref N8NTaskDefinition
        DesiredCount: 1
        LaunchType: FARGATE
        NetworkConfiguration:
          AwsvpcConfiguration:
            SecurityGroups:
              - !Ref N8NSecurityGroup
            Subnets:
              - !Ref N8NPublicSubnet1
              - !Ref N8NPublicSubnet2
            AssignPublicIp: ENABLED
        LoadBalancers:
          - ContainerName: n8n
            ContainerPort: 5678
            TargetGroupArn: !Ref N8NTargetGroup

    # IAM Roles
    N8NExecutionRole:
      Type: AWS::IAM::Role
      Properties:
        AssumeRolePolicyDocument:
          Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Principal:
                Service: ecs-tasks.amazonaws.com
              Action: sts:AssumeRole
        ManagedPolicyArns:
          - arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy

    N8NTaskRole:
      Type: AWS::IAM::Role
      Properties:
        AssumeRolePolicyDocument:
          Version: '2012-10-17'
          Statement:
            - Effect: Allow
              Principal:
                Service: ecs-tasks.amazonaws.com
              Action: sts:AssumeRole

    # CloudWatch Log Group
    N8NLogGroup:
      Type: AWS::Logs::LogGroup
      Properties:
        LogGroupName: /ecs/n8n-zurich-${self:provider.stage}
        RetentionInDays: 7

  Outputs:
    N8NLoadBalancerDNS:
      Description: DNS name of the N8N load balancer
      Value: !GetAtt N8NLoadBalancer.DNSName
      Export:
        Name: N8NLoadBalancerDNS-${self:provider.stage}
        
    N8NClusterName:
      Description: Name of the ECS cluster
      Value: !Ref N8NCluster
      Export:
        Name: N8NClusterName-${self:provider.stage}
