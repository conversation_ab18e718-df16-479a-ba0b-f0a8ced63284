# Development Environment Configuration for Zurich Workflow
# This file contains environment-specific settings for the dev stage

# Service Configuration
service_name: rozieai-zrh-uc05
stage: dev
region: ca-central-1

# ECR Configuration
ecr_repository: rozieai-zrh-uc05-dev
image_tag: latest

# Domain Configuration
domain_name: rozieai-zrh-uc05.dev-scc-demo.rozie.ai
hosted_zone_id: ${env:HOSTED_ZONE_ID}
acm_cert_arn: ${env:ACM_CERT_ARN}

# ECS Configuration
ecs:
  cluster_name: rozieai-zrh-uc05-dev-cluster
  service_name: rozieai-zrh-uc05-dev-service
  task_family: rozieai-zrh-uc05-dev
  cpu: 1024
  memory: 2048
  desired_count: 1

# Load Balancer Configuration
alb:
  name: rozieai-zrh-uc05-dev-alb
  target_group_name: rozieai-zrh-uc05-dev-tg
  health_check_path: /health
  health_check_interval: 60
  health_check_timeout: 5
  healthy_threshold: 2
  unhealthy_threshold: 2

# VPC Configuration
vpc:
  cidr_block: 10.0.0.0/16
  public_subnet_1_cidr: 10.0.0.0/18
  public_subnet_2_cidr: *********/18
  private_subnet_1_cidr: **********/18
  private_subnet_2_cidr: **********/18

# Security Group Configuration
security_group:
  name: rozieai-zrh-uc05-dev-sg
  description: Allow HTTP and HTTPS traffic for Zurich Workflow

# CloudWatch Configuration
cloudwatch:
  log_group: rozieai-zrh-uc05-dev
  retention_days: 30

# Environment Variables for Container
environment:
  FASTAPI_ENV: development
  LOG_LEVEL: INFO
  PORT: 8000
  BAML_ENVIRONMENT: development
  
  # Supabase Configuration
  SUPABASE_URL: https://tlduggpohclrgxbvuzhd.supabase.co
  SUPABASE_ANON_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Eud_SDfGulErh3yhqjaIMqM37eghuz-PVeRxknzxRfk
  
  # Support System Configuration
  SUPPORT_SUBDOMAIN: d3v-rozieai5417
  SUPPORT_EMAIL: <EMAIL>

# Secrets (to be stored in AWS Secrets Manager)
secrets:
  secret_name: rozieai-zrh-uc05-dev-secrets
  keys:
    - OPENAI_API_KEY
    - SUPPORT_TOKEN
    - SUPABASE_SERVICE_ROLE_KEY
    - JWT_SECRET
    - SESSION_SECRET

# N8N Configuration
n8n:
  enabled: true
  image: n8nio/n8n:latest
  port: 5678
  cpu: 512
  memory: 1024
  environment:
    N8N_HOST: 0.0.0.0
    N8N_PORT: 5678
    N8N_PROTOCOL: http
    WEBHOOK_URL: http://rozieai-zrh-uc05.dev-scc-demo.rozie.ai:5678
    N8N_EDITOR_BASE_URL: http://rozieai-zrh-uc05.dev-scc-demo.rozie.ai:5678
    
# Frontend Configuration
frontend:
  enabled: true
  image_name: rozieai-zrh-uc05-frontend-dev
  port: 80
  cpu: 256
  memory: 512

# Dashboard Configuration  
dashboard:
  enabled: true
  image_name: rozieai-zrh-uc05-dashboard-dev
  port: 2000
  cpu: 256
  memory: 512
  environment:
    PORT: 2000
    ZENDESK_SUBDOMAIN: d3v-rozieai5417
    ZENDESK_EMAIL: <EMAIL>

# Deployment Configuration
deployment:
  max_percent: 200
  min_healthy_percent: 70
  enable_circuit_breaker: false
  enable_rollback: true

# Monitoring Configuration
monitoring:
  enable_container_insights: true
  enable_service_discovery: true
  
# Tags
tags:
  Environment: dev
  Project: zurich-workflow
  Service: rozieai-zrh-uc05
  Owner: rozie-ai
  CostCenter: development
