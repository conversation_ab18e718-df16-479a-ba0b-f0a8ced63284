#!/bin/bash

# =============================================================================
# 🔐 AWS SYSTEMS MANAGER PARAMETER STORE SETUP
# =============================================================================
# This script sets up secure parameter storage for Zurich Workflow credentials
# Run this ONCE before deployment to store all secrets securely in AWS

set -e

echo "🔐 Setting up AWS Systems Manager Parameter Store for Zurich Workflow..."
echo "📍 Region: ca-central-1"
echo ""

# Check if AWS CLI is configured
if ! aws sts get-caller-identity &> /dev/null; then
    echo "❌ AWS CLI not configured. Please run 'aws configure' first."
    exit 1
fi

echo "✅ AWS CLI configured. Proceeding with parameter setup..."
echo ""

# =============================================================================
# 🤖 OPENAI CONFIGURATION
# =============================================================================
echo "📝 Setting up OpenAI API Key..."
aws ssm put-parameter \
    --region ca-central-1 \
    --name "/zurich-workflow/openai-api-key" \
    --value "********************************************************************************************************************************************************************" \
    --type "SecureString" \
    --description "OpenAI API Key for Zurich Workflow" \
    --overwrite

# =============================================================================
# 🗄️ SUPABASE CONFIGURATION
# =============================================================================
echo "📝 Setting up Supabase URL..."
aws ssm put-parameter \
    --region ca-central-1 \
    --name "/zurich-workflow/supabase-url" \
    --value "https://tlduggpohclrgxbvuzhd.supabase.co" \
    --type "String" \
    --description "Supabase Project URL" \
    --overwrite

echo "📝 Setting up Supabase Anon Key..."
aws ssm put-parameter \
    --region ca-central-1 \
    --name "/zurich-workflow/supabase-anon-key" \
    --value "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Eud_SDfGulErh3yhqjaIMqM37eghuz-PVeRxknzxRfk" \
    --type "String" \
    --description "Supabase Anonymous Key" \
    --overwrite

echo "📝 Setting up Supabase Service Role Key..."
aws ssm put-parameter \
    --region ca-central-1 \
    --name "/zurich-workflow/supabase-service-role-key" \
    --value "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRsZHVnZ3BvaGNscmd4YnZ1emhkIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDcwMTAzOCwiZXhwIjoyMDY2Mjc3MDM4fQ.wDi5bG1O81a3HaXOujWshJZoGV6AhFgEuaoVcjam0SE" \
    --type "SecureString" \
    --description "Supabase Service Role Key" \
    --overwrite

# =============================================================================
# 🎫 SUPPORT SYSTEM CONFIGURATION
# =============================================================================
echo "📝 Setting up Support System credentials..."
aws ssm put-parameter \
    --region ca-central-1 \
    --name "/zurich-workflow/support-subdomain" \
    --value "d3v-rozieai5417" \
    --type "String" \
    --description "Support System Subdomain" \
    --overwrite

aws ssm put-parameter \
    --region ca-central-1 \
    --name "/zurich-workflow/support-email" \
    --value "<EMAIL>" \
    --type "String" \
    --description "Support System Email" \
    --overwrite

aws ssm put-parameter \
    --region ca-central-1 \
    --name "/zurich-workflow/support-token" \
    --value "1gfty1sXmithKfIhjwWnUWmTXLrxlVAJEiGrNcF1" \
    --type "SecureString" \
    --description "Support System API Token" \
    --overwrite

echo ""
echo "✅ All parameters successfully stored in AWS Systems Manager Parameter Store!"
echo ""
echo "📋 Parameters created:"
echo "  🤖 /zurich-workflow/openai-api-key (SecureString)"
echo "  🗄️ /zurich-workflow/supabase-url (String)"
echo "  🗄️ /zurich-workflow/supabase-anon-key (String)"
echo "  🗄️ /zurich-workflow/supabase-service-role-key (SecureString)"
echo "  🎫 /zurich-workflow/support-subdomain (String)"
echo "  🎫 /zurich-workflow/support-email (String)"
echo "  🎫 /zurich-workflow/support-token (SecureString)"
echo ""
echo "🚀 Ready for deployment! Run: cd rozie-zurich-uc05 && sls deploy --stage dev"
echo ""
echo "💡 To view parameters: aws ssm get-parameters-by-path --path '/zurich-workflow' --region ca-central-1"
echo "🗑️ To delete all parameters: aws ssm delete-parameters --names \$(aws ssm get-parameters-by-path --path '/zurich-workflow' --region ca-central-1 --query 'Parameters[].Name' --output text) --region ca-central-1"
