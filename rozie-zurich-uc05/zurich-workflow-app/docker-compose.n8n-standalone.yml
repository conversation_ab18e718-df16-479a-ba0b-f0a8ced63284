version: '3.8'

# =============================================================================
# STANDALONE N8N DEPLOYMENT
# =============================================================================
# This runs N8N directly without nginx reverse proxy to eliminate
# path prefix issues and WebSocket connection problems
# =============================================================================

services:
  # =============================================================================
  # N8N WORKFLOW AUTOMATION (STANDALONE)
  # =============================================================================
  n8n-standalone:
    image: n8nio/n8n:latest
    container_name: n8n-standalone
    restart: unless-stopped
    ports:
      - "5678:5678"
    user: "1000:1000"
    volumes:
      - n8n_standalone_data:/home/<USER>/.n8n
    environment:
      # Clean N8N Configuration - No Path Prefix
      - N8N_HOST=rozieai-zrh-uc05.dev-scc-demo.rozie.ai
      - N8N_PORT=443
      - N8N_PROTOCOL=https
      - N8N_EDITOR_BASE_URL=https://rozieai-zrh-uc05.dev-scc-demo.rozie.ai
      - WEBHOOK_URL=https://rozieai-zrh-uc05.dev-scc-demo.rozie.ai
      - N8N_BASIC_AUTH_ACTIVE=false
      - N8N_LISTEN_ADDRESS=0.0.0.0
      - N8N_DISABLE_UI=false
      - N8N_METRICS=true
      - N8N_SECURE_COOKIE=false
      - N8N_LOG_LEVEL=info
      - GENERIC_TIMEZONE=UTC
      - N8N_TEMPLATES_ENABLED=true
      - N8N_USER_FOLDER=/home/<USER>
      - N8N_SKIP_WEBHOOK_DEREGISTRATION_SHUTDOWN=true
    networks:
      - n8n-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:5678/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

# =============================================================================
# NETWORKS
# =============================================================================
networks:
  n8n-network:
    driver: bridge

# =============================================================================
# VOLUMES
# =============================================================================
volumes:
  n8n_standalone_data:
    driver: local
