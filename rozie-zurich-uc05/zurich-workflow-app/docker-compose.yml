# DigitalOcean App Platform Compatible Docker Compose
# Optimized for n8n + Zurich Workflow App deployment

services:
  # =============================================================================
  # BACKEND SERVICES
  # =============================================================================
  
  # Main Backend API with AI Explainability
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: zurich-backend
    ports:
      - "8000:8000"
    environment:
      # OpenAI API Configuration
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      
      # Service Configuration
      - SERVICE_NAME=zurich-backend
      - PORT=8000
      
      # BAML Configuration
      - BAML_ENVIRONMENT=${BAML_ENVIRONMENT:-development}
      
      # FastAPI Configuration
      - FASTAPI_ENV=${FASTAPI_ENV:-development}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      
      # Supabase Configuration (updated credentials)
      - SUPABASE_URL=https://tlduggpohclrgxbvuzhd.supabase.co
      - SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Eud_SDfGulErh3yhqjaIMqM37eghuz-PVeRxknzxRfk
      - SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.wDi5bG1O81a3HaXOujWshJZoGV6AhFgEuaoVcjam0SE
      - SUPABASE_BUCKET_NAME=claims-attachments
      
      # AI Explainability Configuration
      - AI_EXPLAINABILITY_ENABLED=true
      - AI_MODEL_PROVIDER=openai
      - AI_MODEL_NAME=gpt-4o

      # Support System API Configuration
      - SUPPORT_SUBDOMAIN=d3v-rozieai5417
      - SUPPORT_EMAIL=<EMAIL>
      - SUPPORT_TOKEN=${SUPPORT_TOKEN:-1gfty1sXmithKfIhjwWnUWmTXLrxlVAJEiGrNcF1}
      
    command: uvicorn backend.src.api.main:app --host 0.0.0.0 --port 8000 --reload
    volumes:
      # Mount logs directory for persistent logging
      - ./logs:/app/logs
      
      # Development mode: mount source code for hot reloading
      - ./backend:/app/backend
      - ./baml_models:/app/baml_models
      
    networks:
      - zurich-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # =============================================================================
  # FRONTEND SERVICES
  # =============================================================================
  
  # Explainable Claims Dashboard Frontend
  frontend:
    build:
      context: .
      dockerfile: frontend.Dockerfile
    container_name: zurich-frontend
    ports:
      - "3000:3000"
    environment:
      # Backend configuration
      - BACKEND_URL=http://backend:8000
      - API_BASE_URL=http://backend:8000/api

      # Supabase configuration for frontend
      - SUPABASE_URL=https://tlduggpohclrgxbvuzhd.supabase.co
      - SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Eud_SDfGulErh3yhqjaIMqM37eghuz-PVeRxknzxRfk

      # Feature flags
      - AI_EXPLAINABILITY_ENABLED=true
      - REAL_TIME_UPDATES_ENABLED=true

    depends_on:
      - backend
    networks:
      - zurich-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 10s

  # Support Dashboard
  support-dashboard:
    build:
      context: ./zurich-dashboard
      dockerfile: Dockerfile
    container_name: zurich-support-dashboard
    ports:
      - "2000:2000"
    environment:
      # Application Configuration
      - NODE_ENV=production
      - PORT=2000

      # Support System API Configuration
      - SUPPORT_SUBDOMAIN=d3v-rozieai5417
      - SUPPORT_EMAIL=<EMAIL>
      - SUPPORT_TOKEN=${SUPPORT_TOKEN:-1gfty1sXmithKfIhjwWnUWmTXLrxlVAJEiGrNcF1}

  # N8N Workflow Automation
  n8n:
    build:
      context: .
      dockerfile: n8n.Dockerfile
    container_name: zurich-n8n
    ports:
      - "5678:5678"
    user: "1000:1000"
    volumes:
      - n8n_data:/home/<USER>/.n8n
    environment:
      # Official AWS ECS Fargate N8N Configuration
      - N8N_PATH=/n8n/
      - N8N_HOST=0.0.0.0
      - N8N_PORT=5678
      - N8N_PROTOCOL=https
      - N8N_EDITOR_BASE_URL=https://rozieai-zrh-uc05.dev-scc-demo.rozie.ai/n8n/
      - WEBHOOK_URL=https://rozieai-zrh-uc05.dev-scc-demo.rozie.ai/n8n/
      - N8N_BASIC_AUTH_ACTIVE=false
      - N8N_LISTEN_ADDRESS=0.0.0.0
      - N8N_DISABLE_UI=false
      - N8N_METRICS=true
      - N8N_SECURE_COOKIE=false
      - N8N_SKIP_WEBHOOK_DEREGISTRATION_SHUTDOWN=true
      - N8N_LOG_LEVEL=info
      - GENERIC_TIMEZONE=UTC
      - N8N_TEMPLATES_ENABLED=true
      - N8N_USER_FOLDER=/home/<USER>
    networks:
      - zurich-network
    restart: unless-stopped
    # Health check disabled for production stability
    # N8N takes time to initialize and may show false negatives
    # healthcheck:
    #   test: ["CMD", "curl", "-f", "http://localhost:5678/"]
    #   interval: 30s
    #   timeout: 10s
    #   retries: 5
    #   start_period: 60s

  # =============================================================================
  # REVERSE PROXY & LOAD BALANCER
  # =============================================================================
  
  # Nginx Reverse Proxy for Production
  proxy:
    image: nginx:alpine
    container_name: zurich-proxy
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx-production.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - backend
      - frontend
      - support-dashboard
    networks:
      - zurich-network
    restart: unless-stopped
    profiles:
      - production
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 5s
      retries: 3

  # =============================================================================
  # MONITORING & OBSERVABILITY
  # =============================================================================
  
  # Application Monitoring (optional)
  monitoring:
    image: prom/prometheus:latest
    container_name: zurich-monitoring
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - zurich-network
    restart: unless-stopped
    profiles:
      - monitoring

  # Log Aggregation (optional)
  logging:
    image: grafana/loki:latest
    container_name: zurich-logging
    ports:
      - "3100:3100"
    volumes:
      - loki_data:/loki
      - ./monitoring/loki-config.yml:/etc/loki/local-config.yaml
    command: -config.file=/etc/loki/local-config.yaml
    networks:
      - zurich-network
    restart: unless-stopped
    profiles:
      - monitoring

  # =============================================================================
  # DEVELOPMENT TOOLS
  # =============================================================================
  
  # Database UI (optional - for development)
  database-ui:
    image: supabase/studio:latest
    container_name: zurich-database-ui
    ports:
      - "3001:3000"
    environment:
      - SUPABASE_URL=https://tlduggpohclrgxbvuzhd.supabase.co
      - SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Eud_SDfGulErh3yhqjaIMqM37eghuz-PVeRxknzxRfk
    networks:
      - zurich-network
    restart: unless-stopped
    profiles:
      - development

  # =============================================================================
  # CACHE & SESSION STORAGE
  # =============================================================================
  
  # Redis for Caching (optional)
  cache:
    image: redis:7-alpine
    container_name: zurich-cache
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - zurich-network
    restart: unless-stopped
    profiles:
      - cache
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 5s
      retries: 3

# =============================================================================
# NETWORK CONFIGURATION
# =============================================================================

networks:
  zurich-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# =============================================================================
# VOLUME CONFIGURATION
# =============================================================================

volumes:
  # Application logs
  zurich_logs:
    driver: local
    
  # Monitoring data
  prometheus_data:
    driver: local
    
  loki_data:
    driver: local
    
  # Cache data
  redis_data:
    driver: local

  # N8N data persistence
  n8n_data:
    driver: local