# =============================================================================
# 🔑 ZURICH WORKFLOW APP - ENVIRONMENT CONFIGURATION TEMPLATE
# =============================================================================
# Copy this file to .env and fill in your actual values
# NEVER commit .env to version control!

# =============================================================================
# 🤖 OPENAI API CONFIGURATION
# =============================================================================
OPENAI_API_KEY=sk-your-openai-api-key-here

# =============================================================================
# 🗄️ SUPABASE CONFIGURATION
# =============================================================================
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-supabase-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key-here

# =============================================================================
# 🎫 SUPPORT SYSTEM CONFIGURATION
# =============================================================================
# Support system API credentials (generic naming for ticketing system)
SUPPORT_SUBDOMAIN=d3v-rozieai5417
SUPPORT_EMAIL=<EMAIL>
SUPPORT_TOKEN=your-NEW-support-api-token-here

# =============================================================================
# 📧 EMAIL CLASSIFICATION CONFIG
# =============================================================================
BAML_ENVIRONMENT=development

# =============================================================================
# 🚀 FASTAPI CONFIGURATION
# =============================================================================
FASTAPI_ENV=development
LOG_LEVEL=INFO

# =============================================================================
# 🔐 SECURITY CONFIGURATION
# =============================================================================
JWT_SECRET=your-jwt-secret-here
SESSION_SECRET=your-session-secret-here

# =============================================================================
# 🌐 DEPLOYMENT CONFIGURATION
# =============================================================================
# Domain for production deployment
DOMAIN_NAME=your-domain.com

# AWS Configuration (for deployment)
AWS_REGION=us-east-1
AWS_ACCOUNT_ID=************

# =============================================================================
# 📝 INSTRUCTIONS
# =============================================================================
# 1. Copy this file: cp .env.example .env
# 2. Fill in your actual API keys and credentials
# 3. Never commit .env to version control
# 4. Use GitHub Secrets for production deployment
# 5. Rotate credentials regularly for security
