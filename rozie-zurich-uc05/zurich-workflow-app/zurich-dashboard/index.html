<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zurich Claims Dashboard - Support Integration</title>
    <link rel="icon" type="image/svg+xml" href="./assets/zurich-logo.svg">
    <link rel="icon" type="image/svg+xml" href="/favicon.svg">
    
    <!-- Support System CSS -->
    <link rel="stylesheet" href="https://unpkg.com/@zendeskgarden/css-bedrock@8.70.0/dist/index.css">
    <link rel="stylesheet" href="https://unpkg.com/@zendeskgarden/css-utilities@4.70.0/dist/index.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="./styles/main.css">
    
    <!-- Supabase Client -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
</head>
<body>
    <div id="app">
        <!-- Loading State -->
        <div id="loading" class="loading-container">
            <div class="loading-spinner"></div>
            <p>Loading Zurich Dashboard...</p>
        </div>

        <!-- Login Page -->
        <div id="login-page" class="page" style="display: none;">
            <div class="login-container">
                <div class="login-card">
                    <div class="login-header">
                        <img src="./assets/zurich-logo.svg" alt="Zurich Insurance" class="logo">
                        <h1>Claims Dashboard</h1>
                        <p>Sign in to access your claims management portal</p>
                    </div>
                    
                    <form id="login-form" class="login-form">
                        <div class="form-group">
                            <label for="email">Email Address</label>
                            <input type="email" id="email" name="email" required 
                                   placeholder="Enter your email address">
                        </div>
                        
                        <div class="form-group">
                            <label for="password">Password</label>
                            <input type="password" id="password" name="password" required 
                                   placeholder="Enter your password">
                        </div>
                        
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="remember-me">
                                <span class="checkmark"></span>
                                Remember me
                            </label>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-full">
                            Sign In
                        </button>
                        
                        <div class="login-footer">
                            <a href="#" class="forgot-password">Forgot your password?</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Main Dashboard -->
        <div id="main-dashboard" class="page" style="display: none;">
            <!-- Navigation Header -->
            <header class="dashboard-header">
                <div class="header-content">
                    <div class="header-left">
                        <img src="./assets/zurich-logo.svg" alt="Zurich" class="header-logo">
                        <h1>Claims Dashboard</h1>
                    </div>
                    
                    <nav class="header-nav">
                        <button class="nav-btn active" data-page="dashboard">
                            Dashboard
                        </button>
                        <button class="nav-btn" data-page="reports">
                            Reports
                        </button>
                        <button class="nav-btn" data-page="support">
                            Support Tickets
                        </button>
                        <button class="nav-btn" data-page="ticket">
                            Claim Details
                        </button>
                    </nav>
                    
                    <div class="header-right">
                        <div class="user-menu">
                            <button class="user-btn">
                                <span class="user-avatar">👤</span>
                                <span class="user-name" id="user-name">Agent</span>
                                <span class="dropdown-arrow">▼</span>
                            </button>
                            <div class="user-dropdown">
                                <a href="#" class="dropdown-item">Profile</a>
                                <a href="#" class="dropdown-item">Settings</a>
                                <hr class="dropdown-divider">
                                <a href="#" class="dropdown-item" id="logout-btn">Sign Out</a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Dashboard Content -->
            <main class="dashboard-main">
                <!-- Dashboard Overview Page -->
                <div id="dashboard-content" class="content-page active">
                    <div class="page-header">
                        <h2>Claims Overview</h2>
                        <div class="page-actions">
                            <button class="btn btn-outline">Export Data</button>
                            <button class="btn btn-primary">New Claim</button>
                        </div>
                    </div>
                    
                    <!-- Metrics Cards -->
                    <div class="metrics-grid">
                        <div class="metric-card">
                            <div class="metric-header">
                                <h3>Total Claims</h3>
                            </div>
                            <div class="metric-value" id="total-claims">-</div>
                            <div class="metric-change positive">+12% from last month</div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-header">
                                <h3>Pending Review</h3>
                            </div>
                            <div class="metric-value" id="pending-claims">-</div>
                            <div class="metric-change neutral">No change</div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-header">
                                <h3>Approved</h3>
                            </div>
                            <div class="metric-value" id="approved-claims">-</div>
                            <div class="metric-change positive">+8% from last month</div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-header">
                                <h3>Avg. Processing Time</h3>
                            </div>
                            <div class="metric-value" id="avg-processing-time">-</div>
                            <div class="metric-change negative">+2 days from last month</div>
                        </div>
                    </div>
                    
                    <!-- Recent Claims Table -->
                    <div class="content-section">
                        <div class="section-header">
                            <h3>Recent Claims</h3>
                            <div class="section-actions">
                                <input type="search" placeholder="Search claims..." class="search-input">
                                <select class="filter-select">
                                    <option value="">All Statuses</option>
                                    <option value="pending">Pending</option>
                                    <option value="approved">Approved</option>
                                    <option value="denied">Denied</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="table-container">
                            <table class="claims-table">
                                <thead>
                                    <tr>
                                        <th>Claim Reference</th>
                                        <th>Status</th>
                                        <th>Submitted</th>
                                        <th>Assigned Agent</th>
                                        <th>Priority</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="claims-table-body">
                                    <!-- Claims will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Reports Page -->
                <div id="reports-content" class="content-page">
                    <div class="page-header">
                        <h2>Claims Reports</h2>
                        <div class="page-actions">
                            <button class="btn btn-outline">Download PDF</button>
                            <button class="btn btn-primary">Schedule Report</button>
                        </div>
                    </div>
                    
                    <!-- Report Filters -->
                    <div class="filters-section">
                        <div class="filter-group">
                            <label>Date Range</label>
                            <select id="date-range">
                                <option value="7">Last 7 days</option>
                                <option value="30">Last 30 days</option>
                                <option value="90">Last 90 days</option>
                                <option value="365">Last year</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label>Claim Type</label>
                            <select id="claim-type">
                                <option value="">All Types</option>
                                <option value="auto">Auto</option>
                                <option value="property">Property</option>
                                <option value="liability">Liability</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label>Agent</label>
                            <select id="agent-filter">
                                <option value="">All Agents</option>
                                <option value="sarah">Sarah Johnson</option>
                                <option value="mike">Mike Chen</option>
                                <option value="lisa">Lisa Rodriguez</option>
                            </select>
                        </div>
                        
                        <button class="btn btn-primary" id="apply-filters">Apply Filters</button>
                    </div>
                    
                    <!-- Charts Section -->
                    <div class="charts-grid">
                        <div class="chart-card">
                            <h3>Claims by Status</h3>
                            <div id="status-chart" class="chart-container"></div>
                        </div>
                        
                        <div class="chart-card">
                            <h3>Processing Time Trends</h3>
                            <div id="time-chart" class="chart-container"></div>
                        </div>
                    </div>
                </div>

                <!-- Support Tickets Page -->
                <div id="support-content" class="content-page">
                    <div class="page-header">
                        <h2>Support Tickets</h2>
                        <div class="page-actions">
                            <button class="btn btn-outline" id="refresh-tickets">Refresh</button>
                            <div class="connection-status" id="ticket-status">
                                <span class="status-indicator"></span>
                                <span class="status-text">Connecting...</span>
                            </div>
                        </div>
                    </div>

                    <!-- Ticket Filters -->
                    <div class="filters-section">
                        <div class="filter-group">
                            <label>Status</label>
                            <select id="ticket-status-filter">
                                <option value="">All Statuses</option>
                                <option value="new">New</option>
                                <option value="open">Open</option>
                                <option value="pending">Pending</option>
                                <option value="solved">Solved</option>
                                <option value="closed">Closed</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label>Priority</label>
                            <select id="ticket-priority-filter">
                                <option value="">All Priorities</option>
                                <option value="low">Low</option>
                                <option value="normal">Normal</option>
                                <option value="high">High</option>
                                <option value="urgent">Urgent</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label>Search</label>
                            <input type="text" id="ticket-search" placeholder="Search tickets...">
                        </div>

                        <button class="btn btn-primary" id="apply-ticket-filters">Apply Filters</button>
                    </div>

                    <!-- Support Tickets Table -->
                    <div class="content-section">
                        <div class="section-header">
                            <h3>Support Tickets</h3>
                            <div class="section-actions">
                                <span id="support-ticket-count">Loading...</span>
                            </div>
                        </div>

                        <div class="table-container">
                            <table class="claims-table">
                                <thead>
                                    <tr>
                                        <th>Ticket ID</th>
                                        <th>Subject</th>
                                        <th>Status</th>
                                        <th>Priority</th>
                                        <th>Assignee</th>
                                        <th>Created</th>
                                        <th>Updated</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="support-tickets-table-body">
                                    <!-- Support tickets will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Individual Ticket View -->
                <div id="ticket-content" class="content-page">
                    <div class="page-header">
                        <h2>Claim Ticket Details</h2>
                        <div class="page-actions">
                            <button class="btn btn-outline">Print</button>
                            <button class="btn btn-primary">Update Status</button>
                        </div>
                    </div>

                    <div class="ticket-container">
                        <!-- Ticket details will be loaded here -->
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Scripts -->
    <script src="./js/zendesk-api.js"></script>
    <script src="./js/zendesk-dashboard.js"></script>
    <script src="./js/auth.js"></script>
    <script src="./js/dashboard.js"></script>
    <script src="./js/reports.js"></script>
    <script src="./js/tickets.js"></script>
    <script src="./js/main.js"></script>
</body>
</html>
