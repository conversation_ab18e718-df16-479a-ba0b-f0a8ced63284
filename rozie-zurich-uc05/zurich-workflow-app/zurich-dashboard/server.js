// Zendesk API Wrapper Server
// Handles Zendesk API calls and serves the dashboard

const express = require('express');
const cors = require('cors');
const path = require('path');
const fetch = require('node-fetch');

const app = express();
const PORT = process.env.PORT || 2000;

// Zendesk Configuration
const ZENDESK_CONFIG = {
    subdomain: 'd3v-rozieai5417',
    email: '<EMAIL>',
    token: '1gfty1sXmithKfIhjwWnUWmTXLrxlVAJEiGrNcF1'
};

const ZENDESK_BASE_URL = `https://${ZENDESK_CONFIG.subdomain}.zendesk.com/api/v2`;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname)));

// Helper function to make Zendesk API calls
async function callZendeskAPI(endpoint, options = {}) {
    const url = `${ZENDESK_BASE_URL}${endpoint}`;
    
    // Create authorization header
    const auth = Buffer.from(`${ZENDESK_CONFIG.email}/token:${ZENDESK_CONFIG.token}`).toString('base64');
    
    const response = await fetch(url, {
        method: 'GET',
        headers: {
            'Authorization': `Basic ${auth}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            ...options.headers
        },
        ...options
    });

    if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
}

// API Routes

// Test connection
app.get('/api/zendesk/test', async (req, res) => {
    try {
        console.log('Testing Zendesk connection...');
        const data = await callZendeskAPI('/users/me.json');
        
        res.json({
            success: true,
            connected: true,
            user: data.user,
            subdomain: ZENDESK_CONFIG.subdomain
        });
    } catch (error) {
        console.error('Zendesk connection failed:', error.message);
        res.status(500).json({
            success: false,
            connected: false,
            error: error.message
        });
    }
});

// Get all tickets
app.get('/api/zendesk/tickets', async (req, res) => {
    try {
        console.log('Fetching tickets...');
        
        // Build query parameters
        const params = new URLSearchParams();
        if (req.query.per_page) params.append('per_page', Math.min(req.query.per_page, 100));
        if (req.query.sort) params.append('sort', req.query.sort);
        
        const endpoint = `/tickets.json${params.toString() ? '?' + params.toString() : ''}`;
        const data = await callZendeskAPI(endpoint);
        
        console.log(`Loaded ${data.tickets?.length || 0} tickets`);
        res.json(data);
    } catch (error) {
        console.error('Error fetching tickets:', error.message);
        res.status(500).json({
            success: false,
            error: error.message,
            tickets: []
        });
    }
});

// Get all users
app.get('/api/zendesk/users', async (req, res) => {
    try {
        console.log('Fetching users...');
        const data = await callZendeskAPI('/users.json');
        
        console.log(`Loaded ${data.users?.length || 0} users`);
        res.json(data);
    } catch (error) {
        console.error('Error fetching users:', error.message);
        res.status(500).json({
            success: false,
            error: error.message,
            users: []
        });
    }
});

// Get specific ticket
app.get('/api/zendesk/tickets/:id', async (req, res) => {
    try {
        const ticketId = req.params.id;
        console.log(`Fetching ticket ${ticketId}...`);
        
        const data = await callZendeskAPI(`/tickets/${ticketId}.json`);
        res.json(data);
    } catch (error) {
        console.error(`Error fetching ticket ${req.params.id}:`, error.message);
        res.status(500).json({
            success: false,
            error: error.message,
            ticket: null
        });
    }
});

// Get ticket comments
app.get('/api/zendesk/tickets/:id/comments', async (req, res) => {
    try {
        const ticketId = req.params.id;
        console.log(`Fetching comments for ticket ${ticketId}...`);
        
        const data = await callZendeskAPI(`/tickets/${ticketId}/comments.json`);
        res.json(data);
    } catch (error) {
        console.error(`Error fetching comments for ticket ${req.params.id}:`, error.message);
        res.status(500).json({
            success: false,
            error: error.message,
            comments: []
        });
    }
});

// Search tickets
app.get('/api/zendesk/search', async (req, res) => {
    try {
        const query = req.query.query;
        if (!query) {
            return res.status(400).json({
                success: false,
                error: 'Query parameter is required'
            });
        }
        
        console.log(`Searching tickets: ${query}`);
        const encodedQuery = encodeURIComponent(query);
        const data = await callZendeskAPI(`/search.json?query=${encodedQuery}`);
        
        // Filter only tickets from search results
        const tickets = data.results?.filter(result => result.result_type === 'ticket') || [];
        
        res.json({
            ...data,
            tickets: tickets
        });
    } catch (error) {
        console.error('Error searching tickets:', error.message);
        res.status(500).json({
            success: false,
            error: error.message,
            results: []
        });
    }
});

// Health check
app.get('/api/health', (req, res) => {
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        service: 'Zendesk API Wrapper'
    });
});

// Serve the dashboard
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Error handling middleware
app.use((error, req, res, next) => {
    console.error('Server error:', error);
    res.status(500).json({
        success: false,
        error: 'Internal server error'
    });
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({
        success: false,
        error: 'Endpoint not found'
    });
});

// Start server
app.listen(PORT, () => {
    console.log('🚀 Zendesk API Wrapper Server Started');
    console.log(`📍 Server running at: http://localhost:${PORT}`);
    console.log(`🌐 Dashboard URL: http://localhost:${PORT}`);
    console.log(`🔧 API Base URL: http://localhost:${PORT}/api/zendesk`);
    console.log(`⏹️  Press Ctrl+C to stop the server`);
    console.log();
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Server shutting down...');
    process.exit(0);
});
