{"name": "zurich-dashboard", "version": "1.0.0", "description": "Zurich Insurance Claims Dashboard with Zendesk Integration", "main": "index.js", "scripts": {"dev": "nodemon server.js", "start": "node server.js", "build": "echo 'No build step required for this application'", "test": "echo 'No tests specified'", "docker:build": "docker build -t zurich-dashboard .", "docker:build:dev": "docker build -f Dockerfile.dev -t zurich-dashboard:dev .", "docker:run": "docker run -p 2000:2000 zurich-dashboard", "docker:run:dev": "docker run -p 2000:2000 -v $(pwd):/app zurich-dashboard:dev", "docker:compose:up": "docker-compose up -d", "docker:compose:down": "docker-compose down", "docker:compose:dev": "docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d", "docker:compose:prod": "docker-compose --profile production up -d", "docker:logs": "docker-compose logs -f zurich-dashboard", "docker:clean": "docker-compose down -v && docker system prune -f"}, "dependencies": {"@zendeskgarden/react-accordions": "^9.5.3", "@zendeskgarden/react-buttons": "^9.5.3", "@zendeskgarden/react-chrome": "^9.5.3", "@zendeskgarden/react-forms": "^9.5.3", "@zendeskgarden/react-grid": "^9.5.3", "@zendeskgarden/react-loaders": "^9.5.3", "@zendeskgarden/react-modals": "^9.5.3", "@zendeskgarden/react-notifications": "^9.5.3", "@zendeskgarden/react-tables": "^9.5.3", "@zendeskgarden/react-theming": "^9.5.3", "@zendeskgarden/react-typography": "^9.5.3", "@zendeskgarden/svg-icons": "^7.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "styled-components": "^5.3.6", "axios": "^1.3.0", "date-fns": "^2.29.0", "recharts": "^2.5.0", "express": "^4.18.2", "cors": "^2.8.5", "node-fetch": "^2.7.0"}, "devDependencies": {"@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "@vitejs/plugin-react": "^3.1.0", "typescript": "^4.9.4", "vite": "^4.1.0", "http-server": "^14.1.1", "nodemon": "^3.0.1"}, "keywords": ["<PERSON>ich", "insurance", "claims", "dashboard", "zendesk"], "author": "Zurich Insurance", "license": "PROPRIETARY"}