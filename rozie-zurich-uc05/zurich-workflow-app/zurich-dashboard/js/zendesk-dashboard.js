// Support Tickets Dashboard Manager - Read-Only Integration
class TicketsDashboardManager {
    constructor() {
        this.supportAPI = null;
        this.tickets = [];
        this.filteredTickets = [];
        this.agents = [];
        this.currentFilters = {
            status: '',
            priority: '',
            search: ''
        };
        this.init();
    }

    async init() {
        console.log('Initializing Support Tickets Dashboard Manager...');
        this.waitForAPI();
    }

    waitForAPI() {
        if (window.supportAPI) {
            this.supportAPI = window.supportAPI;
            this.setupEventListeners();
            console.log('Support Tickets Dashboard Manager ready');
        } else {
            setTimeout(() => this.waitForAPI(), 100);
        }
    }

    setupEventListeners() {
        // Refresh button
        const refreshBtn = document.getElementById('refresh-tickets');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.loadTicketsData();
            });
        }

        // Filter controls
        const statusFilter = document.getElementById('ticket-status-filter');
        const priorityFilter = document.getElementById('ticket-priority-filter');
        const searchInput = document.getElementById('ticket-search');
        const applyFiltersBtn = document.getElementById('apply-ticket-filters');

        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.currentFilters.status = e.target.value;
            });
        }

        if (priorityFilter) {
            priorityFilter.addEventListener('change', (e) => {
                this.currentFilters.priority = e.target.value;
            });
        }

        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.currentFilters.search = e.target.value;
            });
            
            // Search on Enter key
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.applyFilters();
                }
            });
        }

        if (applyFiltersBtn) {
            applyFiltersBtn.addEventListener('click', () => {
                this.applyFilters();
            });
        }
    }

    async loadTicketsData() {
        console.log('Loading support tickets data...');

        try {
            // Update connection status
            this.updateConnectionStatus('loading', 'Loading...');

            // Load tickets and agents
            await Promise.all([
                this.loadTickets(),
                this.loadAgents()
            ]);

            // Apply current filters
            this.applyFilters();

            // Update connection status
            const status = this.supportAPI.getConnectionStatus();
            if (status.connected) {
                this.updateConnectionStatus('connected', `Connected to ${status.subdomain}`);
            } else {
                this.updateConnectionStatus('demo', 'Demo Mode (API not configured)');
            }

            console.log('Support tickets data loaded successfully');

        } catch (error) {
            console.error('Error loading support tickets data:', error);
            this.updateConnectionStatus('error', 'Connection Failed');
            this.showError('Failed to load support tickets data');
        }
    }

    async loadTickets() {
        try {
            this.tickets = await this.supportAPI.getAllTickets({ per_page: 100 });
            console.log(`📋 Loaded ${this.tickets.length} support tickets`);
        } catch (error) {
            console.error('❌ Error loading tickets:', error);
            this.tickets = [];
        }
    }

    async loadAgents() {
        try {
            this.agents = await this.supportAPI.getAllUsers();
            console.log(`👥 Loaded ${this.agents.length} support users`);
        } catch (error) {
            console.error('❌ Error loading agents:', error);
            this.agents = [];
        }
    }

    applyFilters() {
        this.filteredTickets = this.tickets.filter(ticket => {
            // Status filter
            if (this.currentFilters.status && (ticket.status || '').toLowerCase() !== this.currentFilters.status.toLowerCase()) {
                return false;
            }

            // Priority filter
            if (this.currentFilters.priority && (ticket.priority || '').toLowerCase() !== this.currentFilters.priority.toLowerCase()) {
                return false;
            }

            // Search filter
            if (this.currentFilters.search) {
                const searchTerm = this.currentFilters.search.toLowerCase();
                const searchableText = [
                    ticket.subject || '',
                    ticket.description || '',
                    (ticket.id || '').toString(),
                    ...(ticket.tags || [])
                ].join(' ').toLowerCase();

                if (!searchableText.includes(searchTerm)) {
                    return false;
                }
            }

            return true;
        });

        console.log(`Filtered to ${this.filteredTickets.length} tickets`);
        this.renderTicketsTable();
        this.updateTicketCount();
    }

    renderTicketsTable() {
        const tableBody = document.getElementById('support-tickets-table-body');
        if (!tableBody) return;

        if (this.filteredTickets.length === 0) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="8" style="text-align: center; padding: 2rem; color: var(--zd-color-grey-600);">
                        No tickets found
                    </td>
                </tr>
            `;
            return;
        }

        tableBody.innerHTML = this.filteredTickets.map(ticket => this.createTicketRow(ticket)).join('');
    }

    createTicketRow(ticket) {
        const assignee = this.getAgentName(ticket.assignee_id);
        const statusClass = (ticket.status || 'unknown').toLowerCase();
        const priorityClass = (ticket.priority || 'normal').toLowerCase();

        return `
            <tr data-ticket-id="${ticket.id}">
                <td>
                    <a href="#" class="ticket-link" data-ticket-id="${ticket.id}">
                        #${ticket.id}
                    </a>
                </td>
                <td>
                    <div class="ticket-subject">
                        <strong>${this.escapeHtml(ticket.subject || 'No Subject')}</strong>
                        ${ticket.tags && ticket.tags.length > 0 ? `
                            <div class="ticket-tags">
                                ${ticket.tags.slice(0, 3).map(tag => `<span class="tag">${tag}</span>`).join('')}
                            </div>
                        ` : ''}
                    </div>
                </td>
                <td>
                    <span class="status-badge ${statusClass}">
                        ${this.zendeskAPI.formatTicketStatus(ticket.status)}
                    </span>
                </td>
                <td>
                    <span class="priority-badge ${priorityClass}">
                        ${this.zendeskAPI.formatTicketPriority(ticket.priority)}
                    </span>
                </td>
                <td>${assignee}</td>
                <td>${this.formatDate(ticket.created_at)}</td>
                <td>${this.formatDate(ticket.updated_at)}</td>
                <td>
                    ${ticket.zendesk_ticket_url ? `
                        <button class="action-btn" onclick="ticketsDashboard.openTicketURL('${ticket.zendesk_ticket_url}')">
                            View
                        </button>
                    ` : `
                        <button class="action-btn" onclick="ticketsDashboard.viewTicketDetails(${ticket.id})">
                            View Details
                        </button>
                    `}
                </td>
            </tr>
        `;
    }

    getAgentName(agentId) {
        if (!agentId) return 'Unassigned';
        
        const agent = this.agents.find(a => a.id === agentId);
        return agent ? agent.name : `Agent ${agentId}`;
    }

    openTicketURL(ticketUrl) {
        console.log('Opening ticket URL:', ticketUrl);

        // Open the ticket URL in a new tab
        window.open(ticketUrl, '_blank');
    }

    async viewTicketDetails(ticketId) {
        console.log(`Viewing ticket details: ${ticketId}`);

        try {
            const ticket = await this.zendeskAPI.getTicketById(ticketId);
            if (ticket) {
                this.showTicketModal(ticket);
            } else {
                this.showError(`Ticket ${ticketId} not found`);
            }
        } catch (error) {
            console.error('Error viewing ticket details:', error);
            this.showError('Failed to load ticket details');
        }
    }

    async viewTicketComments(ticketId) {
        console.log(`💬 Viewing comments for ticket: ${ticketId}`);
        
        try {
            const comments = await this.zendeskAPI.getTicketComments(ticketId);
            this.showCommentsModal(ticketId, comments);
        } catch (error) {
            console.error('❌ Error viewing ticket comments:', error);
            this.showError('Failed to load ticket comments');
        }
    }

    showTicketModal(ticket) {
        const assignee = this.getAgentName(ticket.assignee_id);
        const requester = this.getAgentName(ticket.requester_id);
        
        const modalHtml = `
            <div class="modal-overlay" onclick="ticketsDashboard.closeModal()">
                <div class="modal-content" onclick="event.stopPropagation()">
                    <div class="modal-header">
                        <h3>Support Ticket #${ticket.id}</h3>
                        <button class="modal-close" onclick="ticketsDashboard.closeModal()">×</button>
                    </div>
                    <div class="modal-body">
                        <div class="ticket-details-grid">
                            <div class="detail-item">
                                <label>Subject</label>
                                <span>${this.escapeHtml(ticket.subject)}</span>
                            </div>
                            <div class="detail-item">
                                <label>Status</label>
                                <span class="status-badge ${ticket.status}">${this.zendeskAPI.formatTicketStatus(ticket.status)}</span>
                            </div>
                            <div class="detail-item">
                                <label>Priority</label>
                                <span class="priority-badge ${ticket.priority}">${this.zendeskAPI.formatTicketPriority(ticket.priority)}</span>
                            </div>
                            <div class="detail-item">
                                <label>Type</label>
                                <span>${ticket.type || 'N/A'}</span>
                            </div>
                            <div class="detail-item">
                                <label>Assignee</label>
                                <span>${assignee}</span>
                            </div>
                            <div class="detail-item">
                                <label>Requester</label>
                                <span>${requester}</span>
                            </div>
                            <div class="detail-item">
                                <label>Created</label>
                                <span>${this.formatDateTime(ticket.created_at)}</span>
                            </div>
                            <div class="detail-item">
                                <label>Updated</label>
                                <span>${this.formatDateTime(ticket.updated_at)}</span>
                            </div>
                        </div>
                        
                        ${ticket.description ? `
                            <div class="ticket-description">
                                <h4>Description</h4>
                                <div class="description-content">${this.escapeHtml(ticket.description)}</div>
                            </div>
                        ` : ''}
                        
                        ${ticket.tags && ticket.tags.length > 0 ? `
                            <div class="ticket-tags-section">
                                <h4>Tags</h4>
                                <div class="tags-list">
                                    ${ticket.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                                </div>
                            </div>
                        ` : ''}
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-outline" onclick="ticketsDashboard.viewTicketComments(${ticket.id})">
                            View Comments
                        </button>
                        <button class="btn btn-primary" onclick="ticketsDashboard.closeModal()">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        this.showModal(modalHtml);
    }

    showCommentsModal(ticketId, comments) {
        const modalHtml = `
            <div class="modal-overlay" onclick="ticketsDashboard.closeModal()">
                <div class="modal-content large" onclick="event.stopPropagation()">
                    <div class="modal-header">
                        <h3>Comments - Ticket #${ticketId}</h3>
                        <button class="modal-close" onclick="ticketsDashboard.closeModal()">×</button>
                    </div>
                    <div class="modal-body">
                        <div class="comments-list">
                            ${comments.length > 0 ? comments.map(comment => `
                                <div class="comment-item">
                                    <div class="comment-header">
                                        <strong>${this.getAgentName(comment.author_id)}</strong>
                                        <span class="comment-date">${this.formatDateTime(comment.created_at)}</span>
                                        <span class="comment-type ${comment.public ? 'public' : 'private'}">
                                            ${comment.public ? 'Public' : 'Private'}
                                        </span>
                                    </div>
                                    <div class="comment-body">
                                        ${this.escapeHtml(comment.plain_body || comment.body)}
                                    </div>
                                </div>
                            `).join('') : '<p>No comments found for this ticket.</p>'}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-primary" onclick="ticketsDashboard.closeModal()">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        this.showModal(modalHtml);
    }

    showModal(html) {
        // Remove existing modal
        this.closeModal();
        
        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', html);
        
        // Add modal styles if not already added
        this.addModalStyles();
    }

    closeModal() {
        const modal = document.querySelector('.modal-overlay');
        if (modal) {
            modal.remove();
        }
    }

    updateConnectionStatus(status, text) {
        const statusElement = document.getElementById('ticket-status');
        if (!statusElement) return;

        const indicator = statusElement.querySelector('.status-indicator');
        const textElement = statusElement.querySelector('.status-text');

        if (indicator && textElement) {
            indicator.className = `status-indicator ${status}`;
            textElement.textContent = text;
        }
    }

    updateTicketCount() {
        const countElement = document.getElementById('support-ticket-count');
        if (countElement) {
            countElement.textContent = `${this.filteredTickets.length} tickets`;
        }
    }

    formatDate(dateString) {
        if (!dateString) return 'N/A';
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }

    formatDateTime(dateString) {
        if (!dateString) return 'N/A';
        return new Date(dateString).toLocaleString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    addModalStyles() {
        if (document.getElementById('zendesk-modal-styles')) return;

        const style = document.createElement('style');
        style.id = 'zendesk-modal-styles';
        style.textContent = `
            .modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
            }
            
            .modal-content {
                background: white;
                border-radius: 8px;
                max-width: 600px;
                width: 90%;
                max-height: 80vh;
                overflow: hidden;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            }
            
            .modal-content.large {
                max-width: 800px;
            }
            
            .modal-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 1rem 1.5rem;
                border-bottom: 1px solid var(--zd-color-grey-200);
            }
            
            .modal-close {
                background: none;
                border: none;
                font-size: 24px;
                cursor: pointer;
                color: var(--zd-color-grey-600);
            }
            
            .modal-body {
                padding: 1.5rem;
                overflow-y: auto;
                max-height: 60vh;
            }
            
            .modal-footer {
                padding: 1rem 1.5rem;
                border-top: 1px solid var(--zd-color-grey-200);
                display: flex;
                justify-content: flex-end;
                gap: 1rem;
            }
            
            .ticket-details-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 1rem;
                margin-bottom: 1.5rem;
            }
            
            .ticket-description {
                margin-bottom: 1.5rem;
            }
            
            .description-content {
                background: var(--zd-color-grey-100);
                padding: 1rem;
                border-radius: 4px;
                white-space: pre-wrap;
            }
            
            .ticket-tags-section {
                margin-bottom: 1rem;
            }
            
            .tags-list {
                display: flex;
                flex-wrap: wrap;
                gap: 0.5rem;
            }
            
            .tag {
                background: var(--zurich-light-blue);
                color: var(--zurich-blue);
                padding: 0.25rem 0.5rem;
                border-radius: 4px;
                font-size: 12px;
                font-weight: 500;
            }
            
            .comments-list {
                display: flex;
                flex-direction: column;
                gap: 1rem;
            }
            
            .comment-item {
                border: 1px solid var(--zd-color-grey-200);
                border-radius: 4px;
                padding: 1rem;
            }
            
            .comment-header {
                display: flex;
                align-items: center;
                gap: 1rem;
                margin-bottom: 0.5rem;
                font-size: 14px;
            }
            
            .comment-date {
                color: var(--zd-color-grey-600);
                font-size: 12px;
            }
            
            .comment-type {
                padding: 0.125rem 0.5rem;
                border-radius: 4px;
                font-size: 11px;
                font-weight: 500;
            }
            
            .comment-type.public {
                background: rgba(3, 129, 83, 0.1);
                color: var(--zd-color-green-600);
            }
            
            .comment-type.private {
                background: rgba(247, 154, 62, 0.1);
                color: var(--zd-color-yellow-600);
            }
            
            .comment-body {
                white-space: pre-wrap;
                line-height: 1.5;
            }
            
            .status-indicator {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                display: inline-block;
                margin-right: 0.5rem;
            }
            
            .status-indicator.connected {
                background: var(--zd-color-green-600);
            }
            
            .status-indicator.loading {
                background: var(--zd-color-yellow-600);
                animation: pulse 1s infinite;
            }
            
            .status-indicator.error {
                background: var(--zd-color-red-600);
            }
            
            .status-indicator.demo {
                background: var(--zd-color-blue-600);
            }
            
            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.5; }
            }
            
            .ticket-subject {
                display: flex;
                flex-direction: column;
                gap: 0.25rem;
            }
            
            .ticket-tags {
                display: flex;
                gap: 0.25rem;
                flex-wrap: wrap;
            }
        `;
        
        document.head.appendChild(style);
    }

    showError(message) {
        console.error('Zendesk Dashboard Error:', message);
        if (window.zurichDashboard) {
            window.zurichDashboard.handleError(new Error(message), 'Zendesk');
        }
    }

    // Public methods
    refresh() {
        this.loadZendeskData();
    }

    getTickets() {
        return this.tickets;
    }

    getFilteredTickets() {
        return this.filteredTickets;
    }
}

// Initialize support tickets dashboard manager when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.ticketsDashboard = new TicketsDashboardManager();
});
