// Support API Wrapper - Uses backend proxy to avoid CORS issues
class SupportAPI {
    constructor() {
        this.baseUrl = '/api/support'; // Use local wrapper API
        this.isConnected = false;
        this.tickets = [];
        this.agents = [];
        this.organizations = [];
        this.init();
    }

    async init() {
        console.log('Initializing Support System API Wrapper...');

        // Test connection through wrapper
        await this.testConnection();
    }

    async testConnection() {
        try {
            console.log('Testing support system API connection...');

            const response = await fetch(`${this.baseUrl}/test`);
            const data = await response.json();

            if (data.success && data.connected) {
                this.isConnected = true;
                console.log('Support System API connection successful:', data.user?.name || 'Connected');
                return true;
            } else {
                throw new Error(data.error || 'Connection failed');
            }

        } catch (error) {
            console.error('Support System API connection failed:', error);
            this.isConnected = false;
            return false;
        }
    }

    async makeRequest(endpoint, options = {}) {
        try {
            const url = `${this.baseUrl}${endpoint}`;

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                ...options
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();

        } catch (error) {
            console.error('Support API request failed:', error);

            // Fallback to mock data for demo
            console.log('Using mock data for demo purposes');
            return this.getMockData(endpoint);
        }
    }

    getMockData(endpoint) {
        // Mock data for demo when API is not available
        const mockData = {
            '/users/me.json': {
                user: {
                    id: 123456789,
                    name: 'Demo Agent',
                    email: '<EMAIL>',
                    role: 'agent',
                    active: true
                }
            },
            '/tickets.json': {
                tickets: [
                    {
                        id: 1001,
                        subject: 'Auto Insurance Claim - CLM-85228383',
                        description: 'Customer submitted auto insurance claim for vehicle damage',
                        status: 'open',
                        priority: 'normal',
                        type: 'incident',
                        created_at: '2024-01-15T10:30:00Z',
                        updated_at: '2024-01-16T14:20:00Z',
                        assignee_id: 123456789,
                        requester_id: 987654321,
                        organization_id: 555666777,
                        tags: ['insurance', 'auto', 'claim'],
                        custom_fields: [
                            { id: 360000001, value: 'CLM-85228383' },
                            { id: 360000002, value: 'Auto Insurance' }
                        ],
                        zendesk_ticket_url: 'https://d3v-rozieai5417.zendesk.com/agent/tickets/1001'
                    },
                    {
                        id: 1002,
                        subject: 'Property Claim Review - CLM-52939141',
                        description: 'Property damage claim requiring review',
                        status: 'solved',
                        priority: 'high',
                        type: 'problem',
                        created_at: '2024-01-10T09:15:00Z',
                        updated_at: '2024-01-18T16:45:00Z',
                        assignee_id: 123456789,
                        requester_id: 111222333,
                        organization_id: 555666777,
                        tags: ['insurance', 'property', 'approved'],
                        custom_fields: [
                            { id: 360000001, value: 'CLM-52939141' },
                            { id: 360000002, value: 'Property Insurance' }
                        ],
                        zendesk_ticket_url: 'https://d3v-rozieai5417.zendesk.com/agent/tickets/1002'
                    },
                    {
                        id: 1003,
                        subject: 'Liability Claim Investigation',
                        description: 'Complex liability claim requiring investigation',
                        status: 'pending',
                        priority: 'urgent',
                        type: 'incident',
                        created_at: '2024-01-20T11:00:00Z',
                        updated_at: '2024-01-20T11:00:00Z',
                        assignee_id: null,
                        requester_id: 444555666,
                        organization_id: 555666777,
                        tags: ['insurance', 'liability', 'investigation'],
                        custom_fields: [
                            { id: 360000001, value: 'CLM-12345678' },
                            { id: 360000002, value: 'Liability Insurance' }
                        ]
                        // No zendesk_ticket_url for this one to test the fallback
                    }
                ],
                next_page: null,
                previous_page: null,
                count: 3
            },
            '/users.json': {
                users: [
                    {
                        id: 123456789,
                        name: 'Sarah Johnson',
                        email: '<EMAIL>',
                        role: 'agent',
                        active: true,
                        created_at: '2023-06-01T00:00:00Z',
                        last_login_at: '2024-01-20T08:30:00Z'
                    },
                    {
                        id: 987654321,
                        name: 'Mike Chen',
                        email: '<EMAIL>',
                        role: 'agent',
                        active: true,
                        created_at: '2023-06-01T00:00:00Z',
                        last_login_at: '2024-01-19T17:45:00Z'
                    },
                    {
                        id: 111222333,
                        name: 'Lisa Rodriguez',
                        email: '<EMAIL>',
                        role: 'admin',
                        active: true,
                        created_at: '2023-06-01T00:00:00Z',
                        last_login_at: '2024-01-20T09:15:00Z'
                    }
                ]
            }
        };

        // Add ticket comments mock data
        if (endpoint.includes('/tickets/') && endpoint.includes('/comments.json')) {
            const ticketId = endpoint.match(/\/tickets\/(\d+)\//)[1];
            return {
                comments: [
                    {
                        id: 2001,
                        type: 'Comment',
                        author_id: 123456789,
                        body: 'Initial claim received and under review. All required documents have been submitted.',
                        html_body: '<p>Initial claim received and under review. All required documents have been submitted.</p>',
                        plain_body: 'Initial claim received and under review. All required documents have been submitted.',
                        public: false,
                        created_at: '2024-01-15T10:35:00Z'
                    },
                    {
                        id: 2002,
                        type: 'Comment',
                        author_id: 987654321,
                        body: 'Completed initial assessment. Claim appears valid and within policy coverage.',
                        html_body: '<p>Completed initial assessment. Claim appears valid and within policy coverage.</p>',
                        plain_body: 'Completed initial assessment. Claim appears valid and within policy coverage.',
                        public: false,
                        created_at: '2024-01-16T14:20:00Z'
                    }
                ]
            };
        }

        return mockData[endpoint] || { error: 'Mock data not found for endpoint' };
    }

    // Public API methods for reading Zendesk data

    async getAllTickets(options = {}) {
        console.log('Fetching all support tickets...');

        try {
            // Use wrapper API endpoint
            let endpoint = '/tickets';

            // Add query parameters if provided
            const params = new URLSearchParams();
            if (options.per_page) params.append('per_page', options.per_page || 100);
            if (options.sort) params.append('sort', options.sort);

            if (params.toString()) {
                endpoint += '?' + params.toString();
            }

            console.log('Requesting endpoint:', endpoint);
            const response = await this.makeRequest(endpoint);

            if (response && response.tickets) {
                this.tickets = response.tickets;
                console.log(`Loaded ${this.tickets.length} tickets from support system`);
                return this.tickets;
            } else if (response && response.error) {
                console.error('API Error:', response.error);
                throw new Error(response.error);
            } else {
                console.warn('No tickets found in response, using mock data');
                const mockResponse = this.getMockData('/tickets');
                this.tickets = mockResponse.tickets || [];
                return this.tickets;
            }

        } catch (error) {
            console.error('Error fetching tickets:', error);
            console.log('Falling back to mock data');
            const mockResponse = this.getMockData('/tickets');
            this.tickets = mockResponse.tickets || [];
            return this.tickets;
        }
    }

    async getTicketById(ticketId) {
        console.log(`🎫 Fetching ticket ${ticketId}...`);

        try {
            const response = await this.makeRequest(`/tickets/${ticketId}`);
            return response.ticket || null;

        } catch (error) {
            console.error(`❌ Error fetching ticket ${ticketId}:`, error);
            return null;
        }
    }

    async getTicketComments(ticketId) {
        console.log(`💬 Fetching comments for ticket ${ticketId}...`);

        try {
            const response = await this.makeRequest(`/tickets/${ticketId}/comments`);
            return response.comments || [];

        } catch (error) {
            console.error(`❌ Error fetching comments for ticket ${ticketId}:`, error);
            return [];
        }
    }

    async getAllUsers() {
        console.log('Fetching support users...');

        try {
            // Use wrapper API endpoint
            const response = await this.makeRequest('/users');

            if (response && response.users) {
                this.agents = response.users;
                console.log(`Loaded ${this.agents.length} users from support system`);
                return this.agents;
            } else if (response && response.error) {
                console.error('API Error:', response.error);
                throw new Error(response.error);
            } else {
                console.warn('No users found in response, using mock data');
                const mockResponse = this.getMockData('/users');
                this.agents = mockResponse.users || [];
                return this.agents;
            }

        } catch (error) {
            console.error('Error fetching users:', error);
            console.log('Falling back to mock data');
            const mockResponse = this.getMockData('/users');
            this.agents = mockResponse.users || [];
            return this.agents;
        }
    }

    async getUserById(userId) {
        console.log(`👤 Fetching user ${userId}...`);
        
        try {
            const response = await this.makeRequest(`/users/${userId}.json`);
            return response.user || null;
            
        } catch (error) {
            console.error(`❌ Error fetching user ${userId}:`, error);
            return null;
        }
    }

    async searchTickets(query) {
        console.log(`🔍 Searching tickets: ${query}`);

        try {
            const encodedQuery = encodeURIComponent(query);
            const response = await this.makeRequest(`/search?query=${encodedQuery}`);

            // Filter only tickets from search results
            const tickets = response.tickets || response.results?.filter(result => result.result_type === 'ticket') || [];

            console.log(`✅ Found ${tickets.length} tickets matching: ${query}`);
            return tickets;

        } catch (error) {
            console.error(`❌ Error searching tickets:`, error);
            return [];
        }
    }

    // Utility methods

    getTicketsByClaimReference(claimRef) {
        return this.tickets.filter(ticket => 
            ticket.subject?.includes(claimRef) ||
            ticket.custom_fields?.some(field => field.value === claimRef)
        );
    }

    formatTicketStatus(status) {
        const statusMap = {
            'new': 'New',
            'open': 'Open',
            'pending': 'Pending',
            'hold': 'On Hold',
            'solved': 'Solved',
            'closed': 'Closed'
        };
        
        return statusMap[status] || status;
    }

    formatTicketPriority(priority) {
        const priorityMap = {
            'low': 'Low',
            'normal': 'Normal',
            'high': 'High',
            'urgent': 'Urgent'
        };
        
        return priorityMap[priority] || priority;
    }

    isConnected() {
        return this.isConnected;
    }

    getConnectionStatus() {
        return {
            connected: this.isConnected,
            subdomain: 'd3v-rozieai5417',
            email: '<EMAIL>',
            baseUrl: this.baseUrl
        };
    }
}

// Initialize Zendesk API when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.zendeskAPI = new ZendeskAPI();
});
