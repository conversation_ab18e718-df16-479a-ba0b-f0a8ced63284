// Main Application Controller for Zurich Dashboard
class ZurichDashboard {
    constructor() {
        this.currentPage = 'dashboard';
        this.isInitialized = false;
        this.init();
    }

    async init() {
        console.log('🚀 Initializing Zurich Dashboard...');
        
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }

    setup() {
        // Setup navigation
        this.setupNavigation();
        
        // Setup page routing
        this.setupRouting();
        
        // Setup global event listeners
        this.setupGlobalEvents();
        
        // Initialize page based on URL hash
        this.initializeFromHash();
        
        this.isInitialized = true;
        console.log('✅ Zurich Dashboard initialized');
    }

    setupNavigation() {
        // Navigation button handlers
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const page = btn.getAttribute('data-page');
                if (page) {
                    this.navigateToPage(page);
                }
            });
        });

        // Claim link handlers (for table rows)
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('claim-link')) {
                e.preventDefault();
                const claimRef = e.target.getAttribute('data-claim-ref');
                if (claimRef) {
                    this.viewClaimTicket(claimRef);
                }
            }
        });
    }

    setupRouting() {
        // Handle browser back/forward buttons
        window.addEventListener('popstate', (e) => {
            if (e.state && e.state.page) {
                this.navigateToPage(e.state.page, false);
            }
        });
    }

    setupGlobalEvents() {
        // Global keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + K for search
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                this.focusSearch();
            }
            
            // Escape to close modals/dropdowns
            if (e.key === 'Escape') {
                this.closeModals();
            }
        });

        // Global click handler for dropdowns
        document.addEventListener('click', (e) => {
            // Close user dropdown if clicking outside
            if (!e.target.closest('.user-menu')) {
                document.querySelectorAll('.user-dropdown').forEach(dropdown => {
                    dropdown.style.display = 'none';
                });
            }
        });

        // Auto-refresh data every 5 minutes
        setInterval(() => {
            if (this.currentPage === 'dashboard' && window.dashboardManager) {
                console.log('🔄 Auto-refreshing dashboard data...');
                window.dashboardManager.refresh();
            }
        }, 5 * 60 * 1000);
    }

    initializeFromHash() {
        const hash = window.location.hash.slice(1);
        if (hash) {
            const [page, ...params] = hash.split('/');
            if (page) {
                this.navigateToPage(page, false);
                
                // Handle specific routes
                if (page === 'ticket' && params[0]) {
                    this.viewClaimTicket(params[0]);
                }
            }
        }
    }

    navigateToPage(pageName, updateHistory = true) {
        console.log(`📱 Navigating to page: ${pageName}`);
        
        // Validate page exists
        const targetPage = document.getElementById(`${pageName}-content`);
        if (!targetPage) {
            console.error(`❌ Page not found: ${pageName}`);
            return;
        }

        // Update current page
        this.currentPage = pageName;

        // Hide all content pages
        document.querySelectorAll('.content-page').forEach(page => {
            page.classList.remove('active');
        });

        // Show target page
        targetPage.classList.add('active');

        // Update navigation state
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        const targetNav = document.querySelector(`[data-page="${pageName}"]`);
        if (targetNav) {
            targetNav.classList.add('active');
        }

        // Update URL hash
        if (updateHistory) {
            const newUrl = `#${pageName}`;
            window.history.pushState({ page: pageName }, '', newUrl);
        }

        // Load page-specific data
        this.loadPageData(pageName);

        // Update page title
        this.updatePageTitle(pageName);
    }

    loadPageData(pageName) {
        switch (pageName) {
            case 'dashboard':
                if (window.dashboardManager) {
                    window.dashboardManager.loadDashboardData();
                }
                break;

            case 'reports':
                if (window.reportsManager) {
                    window.reportsManager.loadReportsData();
                }
                break;

            case 'support':
                if (window.ticketsDashboard) {
                    window.ticketsDashboard.loadTicketsData();
                }
                break;

            case 'ticket':
                // Individual ticket page loads data when specific ticket is requested
                break;

            default:
                console.log(`No specific data loading for page: ${pageName}`);
        }
    }

    updatePageTitle(pageName) {
        const titles = {
            'dashboard': 'Claims Overview',
            'reports': 'Claims Reports',
            'support': 'Support Tickets',
            'ticket': 'Claim Ticket Details'
        };

        const title = titles[pageName] || 'Dashboard';
        document.title = `${title} - Zurich Claims Dashboard`;
    }

    viewClaimTicket(claimReference) {
        console.log(`Viewing ticket for claim: ${claimReference}`);

        // Navigate to claim details page (not support tickets)
        this.navigateToPage('ticket');

        // Load specific ticket
        if (window.ticketManager) {
            window.ticketManager.loadTicket(claimReference);
        }

        // Update URL to include claim reference
        const newUrl = `#ticket/${claimReference}`;
        window.history.pushState({ page: 'ticket', claim: claimReference }, '', newUrl);
    }

    focusSearch() {
        const searchInput = document.querySelector('.search-input');
        if (searchInput) {
            searchInput.focus();
            searchInput.select();
        }
    }

    closeModals() {
        // Close any open modals or dropdowns
        document.querySelectorAll('.user-dropdown').forEach(dropdown => {
            dropdown.style.display = 'none';
        });
        
        // Close any other modals that might be open
        document.querySelectorAll('.modal').forEach(modal => {
            modal.style.display = 'none';
        });
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'error' ? '#d93f4c' : type === 'success' ? '#038153' : '#1f73b7'};
            color: white;
            padding: 16px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            max-width: 400px;
            animation: slideInRight 0.3s ease-out;
        `;
        
        notification.textContent = message;
        
        // Add to page
        document.body.appendChild(notification);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 300);
        }, 5000);
        
        // Add click to dismiss
        notification.addEventListener('click', () => {
            notification.remove();
        });
    }

    // Public utility methods
    getCurrentPage() {
        return this.currentPage;
    }

    isPageActive(pageName) {
        return this.currentPage === pageName;
    }

    refreshCurrentPage() {
        this.loadPageData(this.currentPage);
    }

    // Error handling
    handleError(error, context = 'Application') {
        console.error(`❌ ${context} Error:`, error);
        this.showNotification(`An error occurred: ${error.message || error}`, 'error');
    }

    // Success handling
    handleSuccess(message) {
        console.log(`✅ Success: ${message}`);
        this.showNotification(message, 'success');
    }
}

// Add CSS animations for notifications
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    
    .notification {
        cursor: pointer;
        transition: transform 0.2s ease;
    }
    
    .notification:hover {
        transform: translateY(-2px);
    }
`;
document.head.appendChild(style);

// Initialize the main application
window.addEventListener('DOMContentLoaded', () => {
    window.zurichDashboard = new ZurichDashboard();
});

// Global error handler
window.addEventListener('error', (e) => {
    console.error('Global Error:', e.error);
    if (window.zurichDashboard) {
        window.zurichDashboard.handleError(e.error, 'Global');
    }
});

// Global unhandled promise rejection handler
window.addEventListener('unhandledrejection', (e) => {
    console.error('Unhandled Promise Rejection:', e.reason);
    if (window.zurichDashboard) {
        window.zurichDashboard.handleError(e.reason, 'Promise');
    }
});
