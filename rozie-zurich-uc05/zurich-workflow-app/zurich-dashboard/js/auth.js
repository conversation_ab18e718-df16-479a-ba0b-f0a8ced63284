// Authentication Module for Zurich Dashboard
class AuthManager {
    constructor() {
        this.supabaseClient = null;
        this.currentUser = null;
        this.init();
    }

    async init() {
        console.log('🔐 Initializing Authentication...');
        
        // Initialize Supabase
        await this.initializeSupabase();
        
        // Check for existing session
        await this.checkExistingSession();
        
        // Setup event listeners
        this.setupEventListeners();
    }

    async initializeSupabase() {
        try {
            // Supabase configuration
            const supabaseUrl = 'https://tlduggpohclrgxbvuzhd.supabase.co';
            const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRsZHVnZ3BvaGNscmd4YnZ1emhkIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDcwMTAzOCwiZXhwIjoyMDY2Mjc3MDM4fQ.wDi5bG1O81a3HaXOujWshJZoGV6AhFgEuaoVcjam0SE';
            
            this.supabaseClient = window.supabase.createClient(supabaseUrl, supabaseKey);
            console.log('✅ Supabase client initialized');
            
        } catch (error) {
            console.error('❌ Failed to initialize Supabase:', error);
            this.showError('Failed to connect to authentication service.');
        }
    }

    async checkExistingSession() {
        // Check localStorage for saved session
        const savedUser = localStorage.getItem('zurich_user');
        if (savedUser) {
            try {
                this.currentUser = JSON.parse(savedUser);
                console.log('✅ Found existing session:', this.currentUser.email);
                this.showDashboard();
                return;
            } catch (error) {
                console.error('❌ Invalid saved session:', error);
                localStorage.removeItem('zurich_user');
            }
        }
        
        // Show login page
        this.showLogin();
    }

    setupEventListeners() {
        // Login form submission
        const loginForm = document.getElementById('login-form');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleLogin();
            });
        }

        // Logout button
        const logoutBtn = document.getElementById('logout-btn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleLogout();
            });
        }

        // Remember me checkbox
        const rememberMe = document.getElementById('remember-me');
        if (rememberMe) {
            const savedRemember = localStorage.getItem('zurich_remember');
            if (savedRemember === 'true') {
                rememberMe.checked = true;
                const savedEmail = localStorage.getItem('zurich_email');
                if (savedEmail) {
                    document.getElementById('email').value = savedEmail;
                }
            }
        }
    }

    async handleLogin() {
        const email = document.getElementById('email').value.trim();
        const password = document.getElementById('password').value;
        const rememberMe = document.getElementById('remember-me').checked;

        if (!email || !password) {
            this.showError('Please enter both email and password.');
            return;
        }

        // Show loading state
        const submitBtn = document.querySelector('#login-form button[type="submit"]');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = 'Signing In...';
        submitBtn.disabled = true;

        try {
            // For demo purposes, we'll use simple validation
            // In production, this would authenticate against your user system
            const isValidUser = await this.validateUser(email, password);
            
            if (isValidUser) {
                // Create user session
                this.currentUser = {
                    id: Date.now().toString(),
                    email: email,
                    name: this.extractNameFromEmail(email),
                    role: this.getUserRole(email),
                    loginTime: new Date().toISOString()
                };

                // Save session
                localStorage.setItem('zurich_user', JSON.stringify(this.currentUser));
                
                // Handle remember me
                if (rememberMe) {
                    localStorage.setItem('zurich_remember', 'true');
                    localStorage.setItem('zurich_email', email);
                } else {
                    localStorage.removeItem('zurich_remember');
                    localStorage.removeItem('zurich_email');
                }

                console.log('✅ Login successful:', this.currentUser);
                this.showDashboard();
                
            } else {
                this.showError('Invalid email or password. Please try again.');
            }

        } catch (error) {
            console.error('❌ Login error:', error);
            this.showError('An error occurred during login. Please try again.');
        } finally {
            // Reset button state
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }
    }

    async validateUser(email, password) {
        // Demo validation - in production, this would be a real authentication check
        const validUsers = [
            { email: '<EMAIL>', password: 'demo123' },
            { email: '<EMAIL>', password: 'admin123' },
            { email: '<EMAIL>', password: 'manager123' },
            { email: '<EMAIL>', password: 'demo' }
        ];

        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        return validUsers.some(user => 
            user.email.toLowerCase() === email.toLowerCase() && 
            user.password === password
        );
    }

    extractNameFromEmail(email) {
        const name = email.split('@')[0];
        return name.charAt(0).toUpperCase() + name.slice(1);
    }

    getUserRole(email) {
        if (email.includes('admin')) return 'admin';
        if (email.includes('manager')) return 'manager';
        return 'agent';
    }

    handleLogout() {
        console.log('🔐 Logging out user...');
        
        // Clear session data
        this.currentUser = null;
        localStorage.removeItem('zurich_user');
        
        // Show login page
        this.showLogin();
        
        // Clear any form data
        const loginForm = document.getElementById('login-form');
        if (loginForm) {
            loginForm.reset();
        }
    }

    showLogin() {
        console.log('📱 Showing login page...');
        
        // Hide loading and dashboard
        document.getElementById('loading').style.display = 'none';
        document.getElementById('main-dashboard').style.display = 'none';
        
        // Show login page
        document.getElementById('login-page').style.display = 'block';
        
        // Focus on email field
        setTimeout(() => {
            const emailField = document.getElementById('email');
            if (emailField) {
                emailField.focus();
            }
        }, 100);
    }

    showDashboard() {
        console.log('📊 Showing dashboard...');
        
        // Hide loading and login
        document.getElementById('loading').style.display = 'none';
        document.getElementById('login-page').style.display = 'none';
        
        // Show dashboard
        document.getElementById('main-dashboard').style.display = 'block';
        
        // Update user info in header
        this.updateUserInfo();
        
        // Initialize dashboard data
        if (window.dashboardManager) {
            window.dashboardManager.loadDashboardData();
        }
    }

    updateUserInfo() {
        if (this.currentUser) {
            const userNameElement = document.getElementById('user-name');
            if (userNameElement) {
                userNameElement.textContent = this.currentUser.name;
            }
        }
    }

    showError(message) {
        // Create or update error message
        let errorElement = document.querySelector('.login-error');
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'login-error';
            errorElement.style.cssText = `
                background-color: rgba(217, 63, 76, 0.1);
                color: #d93f4c;
                padding: 12px;
                border-radius: 4px;
                margin-bottom: 16px;
                font-size: 14px;
                border: 1px solid rgba(217, 63, 76, 0.2);
            `;
            
            const loginForm = document.getElementById('login-form');
            if (loginForm) {
                loginForm.insertBefore(errorElement, loginForm.firstChild);
            }
        }
        
        errorElement.textContent = message;
        
        // Auto-hide after 5 seconds
        setTimeout(() => {
            if (errorElement && errorElement.parentNode) {
                errorElement.remove();
            }
        }, 5000);
    }

    // Public methods for other modules
    getCurrentUser() {
        return this.currentUser;
    }

    isAuthenticated() {
        return this.currentUser !== null;
    }

    hasRole(role) {
        return this.currentUser && this.currentUser.role === role;
    }

    getSupabaseClient() {
        return this.supabaseClient;
    }
}

// Initialize authentication when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.authManager = new AuthManager();
});
