// Tickets Module for Individual Claim Management
class TicketManager {
    constructor() {
        this.supabaseClient = null;
        this.currentTicket = null;
        this.ticketHistory = [];
        this.init();
    }

    async init() {
        console.log('🎫 Initializing Ticket Manager...');
        this.waitForAuth();
    }

    waitForAuth() {
        if (window.authManager && window.authManager.getSupabaseClient()) {
            this.supabaseClient = window.authManager.getSupabaseClient();
            this.setupEventListeners();
            console.log('✅ Ticket Manager ready');
        } else {
            setTimeout(() => this.waitForAuth(), 100);
        }
    }

    setupEventListeners() {
        // Status update handlers will be added dynamically when ticket loads
    }

    async loadTicket(claimReference) {
        console.log(`🎫 Loading ticket for claim: ${claimReference}`);
        
        try {
            // Load claim data
            const { data: claim, error } = await this.supabaseClient
                .from('claims')
                .select('*')
                .eq('claim_reference', claimReference)
                .single();

            if (error) {
                throw error;
            }

            if (!claim) {
                throw new Error(`Claim ${claimReference} not found`);
            }

            this.currentTicket = claim;
            
            // Load ticket history (mock for now)
            this.loadTicketHistory(claim.id);
            
            // Render ticket details
            this.renderTicketDetails();
            
            console.log('✅ Ticket loaded successfully');
            
        } catch (error) {
            console.error('❌ Error loading ticket:', error);
            this.showError(`Failed to load ticket for claim ${claimReference}`);
        }
    }

    async loadTicketHistory(claimId) {
        // Mock ticket history - in production this would come from a real audit log
        this.ticketHistory = [
            {
                id: 1,
                timestamp: this.currentTicket.created_at,
                action: 'Claim Created',
                description: 'Initial claim submission received',
                user: 'System',
                status: 'NEW'
            },
            {
                id: 2,
                timestamp: this.currentTicket.updated_at,
                action: 'Status Updated',
                description: `Status changed to ${this.currentTicket.status || this.currentTicket.workflow_status}`,
                user: this.currentTicket.assigned_agent || 'System',
                status: this.currentTicket.status || this.currentTicket.workflow_status
            }
        ];
    }

    renderTicketDetails() {
        const container = document.querySelector('.ticket-container');
        if (!container) return;

        const claim = this.currentTicket;
        const status = claim.status || claim.workflow_status || 'unknown';
        const statusClass = status.toLowerCase().replace('_', '-');

        container.innerHTML = `
            <div class="ticket-header">
                <div class="ticket-title">
                    <h3>Claim ${claim.claim_reference}</h3>
                    <span class="status-badge ${statusClass}">
                        ${this.formatStatus(status)}
                    </span>
                </div>
                <div class="ticket-actions">
                    <button class="btn btn-outline" onclick="ticketManager.printTicket()">
                        Print
                    </button>
                    <button class="btn btn-primary" onclick="ticketManager.showStatusUpdateModal()">
                        Update Status
                    </button>
                </div>
            </div>

            <div class="ticket-content">
                <div class="ticket-grid">
                    <div class="ticket-main">
                        <div class="ticket-section">
                            <h4>Claim Details</h4>
                            <div class="detail-grid">
                                <div class="detail-item">
                                    <label>Claim Reference</label>
                                    <span>${claim.claim_reference}</span>
                                </div>
                                <div class="detail-item">
                                    <label>Status</label>
                                    <span class="status-badge ${statusClass}">
                                        ${this.formatStatus(status)}
                                    </span>
                                </div>
                                <div class="detail-item">
                                    <label>Created Date</label>
                                    <span>${this.formatDateTime(claim.created_at)}</span>
                                </div>
                                <div class="detail-item">
                                    <label>Last Updated</label>
                                    <span>${this.formatDateTime(claim.updated_at)}</span>
                                </div>
                                <div class="detail-item">
                                    <label>Assigned Agent</label>
                                    <span>${claim.assigned_agent || 'Unassigned'}</span>
                                </div>
                                <div class="detail-item">
                                    <label>Priority</label>
                                    <span class="priority-badge ${this.calculatePriority(claim).toLowerCase()}">
                                        ${this.calculatePriority(claim)}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="ticket-section">
                            <h4>AI Analysis Results</h4>
                            <div class="analysis-grid">
                                <div class="analysis-card">
                                    <h5>Level 01 - Basic Analysis</h5>
                                    <div class="analysis-status">
                                        ${claim.level01_analysis ? '✅ Complete' : '⏳ Pending'}
                                    </div>
                                    ${claim.level01_analysis ? `
                                        <div class="analysis-summary">
                                            <p><strong>Confidence:</strong> ${claim.level01_confidence || 'N/A'}%</p>
                                            <p><strong>Result:</strong> ${claim.level01_result || 'Processing'}</p>
                                        </div>
                                    ` : ''}
                                </div>
                                
                                <div class="analysis-card">
                                    <h5>Level 02 - Coverage Analysis</h5>
                                    <div class="analysis-status">
                                        ${claim.level02_analysis ? '✅ Complete' : '⏳ Pending'}
                                    </div>
                                    ${claim.level02_analysis ? `
                                        <div class="analysis-summary">
                                            <p><strong>Coverage:</strong> ${claim.coverage_decision || 'Under Review'}</p>
                                        </div>
                                    ` : ''}
                                </div>
                                
                                <div class="analysis-card">
                                    <h5>Level 03 - Fault Analysis</h5>
                                    <div class="analysis-status">
                                        ${claim.level03_analysis ? '✅ Complete' : '⏳ Pending'}
                                    </div>
                                    ${claim.level03_analysis ? `
                                        <div class="analysis-summary">
                                            <p><strong>Fault:</strong> ${claim.fault_determination || 'Under Review'}</p>
                                        </div>
                                    ` : ''}
                                </div>
                                
                                <div class="analysis-card">
                                    <h5>Level 04 - Settlement</h5>
                                    <div class="analysis-status">
                                        ${claim.level04_analysis ? '✅ Complete' : '⏳ Pending'}
                                    </div>
                                    ${claim.level04_analysis ? `
                                        <div class="analysis-summary">
                                            <p><strong>Amount:</strong> ${claim.settlement_amount || 'TBD'}</p>
                                        </div>
                                    ` : ''}
                                </div>
                            </div>
                        </div>

                        <div class="ticket-section">
                            <h4>Documents</h4>
                            <div class="documents-list">
                                ${this.renderDocumentsList(claim)}
                            </div>
                        </div>
                    </div>

                    <div class="ticket-sidebar">
                        <div class="ticket-section">
                            <h4>Activity Timeline</h4>
                            <div class="timeline">
                                ${this.renderTimeline()}
                            </div>
                        </div>

                        <div class="ticket-section">
                            <h4>Quick Actions</h4>
                            <div class="quick-actions">
                                <button class="action-btn full-width" onclick="ticketManager.assignToMe()">
                                    Assign to Me
                                </button>
                                <button class="action-btn full-width" onclick="ticketManager.requestDocuments()">
                                    Request Documents
                                </button>
                                <button class="action-btn full-width" onclick="ticketManager.scheduleCall()">
                                    Schedule Call
                                </button>
                                <button class="action-btn full-width" onclick="ticketManager.escalate()">
                                    Escalate
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add ticket-specific styles
        this.addTicketStyles();
    }

    renderDocumentsList(claim) {
        // Mock documents list - in production this would come from document storage
        const documents = [
            { name: 'Initial Claim Form', status: 'Received', date: claim.created_at },
            { name: 'Police Report', status: 'Pending', date: null },
            { name: 'Photos', status: 'Received', date: claim.updated_at },
            { name: 'Repair Estimate', status: 'Pending', date: null }
        ];

        return documents.map(doc => `
            <div class="document-item">
                <div class="document-info">
                    <span class="document-name">${doc.name}</span>
                    <span class="document-status status-${doc.status.toLowerCase()}">${doc.status}</span>
                </div>
                <div class="document-date">
                    ${doc.date ? this.formatDate(doc.date) : 'Not received'}
                </div>
            </div>
        `).join('');
    }

    renderTimeline() {
        return this.ticketHistory.map(event => `
            <div class="timeline-item">
                <div class="timeline-marker"></div>
                <div class="timeline-content">
                    <div class="timeline-header">
                        <strong>${event.action}</strong>
                        <span class="timeline-time">${this.formatDateTime(event.timestamp)}</span>
                    </div>
                    <div class="timeline-description">${event.description}</div>
                    <div class="timeline-user">by ${event.user}</div>
                </div>
            </div>
        `).join('');
    }

    formatStatus(status) {
        const statusMap = {
            'NEW': 'New',
            'PROCESSING': 'Processing',
            'UNDER_REVIEW': 'Under Review',
            'PENDING_DOCUMENTS': 'Pending Documents',
            'APPROVED': 'Approved',
            'DENIED': 'Denied',
            'COMPLETED': 'Completed',
            'CANCELLED': 'Cancelled'
        };

        return statusMap[status?.toUpperCase()] || status || 'Unknown';
    }

    calculatePriority(claim) {
        // Simple priority calculation
        const created = new Date(claim.created_at);
        const now = new Date();
        const daysDiff = Math.floor((now - created) / (1000 * 60 * 60 * 24));

        if (daysDiff > 30) return 'High';
        if (daysDiff > 14) return 'Medium';
        return 'Low';
    }

    formatDate(dateString) {
        if (!dateString) return 'N/A';
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }

    formatDateTime(dateString) {
        if (!dateString) return 'N/A';
        return new Date(dateString).toLocaleString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    // Action handlers
    printTicket() {
        window.print();
    }

    showStatusUpdateModal() {
        // Simple prompt for demo - in production this would be a proper modal
        const newStatus = prompt('Enter new status:', this.currentTicket.status || this.currentTicket.workflow_status);
        if (newStatus && newStatus !== this.currentTicket.status) {
            this.updateStatus(newStatus);
        }
    }

    async updateStatus(newStatus) {
        try {
            const { error } = await this.supabaseClient
                .from('claims')
                .update({ 
                    status: newStatus,
                    updated_at: new Date().toISOString()
                })
                .eq('id', this.currentTicket.id);

            if (error) throw error;

            // Update local data
            this.currentTicket.status = newStatus;
            this.currentTicket.updated_at = new Date().toISOString();

            // Add to history
            this.ticketHistory.push({
                id: this.ticketHistory.length + 1,
                timestamp: new Date().toISOString(),
                action: 'Status Updated',
                description: `Status changed to ${newStatus}`,
                user: window.authManager.getCurrentUser()?.name || 'Current User',
                status: newStatus
            });

            // Re-render
            this.renderTicketDetails();

            if (window.zurichDashboard) {
                window.zurichDashboard.handleSuccess('Status updated successfully');
            }

        } catch (error) {
            console.error('❌ Error updating status:', error);
            this.showError('Failed to update status');
        }
    }

    assignToMe() {
        const currentUser = window.authManager.getCurrentUser();
        if (currentUser) {
            this.updateAssignedAgent(currentUser.name);
        }
    }

    async updateAssignedAgent(agentName) {
        try {
            const { error } = await this.supabaseClient
                .from('claims')
                .update({ 
                    assigned_agent: agentName,
                    updated_at: new Date().toISOString()
                })
                .eq('id', this.currentTicket.id);

            if (error) throw error;

            this.currentTicket.assigned_agent = agentName;
            this.renderTicketDetails();

            if (window.zurichDashboard) {
                window.zurichDashboard.handleSuccess('Claim assigned successfully');
            }

        } catch (error) {
            console.error('❌ Error assigning claim:', error);
            this.showError('Failed to assign claim');
        }
    }

    requestDocuments() {
        if (window.zurichDashboard) {
            window.zurichDashboard.handleSuccess('Document request sent to claimant');
        }
    }

    scheduleCall() {
        if (window.zurichDashboard) {
            window.zurichDashboard.handleSuccess('Call scheduled for tomorrow at 2 PM');
        }
    }

    escalate() {
        if (window.zurichDashboard) {
            window.zurichDashboard.handleSuccess('Claim escalated to supervisor');
        }
    }

    addTicketStyles() {
        if (document.getElementById('ticket-styles')) return;

        const style = document.createElement('style');
        style.id = 'ticket-styles';
        style.textContent = `
            .ticket-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 2rem;
                padding-bottom: 1rem;
                border-bottom: 1px solid var(--zd-color-grey-200);
            }
            
            .ticket-title {
                display: flex;
                align-items: center;
                gap: 1rem;
            }
            
            .ticket-grid {
                display: grid;
                grid-template-columns: 2fr 1fr;
                gap: 2rem;
            }
            
            .ticket-section {
                margin-bottom: 2rem;
            }
            
            .ticket-section h4 {
                margin-bottom: 1rem;
                color: var(--zd-color-grey-800);
                font-size: 18px;
            }
            
            .detail-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 1rem;
            }
            
            .detail-item {
                display: flex;
                flex-direction: column;
                gap: 0.25rem;
            }
            
            .detail-item label {
                font-size: 12px;
                font-weight: 600;
                color: var(--zd-color-grey-600);
                text-transform: uppercase;
            }
            
            .analysis-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 1rem;
            }
            
            .analysis-card {
                border: 1px solid var(--zd-color-grey-200);
                border-radius: 8px;
                padding: 1rem;
                background: var(--zd-color-grey-100);
            }
            
            .analysis-card h5 {
                margin-bottom: 0.5rem;
                font-size: 14px;
            }
            
            .analysis-status {
                margin-bottom: 0.5rem;
                font-size: 12px;
                font-weight: 600;
            }
            
            .documents-list {
                display: flex;
                flex-direction: column;
                gap: 0.5rem;
            }
            
            .document-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.75rem;
                border: 1px solid var(--zd-color-grey-200);
                border-radius: 4px;
            }
            
            .document-info {
                display: flex;
                align-items: center;
                gap: 1rem;
            }
            
            .timeline {
                display: flex;
                flex-direction: column;
                gap: 1rem;
            }
            
            .timeline-item {
                display: flex;
                gap: 0.75rem;
            }
            
            .timeline-marker {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                background: var(--zurich-blue);
                margin-top: 0.5rem;
                flex-shrink: 0;
            }
            
            .timeline-content {
                flex: 1;
            }
            
            .timeline-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 0.25rem;
            }
            
            .timeline-time {
                font-size: 11px;
                color: var(--zd-color-grey-600);
            }
            
            .timeline-description {
                font-size: 13px;
                color: var(--zd-color-grey-700);
                margin-bottom: 0.25rem;
            }
            
            .timeline-user {
                font-size: 11px;
                color: var(--zd-color-grey-600);
                font-style: italic;
            }
            
            .quick-actions {
                display: flex;
                flex-direction: column;
                gap: 0.5rem;
            }
            
            .full-width {
                width: 100%;
            }
            
            @media (max-width: 768px) {
                .ticket-grid {
                    grid-template-columns: 1fr;
                }
                
                .ticket-header {
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 1rem;
                }
            }
        `;
        
        document.head.appendChild(style);
    }

    showError(message) {
        console.error('Ticket Error:', message);
        if (window.zurichDashboard) {
            window.zurichDashboard.handleError(new Error(message), 'Ticket');
        }
    }

    // Public methods
    getCurrentTicket() {
        return this.currentTicket;
    }

    refresh() {
        if (this.currentTicket) {
            this.loadTicket(this.currentTicket.claim_reference);
        }
    }
}

// Initialize ticket manager when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.ticketManager = new TicketManager();
});
