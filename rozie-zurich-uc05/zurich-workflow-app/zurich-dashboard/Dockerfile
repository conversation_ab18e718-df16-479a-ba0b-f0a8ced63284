# Zurich Dashboard - Multi-stage Docker Build
# Stage 1: Build environment
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy application source
COPY . .

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S zurich -u 1001

# Stage 2: Production environment
FROM node:18-alpine AS production

# Set working directory
WORKDIR /app

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S zurich -u 1001

# Copy built application from builder stage
COPY --from=builder --chown=zurich:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=zurich:nodejs /app/package*.json ./
COPY --from=builder --chown=zurich:nodejs /app/server.js ./
COPY --from=builder --chown=zurich:nodejs /app/index.html ./
COPY --from=builder --chown=zurich:nodejs /app/styles ./styles
COPY --from=builder --chown=zurich:nodejs /app/js ./js
COPY --from=builder --chown=zurich:nodejs /app/assets ./assets

# Set environment variables
ENV NODE_ENV=production
ENV PORT=2000
ENV ZENDESK_SUBDOMAIN=d3v-rozieai5417
ENV ZENDESK_EMAIL=<EMAIL>

# Expose port
EXPOSE 2000

# Switch to non-root user
USER zurich

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:2000/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Start the application
CMD ["node", "server.js"]
