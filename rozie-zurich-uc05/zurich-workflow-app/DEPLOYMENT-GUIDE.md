# 🚀 Selective Component Deployment Guide

## 📋 Overview

This project supports intelligent selective deployment - only the components you change get deployed, saving time and reducing risk.

## 🎯 How It Works

### **Automatic Detection**
The workflow automatically detects which components changed:

| Component | Triggers When Changed |
|-----------|----------------------|
| **Frontend** | `frontend/**`, `frontend.Dockerfile` |
| **Backend** | `backend/**`, `Dockerfile`, `requirements.txt`, `baml_models/**` |
| **Dashboard** | `zurich-dashboard/**` |
| **N8N** | `n8n.Dockerfile`, `docker-compose.yml` |
| **Nginx** | `nginx/**` (triggers frontend deployment too) |

### **Manual Deployment**
You can also manually trigger deployment of specific components:

1. Go to **Actions** tab in GitHub
2. Select **"Selective Component Deployment"**
3. Click **"Run workflow"**
4. Choose which components to deploy:
   - ☑️ Deploy all components
   - ☑️ Force deploy frontend
   - ☑️ Force deploy backend
   - ☑️ Force deploy dashboard
   - ☑️ Force deploy N8N

## 🔄 Deployment Scenarios

### **Scenario 1: Frontend Changes Only**
```bash
# You modify: frontend/index.html
# Result: Only frontend + nginx containers get rebuilt and deployed
# Time saved: ~70% faster than full deployment
```

### **Scenario 2: Backend API Changes**
```bash
# You modify: backend/src/api/main.py
# Result: Only backend container gets rebuilt and deployed
# Time saved: ~60% faster than full deployment
```

### **Scenario 3: Dashboard Updates**
```bash
# You modify: zurich-dashboard/index.html
# Result: Only dashboard container gets rebuilt and deployed
# Time saved: ~80% faster than full deployment
```

### **Scenario 4: N8N Configuration**
```bash
# You modify: n8n.Dockerfile or docker-compose.yml
# Result: Only N8N container gets rebuilt and deployed
# Time saved: ~75% faster than full deployment
```

### **Scenario 5: Multiple Components**
```bash
# You modify: frontend/index.html + backend/main.py
# Result: Both frontend and backend get deployed
# Time saved: ~40% faster than full deployment
```

## 🌐 Access URLs After Deployment

| Service | URL | Description |
|---------|-----|-------------|
| **Frontend** | `https://rozieai-zrh-uc05.dev-scc-demo.rozie.ai/` | Main dashboard |
| **Backend API** | `https://rozieai-zrh-uc05.dev-scc-demo.rozie.ai/docs` | API documentation |
| **Dashboard** | `https://rozieai-zrh-uc05.dev-scc-demo.rozie.ai/dashboard/` | Management UI |
| **N8N** | `https://n8n-zurich.dev-scc-demo.rozie.ai` | Workflow automation |
| **Health Check** | `https://rozieai-zrh-uc05.dev-scc-demo.rozie.ai/health` | System status |

## 🔧 Required GitHub Secrets

Make sure these secrets are configured in your repository:

```
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
```

## 📊 Deployment Status

The workflow provides detailed status for each component:

```
📋 DEPLOYMENT SUMMARY
====================
Frontend: success ✅
Backend: skipped (no changes)
Dashboard: success ✅
N8N: skipped (no changes)
```

## 🚨 Troubleshooting

### **If a component fails to deploy:**
1. Check the GitHub Actions logs
2. Verify the container builds locally:
   ```bash
   # Test frontend build
   docker build -f frontend.Dockerfile .
   
   # Test backend build
   docker build -f Dockerfile .
   
   # Test dashboard build
   docker build -f zurich-dashboard/Dockerfile ./zurich-dashboard
   
   # Test N8N build
   docker build -f n8n.Dockerfile .
   ```

### **Force deploy all components:**
1. Go to Actions → "Selective Component Deployment"
2. Click "Run workflow"
3. Check "Deploy all components"
4. Click "Run workflow"

## 💡 Best Practices

1. **Small Changes**: Make focused changes to single components when possible
2. **Test Locally**: Always test builds locally before pushing
3. **Monitor Logs**: Check deployment logs for any issues
4. **Rollback Plan**: Keep previous working commits tagged for easy rollback

## 🎯 Benefits

- ⚡ **Faster Deployments**: Only deploy what changed
- 🛡️ **Reduced Risk**: Smaller deployment surface area
- 💰 **Cost Savings**: Less compute time and bandwidth
- 🔍 **Better Debugging**: Easier to isolate issues
- 🚀 **Developer Productivity**: Quick iterations on specific components
