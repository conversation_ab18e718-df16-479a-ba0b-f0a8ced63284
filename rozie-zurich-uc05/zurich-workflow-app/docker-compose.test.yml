# =============================================================================
# 🧪 LOCAL TESTING DOCKER COMPOSE
# =============================================================================
# Simplified version for local testing before AWS deployment
# Usage: docker-compose -f docker-compose.test.yml up --build

services:
  # =============================================================================
  # 🚀 BACKEND SERVICE
  # =============================================================================
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: zurich-backend-test
    ports:
      - "8000:8000"
    environment:
      # Load from .env file
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - SUPPORT_SUBDOMAIN=${SUPPORT_SUBDOMAIN}
      - SUPPORT_EMAIL=${SUPPORT_EMAIL}
      - SUPPORT_TOKEN=${SUPPORT_TOKEN}
      - BAML_ENVIRONMENT=development
      - FASTAPI_ENV=development
      - LOG_LEVEL=INFO
    command: uvicorn backend.src.api.main:app --host 0.0.0.0 --port 8000 --reload
    volumes:
      - ./logs:/app/logs
    networks:
      - test-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # =============================================================================
  # 🎨 FRONTEND SERVICE
  # =============================================================================
  frontend:
    build:
      context: .
      dockerfile: frontend.Dockerfile
    container_name: zurich-frontend-test
    ports:
      - "3000:3000"
    environment:
      - BACKEND_URL=http://backend:8000
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
    depends_on:
      - backend
    networks:
      - test-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 10s

  # =============================================================================
  # 📊 DASHBOARD SERVICE
  # =============================================================================
  dashboard:
    build:
      context: ./zurich-dashboard
      dockerfile: Dockerfile
    container_name: zurich-dashboard-test
    ports:
      - "2000:2000"
    environment:
      - NODE_ENV=production
      - PORT=2000
      - SUPPORT_SUBDOMAIN=${SUPPORT_SUBDOMAIN}
      - SUPPORT_EMAIL=${SUPPORT_EMAIL}
      - SUPPORT_TOKEN=${SUPPORT_TOKEN}
      - BACKEND_URL=http://backend:8000
    depends_on:
      - backend
    networks:
      - test-network
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:2000/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # =============================================================================
  # 🔄 N8N SERVICE
  # =============================================================================
  n8n:
    build:
      context: .
      dockerfile: n8n.Dockerfile
    container_name: zurich-n8n-test
    ports:
      - "5678:5678"
    environment:
      - N8N_HOST=0.0.0.0
      - N8N_PORT=5678
      - N8N_PROTOCOL=http
    networks:
      - test-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5678/healthz"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 20s

  # =============================================================================
  # 🌐 NGINX REVERSE PROXY
  # =============================================================================
  nginx:
    build:
      context: ./nginx
      dockerfile: Dockerfile
    container_name: zurich-nginx-test
    ports:
      - "80:80"
    depends_on:
      - backend
      - frontend
      - dashboard
      - n8n
    networks:
      - test-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/readiness"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 10s

# =============================================================================
# 🌐 NETWORK CONFIGURATION
# =============================================================================
networks:
  test-network:
    driver: bridge

# =============================================================================
# 📁 VOLUME CONFIGURATION
# =============================================================================
volumes:
  logs:
    driver: local
