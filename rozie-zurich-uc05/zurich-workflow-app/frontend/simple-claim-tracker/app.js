// Real-time Claims Tracker with Supabase Integration
class ClaimTracker {
    constructor() {
        this.supabaseClient = null;
        this.currentClaim = null;
        this.realTimeSubscription = null;
        this.init();
    }

    async init() {
        console.log('🚀 Initializing Claim Tracker...');
        
        // Initialize Supabase
        await this.initializeSupabase();
        
        // Setup event listeners
        this.setupEventListeners();
        
        // Load default claim if provided
        const urlParams = new URLSearchParams(window.location.search);
        const claimRef = urlParams.get('claim') || 'CLM-85228383';
        document.getElementById('claimReferenceInput').value = claimRef;
        
        // Auto-load the claim
        await this.trackClaim(claimRef);
    }

    async initializeSupabase() {
        try {
            // Supabase configuration - using correct credentials from enhanced claims reviewer
            const supabaseUrl = 'https://tlduggpohclrgxbvuzhd.supabase.co';
            const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRsZHVnZ3BvaGNscmd4YnZ1emhkIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDcwMTAzOCwiZXhwIjoyMDY2Mjc3MDM4fQ.wDi5bG1O81a3HaXOujWshJZoGV6AhFgEuaoVcjam0SE';

            this.supabaseClient = window.supabase.createClient(supabaseUrl, supabaseKey);
            console.log('✅ Supabase client initialized');
            
            // Test connection
            const { error } = await this.supabaseClient
                .from('claims')
                .select('count')
                .limit(1);
                
            if (error) {
                console.error('❌ Supabase connection test failed:', error);
                throw error;
            }
            
            console.log('✅ Supabase connection successful');
        } catch (error) {
            console.error('❌ Failed to initialize Supabase:', error);
            this.showError('Failed to connect to database. Please try again later.');
        }
    }

    setupEventListeners() {
        // Track claim button
        document.getElementById('trackClaimBtn').addEventListener('click', () => {
            const claimRef = document.getElementById('claimReferenceInput').value.trim();
            if (claimRef) {
                this.trackClaim(claimRef);
            }
        });

        // Enter key on input
        document.getElementById('claimReferenceInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                const claimRef = e.target.value.trim();
                if (claimRef) {
                    this.trackClaim(claimRef);
                }
            }
        });

        // Retry button
        document.getElementById('retryBtn').addEventListener('click', () => {
            const claimRef = document.getElementById('claimReferenceInput').value.trim();
            if (claimRef) {
                this.trackClaim(claimRef);
            }
        });
    }

    async trackClaim(claimReference) {
        console.log(`🔍 Tracking claim: ${claimReference}`);
        
        this.showLoading();
        this.hideError();
        this.hideClaimInfo();

        try {
            // Fetch claim data from Supabase
            const { data: claimData, error } = await this.supabaseClient
                .from('claims')
                .select('*')
                .eq('claim_reference', claimReference)
                .single();

            if (error) {
                console.error('❌ Error fetching claim:', error);
                if (error.code === 'PGRST116') {
                    this.showError(`Claim ${claimReference} not found. Please check the reference number.`);
                } else {
                    this.showError('Error fetching claim data. Please try again.');
                }
                return;
            }

            console.log('✅ Claim data retrieved:', claimData);
            console.log('📊 Raw workflow_status:', claimData.workflow_status);
            console.log('📊 Status type:', typeof claimData.workflow_status);
            this.currentClaim = claimData;

            // Display claim information
            this.displayClaimInfo(claimData);
            
            // Setup real-time updates
            this.setupRealTimeUpdates(claimReference);
            
        } catch (error) {
            console.error('❌ Error tracking claim:', error);
            this.showError('An unexpected error occurred. Please try again.');
        } finally {
            this.hideLoading();
        }
    }

    displayClaimInfo(claimData) {
        console.log('📊 Displaying claim information...');

        // Use the most accurate status - prefer 'status' field over 'workflow_status'
        const actualStatus = claimData.status || claimData.workflow_status;
        console.log('📊 Using status:', actualStatus, '(from status field:', claimData.status, ', workflow_status:', claimData.workflow_status, ')');

        // Map technical status to user-friendly status
        const userFriendlyStatus = this.mapStatusToUserFriendly(actualStatus);

        // Populate claim details
        const claimDetails = document.getElementById('claimDetails');
        claimDetails.innerHTML = `
            <div class="claim-item">
                <span class="label">Claim Reference:</span>
                <span class="value">${claimData.claim_reference}</span>
            </div>
            <div class="claim-item">
                <span class="label">Status:</span>
                <span class="value status-${actualStatus}">${userFriendlyStatus}</span>
            </div>
            <div class="claim-item">
                <span class="label">Submitted:</span>
                <span class="value">${this.formatDate(claimData.created_at)}</span>
            </div>
            <div class="claim-item">
                <span class="label">Last Updated:</span>
                <span class="value">${this.formatDate(claimData.updated_at)}</span>
            </div>
            ${claimData.assigned_agent ? `
            <div class="claim-item">
                <span class="label">Assigned Agent:</span>
                <span class="value">${claimData.assigned_agent}</span>
            </div>
            ` : ''}
        `;

        // Update progress bar
        const progressPercentage = this.calculateProgress(actualStatus);
        document.getElementById('progressPercentage').textContent = progressPercentage + '%';
        document.getElementById('progressFill').style.width = progressPercentage + '%';

        // Generate and display timeline
        this.generateTimeline(claimData, actualStatus);

        // Show claim info
        this.showClaimInfo();
    }

    mapStatusToUserFriendly(technicalStatus) {
        const statusMap = {
            // Database constraint statuses
            'RECEIVED': 'Claim Received',
            'PROCESSING': 'Under Review',
            'UNDER_REVIEW': 'Under Investigation',
            'PENDING_DOCUMENTS': 'Pending Documents',
            'APPROVED': 'Approved',
            'DENIED': 'Denied',
            'COMPLETED': 'Completed',
            'CANCELLED': 'Cancelled',

            // Legacy/current data statuses
            'NEW': 'Claim Received',
            'IN_PROGRESS': 'Under Review',
            'REVIEW': 'Under Investigation',
            'INVESTIGATION': 'Under Investigation',
            'HUMAN_REVIEW': 'Expert Review',
            'DECISION': 'Final Assessment',
            'EMAIL_PROCESSING': 'Processing',

            // Additional statuses found in data
            'APPROVED': 'Approved'
        };

        return statusMap[technicalStatus?.toUpperCase()] || 'Processing';
    }

    calculateProgress(status) {
        const progressMap = {
            // Database constraint statuses
            'RECEIVED': 15,
            'PROCESSING': 30,
            'UNDER_REVIEW': 50,
            'PENDING_DOCUMENTS': 40,
            'APPROVED': 100,
            'DENIED': 100,
            'COMPLETED': 100,
            'CANCELLED': 100,

            // Legacy/current data statuses
            'NEW': 15,
            'IN_PROGRESS': 45,
            'REVIEW': 50,
            'INVESTIGATION': 50,
            'HUMAN_REVIEW': 70,
            'DECISION': 85,
            'EMAIL_PROCESSING': 20,

            // Additional statuses
            'APPROVED': 100
        };

        return progressMap[status?.toUpperCase()] || 15;
    }

    generateTimeline(claimData, actualStatus = null) {
        // Use the passed actualStatus or fall back to workflow_status
        const status = (actualStatus || claimData.status || claimData.workflow_status)?.toUpperCase() || 'RECEIVED';
        console.log('🔄 Generating timeline for status:', status);
        console.log('🔄 Original workflow_status:', claimData.workflow_status);
        console.log('🔄 Original status:', claimData.status);

        // Define standard timeline steps based on actual workflow
        const timelineSteps = [
            {
                id: 1,
                title: 'Claim Received',
                subtitle: 'Initial Processing',
                description: 'Your claim has been received and assigned a reference number.',
                icon: '✓',
                status: 'completed',
                date: claimData.created_at
            },
            {
                id: 2,
                title: 'Document Review',
                subtitle: 'Processing & Validation',
                description: 'Our team is processing and validating your claim documents.',
                icon: '✓',
                status: this.getStepStatus(status, [
                    'PROCESSING', 'UNDER_REVIEW', 'PENDING_DOCUMENTS',
                    'IN_PROGRESS', 'REVIEW', 'INVESTIGATION', 'HUMAN_REVIEW', 'DECISION',
                    'APPROVED', 'DENIED', 'COMPLETED'
                ]),
                date: this.isStatusActive(status, ['PROCESSING', 'IN_PROGRESS']) ? claimData.updated_at : null
            },
            {
                id: 3,
                title: 'Investigation',
                subtitle: 'Detailed Assessment',
                description: 'Conducting thorough investigation of the incident and circumstances.',
                icon: '✓',
                status: this.getStepStatus(status, [
                    'UNDER_REVIEW', 'REVIEW', 'INVESTIGATION', 'HUMAN_REVIEW', 'DECISION',
                    'APPROVED', 'DENIED', 'COMPLETED'
                ]),
                date: this.isStatusActive(status, ['UNDER_REVIEW', 'REVIEW', 'INVESTIGATION']) ? claimData.updated_at : null
            },
            {
                id: 4,
                title: 'Final Decision',
                subtitle: 'Settlement Processing',
                description: 'Final review and decision on your claim.',
                icon: '✓',
                status: this.getStepStatus(status, ['APPROVED', 'DENIED', 'COMPLETED']),
                date: this.isStatusActive(status, ['APPROVED', 'DENIED', 'COMPLETED']) ? claimData.updated_at : null
            }
        ];

        // Handle special cases
        if (status === 'PENDING_DOCUMENTS') {
            timelineSteps[1].title = 'Pending Documents';
            timelineSteps[1].subtitle = 'Additional Information Required';
            timelineSteps[1].description = 'We need additional documents to process your claim. Please check your email for details.';
            timelineSteps[1].icon = '✓';
            timelineSteps[1].status = 'pending_documents';
        }

        if (status === 'CANCELLED') {
            timelineSteps.forEach((step, index) => {
                if (index > 0) {
                    step.status = 'cancelled';
                }
            });
        }

        this.renderTimeline(timelineSteps);
    }

    getStepStatus(currentStatus, requiredStatuses) {
        if (requiredStatuses.includes(currentStatus)) {
            if (currentStatus === 'DENIED') return 'denied';
            if (currentStatus === 'CANCELLED') return 'cancelled';
            if (currentStatus === 'PENDING_DOCUMENTS') return 'pending_documents';
            return 'completed';
        }
        return 'pending';
    }

    isStatusActive(currentStatus, activeStatuses) {
        return activeStatuses.includes(currentStatus);
    }

    renderTimeline(steps) {
        const timeline = document.getElementById('timeline');
        timeline.innerHTML = '';

        steps.forEach(step => {
            const timelineItem = this.createTimelineItem(step);
            timeline.appendChild(timelineItem);
        });
    }

    createTimelineItem(step) {
        const item = document.createElement('div');
        item.className = `timeline-item ${step.status}`;

        let dateHtml = '';
        if (step.date) {
            dateHtml = `<div class="timeline-date">${this.formatDate(step.date)}</div>`;
        }

        // Map status to display text
        const statusTextMap = {
            'completed': 'Completed',
            'denied': 'Denied',
            'cancelled': 'Cancelled',
            'pending_documents': 'Pending Documents',
            'pending': 'Pending'
        };

        const statusText = statusTextMap[step.status] || 'Pending';

        // Use tick mark for all statuses - only show for completed items
        let icon = step.status === 'completed' ? '✓' : '';
        if (step.status === 'denied') icon = '✗';
        if (step.status === 'cancelled') icon = '✗';

        item.innerHTML = `
            <div class="timeline-header">
                <div>
                    <div class="timeline-title">${step.title}</div>
                    <div class="timeline-subtitle">${step.subtitle}</div>
                </div>
                <div class="timeline-status status-${step.status}">
                    <span class="status-icon">${icon}</span>
                    <span>${statusText}</span>
                </div>
            </div>
            <div class="timeline-description">${step.description}</div>
            ${dateHtml}
        `;

        return item;
    }

    setupRealTimeUpdates(claimReference) {
        console.log('🔄 Setting up real-time updates...');
        
        // Remove existing subscription
        if (this.realTimeSubscription) {
            this.realTimeSubscription.unsubscribe();
        }

        // Subscribe to real-time changes
        this.realTimeSubscription = this.supabaseClient
            .channel('claim-updates')
            .on('postgres_changes', 
                { 
                    event: 'UPDATE', 
                    schema: 'public', 
                    table: 'claims',
                    filter: `claim_reference=eq.${claimReference}`
                }, 
                (payload) => {
                    console.log('🔄 Real-time update received:', payload);
                    this.handleRealTimeUpdate(payload.new);
                }
            )
            .subscribe();
    }

    handleRealTimeUpdate(updatedClaim) {
        console.log('🔄 Handling real-time update:', updatedClaim);
        this.currentClaim = updatedClaim;
        this.displayClaimInfo(updatedClaim);
        
        // Show notification
        this.showNotification('Claim status updated!');
    }

    showNotification(message) {
        const notification = document.createElement('div');
        notification.className = 'notification';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4CAF50;
            color: white;
            padding: 12px 20px;
            border-radius: 5px;
            z-index: 1000;
        `;
        
        document.body.appendChild(notification);
        setTimeout(() => notification.remove(), 3000);
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }

    showLoading() {
        document.getElementById('loadingIndicator').style.display = 'block';
    }

    hideLoading() {
        document.getElementById('loadingIndicator').style.display = 'none';
    }

    showClaimInfo() {
        document.getElementById('claimInfoSection').style.display = 'block';
        document.getElementById('timelineSection').style.display = 'block';
    }

    hideClaimInfo() {
        document.getElementById('claimInfoSection').style.display = 'none';
        document.getElementById('timelineSection').style.display = 'none';
    }

    showError(message) {
        document.getElementById('errorMessage').textContent = message;
        document.getElementById('errorSection').style.display = 'block';
    }

    hideError() {
        document.getElementById('errorSection').style.display = 'none';
    }
}

// Initialize the application when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.claimTracker = new ClaimTracker();
});
