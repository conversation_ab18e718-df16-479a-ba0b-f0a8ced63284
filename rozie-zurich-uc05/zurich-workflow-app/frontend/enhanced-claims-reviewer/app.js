// AI-Based Two-Way Highlighting and Auto-Scroll System
class IntelligentHighlighter {
    constructor() {
        this.markInstances = new Map();
        this.entityMappings = new Map();
        this.highlightedEntities = new Set();
        this.currentClaim = null;
        this.scrollSyncEnabled = true;
        this.highlightConnections = new Map();
        this.semanticGroups = new Map();
        this.confidenceThreshold = 0.7;
        this.autoScrollDelay = 300;
        this.lastHighlightTime = 0;
        this.scrollDebounceTimer = null;
        this.activeHighlights = new Set();
        this.contextualRelations = new Map();
        this.smartScrollBehavior = {
            smooth: true,
            block: 'center',
            inline: 'nearest'
        };
    }

    initializeIntelligentHighlighting() {
        console.log('🧠 Initializing AI-based highlighting system...');

        // Wait for DOM to be ready before setting up highlighting
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.setupHighlightingSystem();
            });
        } else {
            // DOM is already ready
            setTimeout(() => this.setupHighlightingSystem(), 100);
        }
    }

    setupHighlightingSystem() {
        console.log('🔧 Setting up highlighting system...');

        // Initialize mark.js instances for each document section
        const emailContent = document.querySelector('.email-body');
        const ocrContents = document.querySelectorAll('.ocr-text-content');
        const analysisCards = document.querySelectorAll('.analysis-card');

        console.log('📍 Found DOM elements:');
        console.log('- Email content:', !!emailContent);
        console.log('- OCR contents:', ocrContents.length);
        console.log('- Analysis cards:', analysisCards.length);

        if (emailContent) {
            this.markInstances.set('email', new Mark(emailContent));
        }

        ocrContents.forEach((content, index) => {
            const documentName = content.dataset.document || `doc-${index}`;
            this.markInstances.set(`ocr-${documentName}`, new Mark(content));
        });

        analysisCards.forEach((card, index) => {
            const cardId = card.id || `analysis-${index}`;
            this.markInstances.set(`analysis-${cardId}`, new Mark(card));
        });

        // Only set up interactions if we have elements
        if (emailContent || ocrContents.length > 0 || analysisCards.length > 0) {
            this.setupIntelligentClickHandlers();
            this.buildSemanticConnections();
            this.enableTwoWayHighlighting();
            console.log('✅ AI highlighting system initialized');
        } else {
            console.log('⚠️ No DOM elements found for highlighting, will retry later');
        }
    }

    buildSemanticConnections() {
        console.log('🔗 Building semantic connections between text elements...');

        if (!this.currentClaim) return;

        // Extract entities and build semantic relationships
        const entities = this.extractEntitiesFromAnalysis(this.currentClaim);

        entities.forEach(entity => {
            const semanticGroup = this.determineSemanticGroup(entity);

            if (!this.semanticGroups.has(semanticGroup)) {
                this.semanticGroups.set(semanticGroup, new Set());
            }

            this.semanticGroups.get(semanticGroup).add(entity);

            // Build contextual relations using AI-like pattern matching
            this.buildContextualRelations(entity);
        });

        console.log('✅ Semantic connections built:', this.semanticGroups.size, 'groups');
    }

    determineSemanticGroup(entity) {
        // AI-based semantic grouping
        const entityValue = entity.value.toLowerCase();

        // Date/Time group
        if (this.isDateTimeEntity(entityValue)) return 'temporal';

        // Financial group
        if (this.isFinancialEntity(entityValue)) return 'financial';

        // Location group
        if (this.isLocationEntity(entityValue)) return 'location';

        // Person group
        if (this.isPersonEntity(entityValue)) return 'person';

        // Medical group
        if (this.isMedicalEntity(entityValue)) return 'medical';

        // Legal group
        if (this.isLegalEntity(entityValue)) return 'legal';

        return 'general';
    }

    isDateTimeEntity(text) {
        const datePatterns = [
            /\d{1,2}\/\d{1,2}\/\d{4}/,
            /\d{4}-\d{2}-\d{2}/,
            /january|february|march|april|may|june|july|august|september|october|november|december/i,
            /\d{1,2}:\d{2}/,
            /morning|afternoon|evening|night/i
        ];
        return datePatterns.some(pattern => pattern.test(text));
    }

    isFinancialEntity(text) {
        const financialPatterns = [
            /\$[\d,]+\.?\d*/,
            /cost|expense|payment|bill|invoice|claim|settlement/i,
            /insurance|coverage|deductible|premium/i
        ];
        return financialPatterns.some(pattern => pattern.test(text));
    }

    isLocationEntity(text) {
        const locationPatterns = [
            /street|avenue|road|boulevard|drive|lane/i,
            /store|hospital|clinic|office|building/i,
            /toronto|ontario|canada|no frills/i
        ];
        return locationPatterns.some(pattern => pattern.test(text));
    }

    isPersonEntity(text) {
        const personPatterns = [
            /jacques|patient|claimant|witness|doctor|manager/i,
            /mr\.|mrs\.|ms\.|dr\./i
        ];
        return personPatterns.some(pattern => pattern.test(text));
    }

    isMedicalEntity(text) {
        const medicalPatterns = [
            /injury|diagnosis|treatment|medication|x-ray|hospital/i,
            /contusion|laceration|fracture|sprain/i,
            /emergency|ambulance|medical/i
        ];
        return medicalPatterns.some(pattern => pattern.test(text));
    }

    isLegalEntity(text) {
        const legalPatterns = [
            /liability|negligence|fault|claim|policy|coverage/i,
            /incident|report|statement|witness/i
        ];
        return legalPatterns.some(pattern => pattern.test(text));
    }

    enableTwoWayHighlighting() {
        console.log('🔄 Enabling two-way highlighting...');

        // Set up bidirectional highlighting between analysis panel and documents
        this.setupAnalysisPanelHighlighting();
        this.setupDocumentHighlighting();
        this.setupCrossReferenceHighlighting();
    }

    setupAnalysisPanelHighlighting() {
        // Add click handlers to analysis cards for highlighting related document text
        const analysisCards = document.querySelectorAll('.analysis-card');

        if (analysisCards.length === 0) {
            console.log('⚠️ No analysis cards found for highlighting setup');
            return;
        }

        analysisCards.forEach(card => {
            if (card && typeof card.addEventListener === 'function') {
                card.addEventListener('click', (e) => {
                    this.handleAnalysisCardClick(card, e);
                });

                // Add hover effects for preview
                card.addEventListener('mouseenter', () => {
                    this.previewRelatedHighlights(card);
                });

                card.addEventListener('mouseleave', () => {
                    this.clearPreviewHighlights();
                });
            }
        });

        console.log(`✅ Set up highlighting for ${analysisCards.length} analysis cards`);
    }

    setupDocumentHighlighting() {
        // Add intelligent click handlers to document text
        if (this.markInstances.size === 0) {
            console.log('⚠️ No mark instances found for document highlighting setup');
            return;
        }

        this.markInstances.forEach((markInstance, documentKey) => {
            const container = markInstance.el;

            if (container && typeof container.addEventListener === 'function') {
                container.addEventListener('click', (e) => {
                    this.handleDocumentTextClick(e, documentKey);
                });

                // Add selection handlers for smart highlighting
                container.addEventListener('mouseup', (e) => {
                    this.handleTextSelection(e, documentKey);
                });
            }
        });

        console.log(`✅ Set up document highlighting for ${this.markInstances.size} containers`);
    }

    handleAnalysisCardClick(card, event) {
        console.log('🎯 Analysis card clicked:', card.id);

        const cardData = this.extractCardData(card);
        const relatedEntities = this.findRelatedEntities(cardData);

        // Clear previous highlights
        this.clearAllHighlights();

        // Highlight related text in documents
        relatedEntities.forEach(entity => {
            this.highlightEntityInDocuments(entity, 'analysis-triggered');
        });

        // Auto-scroll to first related highlight
        this.autoScrollToFirstHighlight(relatedEntities);

        // Update analysis panel to show connections
        this.updateAnalysisPanelConnections(card, relatedEntities);
    }

    handleDocumentTextClick(event, documentKey) {
        const clickedText = this.getClickedText(event);
        if (!clickedText) return;

        console.log('📄 Document text clicked:', clickedText, 'in', documentKey);

        // Find semantically related content
        const relatedContent = this.findSemanticMatches(clickedText);

        // Clear previous highlights
        this.clearAllHighlights();

        // Highlight clicked text and related content
        this.highlightTextAndRelated(clickedText, relatedContent, documentKey);

        // Auto-scroll to related analysis
        this.autoScrollToRelatedAnalysis(relatedContent);
    }

    handleTextSelection(event, documentKey) {
        // Handle text selection for smart highlighting
        const selection = window.getSelection();
        if (selection.toString().trim().length > 0) {
            console.log('📝 Text selected:', selection.toString().trim(), 'in', documentKey);
            // Could implement smart selection highlighting here
        }
    }

    autoScrollToFirstHighlight(entities) {
        if (entities.length === 0) return;

        // Find the first highlighted element in documents
        const firstHighlight = document.querySelector('.intelligent-highlight');
        if (firstHighlight) {
            this.smoothScrollToElement(firstHighlight, 'document');
        }
    }

    autoScrollToRelatedAnalysis(relatedContent) {
        if (relatedContent.length === 0) return;

        // Find related analysis card
        const relatedCard = this.findMostRelevantAnalysisCard(relatedContent);
        if (relatedCard) {
            this.smoothScrollToElement(relatedCard, 'analysis');
        }
    }

    smoothScrollToElement(element, context) {
        console.log('🎯 Auto-scrolling to element in', context);

        // Clear any existing scroll timer
        if (this.scrollDebounceTimer) {
            clearTimeout(this.scrollDebounceTimer);
        }

        // Debounced smooth scroll
        this.scrollDebounceTimer = setTimeout(() => {
            element.scrollIntoView(this.smartScrollBehavior);

            // Add visual emphasis
            this.addScrollEmphasis(element);

        }, this.autoScrollDelay);
    }

    addScrollEmphasis(element) {
        // Add a subtle pulse effect to emphasize the scrolled-to element
        element.classList.add('scroll-emphasis');

        setTimeout(() => {
            element.classList.remove('scroll-emphasis');
        }, 2000);
    }

    extractEntitiesFromAnalysis(claimData) {
        const entities = [];

        // Extract entities from all analysis levels
        ['level01_analysis', 'level02_analysis', 'level03_analysis', 'level04_analysis'].forEach(level => {
            if (claimData[level] && claimData[level].entities) {
                Object.entries(claimData[level].entities).forEach(([entityType, entityValue]) => {
                    entities.push({
                        type: entityType,
                        value: entityValue,
                        level: level,
                        id: `${level}-${entityType}`,
                        confidence: claimData[level].confidence || 0.8
                    });
                });
            }
        });

        return entities;
    }

    // Missing methods for IntelligentHighlighter
    setupIntelligentClickHandlers() {
        console.log('🎯 Setting up intelligent click handlers...');
        // This method sets up the click handlers for intelligent highlighting
        // Implementation can be added later if needed
    }

    buildContextualRelations(entity) {
        // Build contextual relationships between entities
        const entityValue = entity.value.toLowerCase();
        const semanticGroup = this.determineSemanticGroup(entity);

        // Store contextual relations for later use
        if (!this.contextualRelations.has(entityValue)) {
            this.contextualRelations.set(entityValue, {
                entity: entity,
                semanticGroup: semanticGroup,
                relatedEntities: new Set()
            });
        }
    }

    setupCrossReferenceHighlighting() {
        console.log('🔗 Setting up cross-reference highlighting...');
        // Implementation for cross-reference highlighting
    }

    extractCardData(card) {
        // Extract data from analysis card
        return {
            id: card.id,
            title: card.querySelector('.card-title')?.textContent || '',
            content: card.textContent || ''
        };
    }

    findRelatedEntities(cardData) {
        // Find entities related to the clicked card
        return [];
    }

    clearAllHighlights() {
        // Clear all existing highlights
        this.markInstances.forEach((markInstance) => {
            markInstance.unmark();
        });
        this.activeHighlights.clear();
    }

    highlightEntityInDocuments(entity, triggerType) {
        // Highlight entity in all documents
        const entityValue = entity.value || entity;
        const highlightClass = `intelligent-highlight ${triggerType}`;

        this.markInstances.forEach((markInstance) => {
            markInstance.mark(entityValue, {
                className: highlightClass,
                separateWordSearch: false
            });
        });

        this.activeHighlights.add(entityValue);
    }

    updateAnalysisPanelConnections(card, relatedEntities) {
        // Update analysis panel to show connections
        card.classList.add('related-active');

        setTimeout(() => {
            card.classList.remove('related-active');
        }, 3000);
    }

    getClickedText(event) {
        // Get the text that was clicked
        const selection = window.getSelection();
        if (selection.rangeCount > 0) {
            return selection.toString().trim();
        }
        return event.target.textContent?.trim() || '';
    }

    findSemanticMatches(clickedText) {
        // Find semantically related content
        const matches = [];
        const lowerText = clickedText.toLowerCase();

        // Simple semantic matching based on our entity groups
        this.contextualRelations.forEach((relation, key) => {
            if (key.includes(lowerText) || lowerText.includes(key)) {
                matches.push(relation);
            }
        });

        return matches;
    }

    highlightTextAndRelated(clickedText, relatedContent, documentKey) {
        // Highlight clicked text and related content
        this.clearAllHighlights();

        // Highlight the clicked text
        this.highlightEntityInDocuments(clickedText, 'user-triggered');

        // Highlight related content
        relatedContent.forEach(content => {
            this.highlightEntityInDocuments(content.entity.value, 'semantic-related');
        });
    }

    findMostRelevantAnalysisCard(relatedContent) {
        // Find the most relevant analysis card
        if (relatedContent.length === 0) return null;

        // Simple implementation - return first analysis card
        return document.querySelector('.analysis-card');
    }

    previewRelatedHighlights(card) {
        // Show preview highlights on hover
        const cardData = this.extractCardData(card);
        const relatedEntities = this.findRelatedEntities(cardData);

        relatedEntities.forEach(entity => {
            this.highlightEntityInDocuments(entity, 'preview-highlight');
        });
    }

    clearPreviewHighlights() {
        // Clear preview highlights
        document.querySelectorAll('.preview-highlight').forEach(el => {
            el.classList.remove('preview-highlight');
        });
    }

    fuzzyTextMatch(entityText, documentText, threshold = 0.8) {
        // Simple fuzzy matching - you can enhance this with more sophisticated NLP
        const normalizeText = (text) => text.toLowerCase().replace(/[^\w\s]/g, '').trim();
        
        const normalizedEntity = normalizeText(entityText);
        const normalizedDoc = normalizeText(documentText);
        
        // Direct match
        if (normalizedDoc.includes(normalizedEntity)) {
            return normalizedDoc.indexOf(normalizedEntity);
        }

        // Fuzzy match for similar strings
        const words = normalizedEntity.split(' ');
        if (words.length > 1) {
            // Try matching individual words
            const matchedWords = words.filter(word => normalizedDoc.includes(word));
            if (matchedWords.length / words.length >= threshold) {
                return normalizedDoc.indexOf(matchedWords[0]);
            }
        }

        return -1;
    }

    highlightEntity(entityId, entityValue) {
        // Clear previous highlights for this entity
        this.clearEntityHighlight(entityId);

        const highlightOptions = {
            element: 'span',
            className: `entity-highlight entity-${entityId}`,
            separateWordSearch: false,
            accuracy: 'complementary',
            each: (element) => {
                element.setAttribute('data-entity-id', entityId);
                element.setAttribute('data-entity-value', entityValue);
                element.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.handleHighlightClick(entityId);
                });
            }
        };

        // Highlight in all document sections
        this.markInstances.forEach((markInstance, documentKey) => {
            markInstance.mark(entityValue, highlightOptions);
        });

        this.highlightedEntities.add(entityId);
    }

    clearEntityHighlight(entityId) {
        this.markInstances.forEach((markInstance) => {
            markInstance.unmark({
                element: 'span',
                className: `entity-highlight entity-${entityId}`
            });
        });
        this.highlightedEntities.delete(entityId);
    }

    highlightAllEntities() {
        if (!this.currentClaim) return;

        const entities = this.extractEntitiesFromAnalysis(this.currentClaim);
        
        entities.forEach(entity => {
            this.highlightEntity(entity.id, entity.value);
        });
    }

    setupEntityClickHandlers() {
        // Add click handlers to magnifying glass icons
        document.querySelectorAll('.nav-icon').forEach(icon => {
            icon.addEventListener('click', (e) => {
                const entityElement = e.target.closest('.analysis-field-clickable');
                if (entityElement) {
                    const entityText = this.extractEntityTextFromElement(entityElement);
                    const entityId = this.generateEntityId(entityElement);
                    
                    this.scrollToAndHighlightEntity(entityId, entityText);
                }
            });
        });
    }

    scrollToAndHighlightEntity(entityId, entityText) {
        // Find the highlighted element in the document
        const highlightedElement = document.querySelector(`[data-entity-id="${entityId}"]`);
        
        if (highlightedElement) {
            // Scroll to the element
            highlightedElement.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });

            // Add temporary flash animation
            highlightedElement.classList.add('highlight-flash');
            setTimeout(() => {
                highlightedElement.classList.remove('highlight-flash');
            }, 2000);

            // Update right panel to show this entity is active
            this.highlightEntityInPanel(entityId);
        }
    }

    handleHighlightClick(entityId) {
        // When clicking a highlight in the document, highlight the corresponding field in the analysis panel
        this.highlightEntityInPanel(entityId);
        
        // Scroll to the entity in the analysis panel
        const panelElement = document.querySelector(`[data-entity-id="${entityId}"]`);
        if (panelElement) {
            panelElement.scrollIntoView({
                behavior: 'smooth',
                block: 'nearest'
            });
        }
    }

    highlightEntityInPanel(entityId) {
        // Remove previous panel highlights
        document.querySelectorAll('.analysis-field-highlighted').forEach(el => {
            el.classList.remove('analysis-field-highlighted');
        });

        // Add highlight to the corresponding panel element
        const panelElement = document.querySelector(`[data-entity-id="${entityId}"]`);
        if (panelElement) {
            panelElement.classList.add('analysis-field-highlighted');
            
            // Remove highlight after a delay
            setTimeout(() => {
                panelElement.classList.remove('analysis-field-highlighted');
            }, 3000);
        }
    }

    extractEntityTextFromElement(element) {
        // Extract the actual entity text from the analysis panel element
        const valueElement = element.querySelector('.value') || element;
        let text = valueElement.textContent.trim();

        // Clean the text by removing emojis and special characters
        text = this.cleanEntityText(text);

        console.log('🔍 Extracted entity text:', text);
        return text;
    }

    cleanEntityText(text) {
        // Remove emojis and clean the text for highlighting
        return text
            .replace(/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu, '') // Remove emojis
            .replace(/🔍/g, '') // Specifically remove magnifying glass
            .replace(/\s+/g, ' ') // Normalize whitespace
            .trim();
    }

    generateEntityId(element) {
        // Generate a unique ID for the entity based on its position in the analysis
        const section = element.closest('.analysis-section');
        const sectionId = section?.id || 'unknown';
        const labelElement = element.querySelector('.label');
        const label = labelElement?.textContent.trim().replace(/[^\w]/g, '_') || 'unknown';
        
        return `${sectionId}-${label}`;
    }

    // Add navigation icons to analysis fields
    addNavigationIconsToAnalysisFields() {
        document.querySelectorAll('.analysis-field').forEach(field => {
            const valueElement = field.querySelector('.value');
            if (valueElement && this.isNavigableEntity(valueElement.textContent)) {
                const navIcon = document.createElement('span');
                navIcon.className = 'nav-icon';
                navIcon.setAttribute('data-entity-value', valueElement.textContent.trim());
                navIcon.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const entityValue = navIcon.getAttribute('data-entity-value');
                    this.scrollToAndHighlightEntity(field.id || 'unknown', entityValue);
                });
                valueElement.appendChild(navIcon);
            }
        });
    }

    isNavigableEntity(value) {
        // Determine if an entity value is worth highlighting
        return value && 
               typeof value === 'string' && 
               value.length > 2 && 
               value !== 'N/A' && 
               !value.match(/^[\d.%$]+$/); // Skip pure numbers/percentages
    }
}

// Real-Time Claims Liability Reviewer Application with Supabase Integration
class ClaimsReviewer {
    constructor() {
        this.currentClaim = null;
        this.tooltip = null;
        this.expandedSections = new Set();
        this.supabaseClient = null;
        this.realTimeSubscription = null;
        this.highlightElements = new Map();
        this.documentContent = '';
        this.uiMetadata = null;
        this.loadingStates = {
            claim: false,
            analysis: false,
            documents: false
        };
        this.entityHighlighter = new IntelligentHighlighter();
        this.init();
    }

    async init() {
        console.log('🚀 Initializing Real-Time Claims Reviewer...');

        try {
            // Setup UI elements first
            this.setupUIElements();

            // Setup event listeners
            this.setupEventListeners();

            // Initialize Supabase client (but don't fail if it doesn't work)
            await this.initializeSupabase();

            // Load initial claim data
            await this.loadInitialClaimData();

            console.log('✅ Claims Reviewer initialized successfully');
        } catch (error) {
            console.error('❌ Failed to initialize Claims Reviewer:', error);

            // Emergency fallback - load sample data and force display
            console.log('🚨 Emergency initialization - loading sample data...');
            await this.loadSampleData();
            this.forceContentDisplay();

            this.showError('Using sample data due to initialization issues.');
        }
    }

    async waitForSupabase() {
        return new Promise((resolve, reject) => {
            let attempts = 0;
            const maxAttempts = 100; // 10 seconds max wait

            const checkSupabase = () => {
                attempts++;

                console.log(`🔍 Checking Supabase (attempt ${attempts}/${maxAttempts})...`);
                console.log('supabase type:', typeof supabase);
                console.log('supabase object:', supabase);

                if (typeof supabase !== 'undefined' && supabase.createClient) {
                    console.log('✅ Supabase library loaded successfully');
                    resolve();
                } else if (attempts >= maxAttempts) {
                    console.error('❌ Supabase library failed to load after', maxAttempts, 'attempts');
                    reject(new Error(`Supabase library failed to load within 10 seconds. Attempts: ${attempts}`));
                } else {
                    setTimeout(checkSupabase, 100);
                }
            };

            checkSupabase();
        });
    }

    async initializeSupabase() {
        console.log('🔄 Initializing Supabase client...');

        try {
            // Wait for Supabase to be available
            await this.waitForSupabase();

            // Check if Supabase is loaded
            if (typeof supabase === 'undefined') {
                console.log('⚠️ Supabase library not found, using mock mode');
                return;
            }

            // Use actual Supabase configuration from environment or config
            const supabaseUrl = this.getConfig('SUPABASE_URL', 'https://tlduggpohclrgxbvuzhd.supabase.co');
            const supabaseServiceKey = this.getConfig('SUPABASE_SERVICE_ROLE_KEY', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRsZHVnZ3BvaGNscmd4YnZ1emhkIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDcwMTAzOCwiZXhwIjoyMDY2Mjc3MDM4fQ.wDi5bG1O81a3HaXOujWshJZoGV6AhFgEuaoVcjam0SE');

            // Initialize Supabase client with service role key (like explainable-claims-dashboard.js)
            this.supabaseClient = supabase.createClient(supabaseUrl, supabaseServiceKey);

            // Verify the client was created properly
            console.log('🔍 Verifying Supabase client...');
            console.log('Client object:', this.supabaseClient);
            console.log('Client methods:', Object.keys(this.supabaseClient));
            console.log('Has table method:', typeof this.supabaseClient.table);
            console.log('Has from method:', typeof this.supabaseClient.from);

            if (!this.supabaseClient || (typeof this.supabaseClient.table !== 'function' && typeof this.supabaseClient.from !== 'function')) {
                throw new Error('Supabase client created but missing required methods');
            }

            // Test the connection using the same pattern as explainable-claims-dashboard.js
            let query;

            // Try different methods to access the table (like the working dashboard)
            if (typeof this.supabaseClient.table === 'function') {
                console.log('Using table() method for connection test');
                query = this.supabaseClient.table('claims');
            } else if (typeof this.supabaseClient.from === 'function') {
                console.log('Using from() method for connection test');
                query = this.supabaseClient.from('claims');
            } else {
                throw new Error('No valid table access method found');
            }

            console.log('🔍 Testing Supabase connection...');
            const { data, error } = await query
                .select('id, claim_reference')
                .limit(1);

            console.log('🔍 Connection test result:');
            console.log('- Data:', data);
            console.log('- Error:', error);

            if (error) {
                console.error('❌ Supabase connection test failed:', error);
                console.error('❌ Error details:', JSON.stringify(error, null, 2));
                this.supabaseClient = null;
            } else {
                console.log('✅ Supabase client initialized successfully');
            }

        } catch (error) {
            console.error('❌ Failed to initialize Supabase:', error);
            this.supabaseClient = null;
        }
    }

    getConfig(key, defaultValue) {
        // Try to get config from various sources
        if (typeof window !== 'undefined') {
            // Check for config object
            if (window.APP_CONFIG && window.APP_CONFIG[key]) {
                return window.APP_CONFIG[key];
            }
            // Check for meta tags
            const metaTag = document.querySelector(`meta[name="${key}"]`);
            if (metaTag) {
                return metaTag.getAttribute('content');
            }
        }
        return defaultValue;
    }

    setupUIElements() {
        this.tooltip = document.getElementById('tooltip');
        
        // Add loading overlays to each panel
        this.addLoadingOverlays();
        
        // Progress indicators removed per user request
    }

    addLoadingOverlays() {
        const panels = [
            { id: 'documentViewer', selector: '.document-viewer' },
            { id: 'analysisPanel', selector: '.decision-cards' },
            { id: 'detailsPanel', selector: '.details-content' }
        ];

        panels.forEach(panel => {
            const element = document.querySelector(panel.selector);
            if (element && !element.querySelector('.loading-overlay-panel')) {
                const overlay = document.createElement('div');
                overlay.className = 'loading-overlay-panel';
                overlay.innerHTML = `
                    <div class="loading-content">
                        <div class="spinner"></div>
                        <p>Loading ${panel.id.replace(/([A-Z])/g, ' $1').toLowerCase()}...</p>
                    </div>
                `;
                overlay.style.display = 'none';
                element.appendChild(overlay);
            }
        });
    }



    async loadInitialClaimData() {
        // Get claim reference from URL or default
        const urlParams = new URLSearchParams(window.location.search);
        const claimReference = urlParams.get('claim') || 'CLM-85228383';

        console.log(`📋 Loading claim: ${claimReference}`);

        // Show loading state
        this.showLoading();

        try {
            if (this.supabaseClient) {
                console.log('🔄 Attempting to load from Supabase...');
                await this.loadClaimFromSupabase(claimReference);

                // Setup real-time subscriptions
                this.setupRealTimeSubscriptions();

                // Initialize intelligent highlighting after data is loaded
                if (this.currentClaim) {
                    this.entityHighlighter.currentClaim = this.currentClaim;
                    this.entityHighlighter.initializeIntelligentHighlighting();
                }
            } else {
                console.log('⚠️ Supabase client not available, using sample data');
                throw new Error('Supabase client not available. Please check configuration.');
            }

        } catch (error) {
            console.error('❌ Failed to load claim data:', error);
            console.error('❌ Error type:', error.constructor.name);
            console.error('❌ Error message:', error.message);
            console.error('❌ Error stack:', error.stack);
            console.log('🔄 Falling back to sample data...');

            // Fallback to sample data for testing
            await this.loadSampleData();

            this.showNotification(`Using sample data due to error: ${error.message}`, 'warning');
        } finally {
            this.hideLoading();

            // Always ensure content is displayed
            console.log('🔧 Ensuring content is displayed...');

            // Force content display immediately
            this.forceContentDisplay();

            // Also set up a delayed fallback in case the first attempt doesn't work
            setTimeout(() => {
                console.log('🔧 Secondary content display check...');
                this.ensureContentVisibility();
            }, 500);

            // Final emergency fallback
            setTimeout(() => {
                console.log('🚨 Final emergency content check...');
                const emailContent = document.getElementById('emailContent');
                const documentsContent = document.getElementById('documentsContent');

                if (!emailContent?.innerHTML?.trim() || emailContent.innerHTML.includes('Loading')) {
                    console.log('🚨 Email content still empty, forcing display...');
                    this.forceContentDisplay();
                }

                if (!documentsContent?.innerHTML?.trim() || documentsContent.innerHTML.includes('Loading')) {
                    console.log('🚨 Documents content still empty, forcing display...');
                    this.forceContentDisplay();
                }
            }, 1500);
        }
    }

    async loadClaimFromSupabase(claimReference) {
        console.log('🔄 Fetching claim data from Supabase...');
        
        try {
            // Fetch main claim data using the same pattern as explainable-claims-dashboard.js
            let query;
            
            // Try different methods to access the table
            if (typeof this.supabaseClient.table === 'function') {
                console.log('Using table() method for claims');
                query = this.supabaseClient.table('claims');
            } else if (typeof this.supabaseClient.from === 'function') {
                console.log('Using from() method for claims');
                query = this.supabaseClient.from('claims');
            } else {
                throw new Error('No valid table access method found');
            }
            
            const { data: claimData, error: claimError } = await query
                .select('*, 01_level_analysis, 02_level_analysis, 03_level_analysis, 04_level_analysis')
                .eq('claim_reference', claimReference)
                .single();

            console.log('🔍 Raw Supabase response:');
            console.log('- Data:', claimData);
            console.log('- Error:', claimError);

            if (claimError) {
                console.error('❌ Failed to fetch claim data:', claimError);
                console.error('❌ Error details:', JSON.stringify(claimError, null, 2));
                throw claimError;
            }

            if (!claimData) {
                console.error('❌ No claim data found for reference:', claimReference);
                throw new Error(`Claim ${claimReference} not found`);
            }

            console.log('✅ Claim data retrieved successfully:');
            console.log('- Claim reference:', claimData.claim_reference);
            console.log('- Email subject:', claimData.email_subject);
            console.log('- Has email body:', !!claimData.email_body);

            this.currentClaim = this.transformClaimData(claimData);

            // Fetch attachments
            await this.loadAttachments(claimReference);

            // Update UI
            this.populateUI();

            console.log('✅ Claim data loaded successfully');

        } catch (error) {
            console.error('❌ Error loading claim from Supabase:', error);
            throw error;
        }
    }

    transformClaimData(rawData) {
        // Transform Supabase data to match the expected format
        const transformedData = {
            id: rawData.id,
            claim_reference: rawData.claim_reference,
            status: rawData.status,
            workflow_status: rawData.workflow_status,
            sender_email: rawData.sender_email,
            sender_name: rawData.sender_name,
            email_subject: rawData.email_subject,
            email_body: rawData.email_body,
            level01_analysis: this.parseAnalysisData(rawData['01_level_analysis']),
            level02_analysis: this.parseAnalysisData(rawData['02_level_analysis']),
            level03_analysis: this.parseAnalysisData(rawData['03_level_analysis']),
            level04_analysis: this.parseAnalysisData(rawData['04_level_analysis']),
            documents: [], // Will be populated by loadAttachments
            
            // Additional claim metadata
            claim_type: rawData.claim_type,
            urgency_level: rawData.urgency_level,
            confidence_level: rawData.confidence_level,
            priority_score: rawData.priority_score,
            incident_date: rawData.incident_date,
            incident_location: rawData.incident_location,
            claimant_name: rawData.claimant_name,
            policy_number: rawData.policy_number,
            estimated_value: rawData.estimated_value,
            attachments_count: rawData.attachments_count,
            has_documents: rawData.has_documents
        };

        console.log('📋 Transformed claim data:', transformedData);
        return transformedData;
    }

    parseAnalysisData(data) {
        if (!data) return null;
        
        try {
            return typeof data === 'string' ? JSON.parse(data) : data;
        } catch (error) {
            console.warn('Failed to parse analysis data:', error);
            return data;
        }
    }

    async loadAttachments(claimReference) {
        try {
            // Fetch attachments using the same pattern as explainable-claims-dashboard.js
            let query;
            
            // Try different methods to access the table
            if (typeof this.supabaseClient.table === 'function') {
                console.log('Using table() method for attachments');
                query = this.supabaseClient.table('attachments');
            } else if (typeof this.supabaseClient.from === 'function') {
                console.log('Using from() method for attachments');
                query = this.supabaseClient.from('attachments');
            } else {
                throw new Error('No valid table access method found');
            }
            
            const { data: attachments, error } = await query
                .select('*')
                .eq('claim_reference', claimReference);

            if (error) throw error;

            // Transform attachments and extract OCR content
            this.currentClaim.documents = [];
            
            if (attachments && attachments.length > 0) {
                for (const attachment of attachments) {
                    // Create document from attachment
                    const document = {
                        id: attachment.id,
                        name: attachment.original_filename || attachment.filename || `Document ${this.currentClaim.documents.length + 1}`,
                        type: attachment.document_type || this.getDocumentType(attachment.filename || ''),
                        content: '',
                        ocrMetadata: attachment,
                        confidence: attachment.ocr_confidence || 0.95,
                        fileSize: attachment.file_size,
                        contentType: attachment.content_type,
                        processingStatus: attachment.processing_status
                    };

                    // Handle OCR text content - parse the exact Zurich OCR structure
                    if (attachment.ocr_text) {
                        try {
                            const ocrData = JSON.parse(attachment.ocr_text);
                            console.log('📄 Parsing OCR data for:', attachment.filename, ocrData);
                            
                            if (ocrData.zurich_ocr_response?.results && Array.isArray(ocrData.zurich_ocr_response.results)) {
                                // Handle Zurich structured OCR response with multiple document results
                                for (let i = 0; i < ocrData.zurich_ocr_response.results.length; i++) {
                                    const result = ocrData.zurich_ocr_response.results[i];
                                    if (result.extracted_text && result.extracted_text.trim()) {
                                        // Clean filename from URL encoding
                                        const cleanFilename = result.filename ? 
                                            decodeURIComponent(result.filename.replace(/\+/g, ' ')) : 
                                            `${document.name} - Part ${i + 1}`;
                                        
                                        const structuredDocument = {
                                            id: `${attachment.id}-${i}`,
                                            name: cleanFilename,
                                            type: this.getDocumentTypeFromFilename(cleanFilename) || document.type,
                                            content: result.extracted_text.trim(),
                                            ocrMetadata: {
                                                ...attachment,
                                                ocrResult: result,
                                                processingTime: ocrData.zurich_ocr_response.processing_time_ms
                                            },
                                            confidence: result.confidence || 0.95,
                                            fileSize: attachment.file_size,
                                            contentType: attachment.content_type,
                                            processingStatus: attachment.processing_status,
                                            sourceAttachmentId: attachment.id
                                        };
                                        
                                        this.currentClaim.documents.push(structuredDocument);
                                        console.log(`📄 Added document: ${cleanFilename} (${result.extracted_text.length} chars)`);
                                    }
                                }
                            } else if (ocrData.extracted_text) {
                                // Handle simple OCR response
                                document.content = ocrData.extracted_text.trim();
                                document.ocrMetadata.ocrResult = ocrData;
                                this.currentClaim.documents.push(document);
                                console.log(`📄 Added simple document: ${document.name}`);
                            } else {
                                // Handle other JSON structure - convert to readable format
                                document.content = `[Complex OCR Data]\n${JSON.stringify(ocrData, null, 2)}`;
                                this.currentClaim.documents.push(document);
                                console.log(`📄 Added JSON document: ${document.name}`);
                            }
                        } catch (e) {
                            console.warn('Failed to parse OCR JSON for', attachment.filename, e);
                            // OCR text is plain text, not JSON
                            document.content = attachment.ocr_text.trim();
                            this.currentClaim.documents.push(document);
                            console.log(`📄 Added plain text document: ${document.name}`);
                        }
                    } else {
                        // No OCR text available
                        document.content = `[No OCR text available for ${document.name}]\nProcessing Status: ${attachment.processing_status || 'Unknown'}`;
                        this.currentClaim.documents.push(document);
                        console.log(`📄 Added placeholder document: ${document.name}`);
                    }
                }
            }

            console.log(`✅ Loaded ${this.currentClaim.documents.length} documents from ${attachments.length} attachments`);

        } catch (error) {
            console.error('❌ Error loading attachments:', error);
            this.currentClaim.documents = [];
        }
    }

    getDocumentType(filename) {
        const extension = filename.split('.').pop().toLowerCase();
        const typeMap = {
            'pdf': 'certificate',
            'doc': 'medical_auth',
            'docx': 'medical_auth',
            'txt': 'text_document',
            'jpg': 'image',
            'jpeg': 'image',
            'png': 'image'
        };
        return typeMap[extension] || 'document';
    }

    getDocumentTypeFromFilename(filename) {
        const cleanName = filename.toLowerCase();
        
        // Map specific document types based on filename patterns
        if (cleanName.includes('certificate') && cleanName.includes('insurance')) {
            return 'certificate';
        } else if (cleanName.includes('medical') && (cleanName.includes('authorization') || cleanName.includes('auth'))) {
            return 'medical_auth';
        } else if (cleanName.includes('medical') && (cleanName.includes('report') || cleanName.includes('record'))) {
            return 'medical_report';
        } else if (cleanName.includes('fnol') || cleanName.includes('first notice')) {
            return 'first_notice';
        } else if (cleanName.includes('police') && cleanName.includes('report')) {
            return 'police_report';
        } else if (cleanName.includes('invoice') || cleanName.includes('bill')) {
            return 'invoice';
        } else if (cleanName.includes('estimate')) {
            return 'estimate';
        } else if (cleanName.includes('photo') || cleanName.includes('image')) {
            return 'photo';
        } else if (cleanName.includes('identity') || cleanName.includes('id')) {
            return 'identity';
        } else if (cleanName.includes('policy')) {
            return 'policy';
        }
        
        return this.getDocumentType(filename);
    }

    setupRealTimeSubscriptions() {
        if (!this.supabaseClient) return;

        console.log('🔄 Setting up real-time subscriptions...');

        try {
            // Subscribe to claim updates
            this.realTimeSubscription = this.supabaseClient
                .channel('claim-updates')
                .on('postgres_changes', {
                    event: 'UPDATE',
                    schema: 'public',
                    table: 'claims',
                    filter: `claim_reference=eq.${this.currentClaim.claim_reference}`
                }, (payload) => {
                    console.log('🔄 Real-time claim update received:', payload);
                    this.handleClaimUpdate(payload.new);
                })
                .on('postgres_changes', {
                    event: 'INSERT',
                    schema: 'public',
                    table: 'attachments',
                    filter: `claim_reference=eq.${this.currentClaim.claim_reference}`
                }, (payload) => {
                    console.log('📎 New attachment received:', payload);
                    this.handleNewAttachment(payload.new);
                })
                .on('postgres_changes', {
                    event: 'UPDATE',
                    schema: 'public',
                    table: 'attachments',
                    filter: `claim_reference=eq.${this.currentClaim.claim_reference}`
                }, (payload) => {
                    console.log('📎 Attachment updated:', payload);
                    this.handleNewAttachment(payload.new);
                })
                .subscribe((status) => {
                    console.log('📡 Subscription status:', status);
                });

        } catch (error) {
            console.error('❌ Failed to setup real-time subscriptions:', error);
        }
    }

    handleClaimUpdate(updatedData) {
        console.log('🔄 Processing real-time claim update...');
        
        // Update current claim data
        const previousClaim = { ...this.currentClaim };
        this.currentClaim = this.transformClaimData(updatedData);
        
        // Determine what changed and update UI accordingly
        this.processAnalysisUpdates(previousClaim);
        
        // Show notification
        this.showNotification('Claim data updated', 'info');
        
        // Update UI
        this.populateUI();
    }

    processAnalysisUpdates(previousClaim) {
        const levels = ['level01_analysis', 'level02_analysis', 'level03_analysis', 'level04_analysis'];
        
        levels.forEach(level => {
            const previous = previousClaim[level];
            const current = this.currentClaim[level];
            
            if (!previous && current) {
                // New analysis completed - progress indicators removed
                console.log(`✅ Analysis completed for ${level}`);
            } else if (previous && current && JSON.stringify(previous) !== JSON.stringify(current)) {
                // Analysis updated
                console.log(`🔄 Analysis updated for ${level}`);
            }
        });
    }







    handleNewAttachment(attachmentData) {
        console.log('📎 Processing new attachment:', attachmentData);
        
        // Create document from attachment data matching the loadAttachments logic
        const newDocument = {
            id: attachmentData.id,
            name: attachmentData.original_filename || attachmentData.filename || `New Document`,
            type: attachmentData.document_type || this.getDocumentType(attachmentData.filename || ''),
            content: '',
            ocrMetadata: attachmentData,
            confidence: attachmentData.ocr_confidence || 0.95,
            fileSize: attachmentData.file_size,
            contentType: attachmentData.content_type,
            processingStatus: attachmentData.processing_status
        };

        // Handle OCR text content (same logic as loadAttachments)
        if (attachmentData.ocr_text) {
            try {
                const ocrData = JSON.parse(attachmentData.ocr_text);
                
                if (ocrData.zurich_ocr_response?.results) {
                    // Handle structured OCR response - just use the first result for real-time updates
                    const firstResult = ocrData.zurich_ocr_response.results[0];
                    if (firstResult?.extracted_text) {
                        newDocument.content = firstResult.extracted_text.trim();
                        newDocument.confidence = firstResult.confidence || newDocument.confidence;
                    }
                } else if (typeof ocrData === 'object' && ocrData.extracted_text) {
                    newDocument.content = ocrData.extracted_text.trim();
                } else {
                    newDocument.content = JSON.stringify(ocrData, null, 2);
                }
            } catch (e) {
                // Plain text OCR
                newDocument.content = attachmentData.ocr_text.trim();
            }
        } else {
            newDocument.content = `[Processing OCR for ${newDocument.name}...]`;
        }
        
        // Add to current claim documents
        this.currentClaim.documents.push(newDocument);
        
        // Update documents display
        this.updateDocuments();
        
        // Show notification with processing status
        const statusText = attachmentData.processing_status === 'completed' ? 'processed' : 'uploaded';
        this.showNotification(`New document ${statusText}: ${newDocument.name}`, 'info');
    }

    async loadSampleData() {
        console.log('📋 Loading sample data for demonstration...');
        
        // Simulate loading delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Use the existing sample data structure
        const sampleData = {
            "sampleClaim": {
                "id": "ec20bd7b-0f42-488c-84df-517f90d7cd27",
                "claim_reference": "CLM-85228383",
                "status": "under_review",
                "workflow_status": "email_processing",
                "sender_email": "<EMAIL>",
                "sender_name": "Dinesh Krishna",
                "email_subject": "Claim Submission 104",
                "email_body": "Dear Zurich Claims Team,\n\nI am writing to formally submit the documents related to the injury I sustained on *August 27, 2020*, due to a slip-and-fall incident at the [No Frills location] while I was approaching the checkout area.\n\nAs discussed in earlier communications, I slipped on what appeared to be water on the floor, resulting in injuries that required medical attention. I am seeking support for the associated medical costs and any applicable compensation under the store's liability insurance.\n\nPlease find the following documents attached for your review:\n\n- A scanned copy of the *incident report*\n- My *medical records and treatment summary*\n- A signed *medical authorization form*\n- A copy of my *government-issued ID*\n\nI kindly request that you review my claim and let me know if any further information or documentation is needed. I would appreciate an update regarding the coverage assessment and claim status at your earliest convenience.\n\nThank you for your time and assistance.\n\nSincerely,\n*Jacques E.*",
                "level01_analysis": {
                    "exitPath": "PROCEED_TO_LEVEL02",
                    "claimId": "CLM-85228383",
                    "confidenceScore": 0.9,
                    "claimDetails": {
                        "incidentDate": "2020-08-27",
                        "incidentLocation": "No Frills, approaching the checkout area",
                        "claimantName": "Jacques E.",
                        "insuredParty": "Franco's No Frills - NF3666",
                        "claimType": "LIABILITY",
                        "damageDescription": "Slip-and-fall resulting in injuries"
                    },
                    "policyDetails": {
                        "policyNumber": "sdnrmv/PS4846",
                        "policyHolder": "Franco's No Frills - NF3666",
                        "effectiveDate": "2020-07-01",
                        "expiryDate": "2021-07-01",
                        "coverageTypes": ["Bodily Injury and Property Damage"],
                        "policyLimits": "5000000 per Occurrence",
                        "deductibleAmount": "5000"
                    },
                    "uiMetadata": {
                        "entityMappings": [
                            {
                                "fieldName": "incidentDate",
                                "extractedValue": "2020-08-27",
                                "originalText": "August 27, 2020",
                                "confidence": 0.95,
                                "highlightColor": "#e3f2fd",
                                "entityType": "DATE",
                                "documentLocation": {
                                    "page": 1,
                                    "startOffset": 95,
                                    "endOffset": 111
                                }
                            },
                            {
                                "fieldName": "claimantName",
                                "extractedValue": "Jacques E.",
                                "originalText": "Jacques E.",
                                "confidence": 0.92,
                                "highlightColor": "#f3e5f5",
                                "entityType": "PERSON",
                                "documentLocation": {
                                    "page": 1,
                                    "startOffset": 847,
                                    "endOffset": 857
                                }
                            },
                            {
                                "fieldName": "incidentLocation",
                                "extractedValue": "No Frills location",
                                "originalText": "No Frills location",
                                "confidence": 0.88,
                                "highlightColor": "#fff3e0",
                                "entityType": "LOCATION",
                                "documentLocation": {
                                    "page": 1,
                                    "startOffset": 153,
                                    "endOffset": 171
                                }
                            }
                        ]
                    }
                },
                "level02_analysis": {
                    "coverage_decision": "COVERED",
                    "confidence_score": 0.7,
                    "coverage_justification": {
                        "primaryReason": "POLICY_TERMS_CLEAR",
                        "detailedReasoning": "The policy clearly covers bodily injury due to negligence, and the incident occurred within the policy period.",
                        "policyBasis": "SECTION II – COMMERCIAL GENERAL LIABILITY"
                    }
                },
                "level03_analysis": {
                    "fault_type": "general_negligence",
                    "fault_parties": [
                        {
                            "party_type": "defendant",
                            "fault_percentage": 50,
                            "fault_factors": ["Requires investigation"]
                        },
                        {
                            "party_type": "plaintiff", 
                            "fault_percentage": 50,
                            "fault_factors": ["Requires investigation"]
                        }
                    ],
                    "determination_confidence": 0.9
                },
                "level04_analysis": {
                    "settlement_recommendation": 17550,
                    "confidence": 0.85,
                    "quantum_breakdown": {
                        "total_special_damages": 22650,
                        "total_general_damages": 18000,
                        "fault_reduction_percentage": 50,
                        "net_recoverable": 20325
                    }
                },
                "documents": [
                    {
                        "id": "doc-1",
                        "name": "Certificate of Insurance (1).pdf.txt",
                        "type": "certificate",
                        "content": "Commercial Policy\nDeclarations\nPolicy Number: sdnrmv/ PS4846\nBroker Code\nCSX REED STENHOUSE INC.\n-------Table information-------\nPolicy Period;None\nDay/Month/Year;Day/Month/Year\nFrom 1-July-2020;To 1-July-2021\n-------Table information-------\nCoverage;Limit of Liability;Deductible;Deductible Amount\nGeneral Liability;$2,000,000;$0;$0\nProperty Damage;$1,000,000;$500;$500",
                        "confidence": 0.98,
                        "processingStatus": "Processed",
                        "fileSize": 2048,
                        "contentType": "text/plain"
                    },
                    {
                        "id": "doc-2", 
                        "name": "Medical Authorization Form+.pdf.txt",
                        "type": "medical_auth",
                        "content": "MEDICAL AUTHORIZATION FORM\n\nI hereby authorize the release of medical information related to my treatment for injuries sustained on August 27, 2020.\n\nPatient Name: Jacques E.\nDate of Birth: 1980-01-01\n\n-------Table information-------\nTreatment Date;Procedure;Provider;Cost\n2020-08-27;Emergency Room Visit;City Hospital;$2,500\n2020-08-28;X-Ray Examination;City Hospital;$800\n2020-09-15;Follow-up Consultation;Dr. Smith;$300\n-------Table information-------\n\nThis authorization is valid for 12 months from the date of signing.",
                        "confidence": 0.95,
                        "processingStatus": "Processed",
                        "fileSize": 1536,
                        "contentType": "text/plain"
                    }
                ]
            }
        };

        this.currentClaim = sampleData.sampleClaim;

        console.log('📋 Sample claim data set:', this.currentClaim.claim_reference);
        console.log('📧 Sample email data:', !!this.currentClaim.email_body);
        console.log('📄 Sample documents:', this.currentClaim.documents?.length || 0);

        this.populateUI();

        console.log('✅ Sample data loaded successfully');
    }

    setupEventListeners() {
        // Tab switching
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.switchTab(e.target.dataset.doc));
        });

        // Expand/collapse buttons
        document.querySelectorAll('.expand-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.toggleSection(e.target.dataset.target));
        });

        // Collapse buttons in details panel
        document.querySelectorAll('.collapse-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.toggleDetails(e.target.dataset.target));
        });

        // Level selector
        const levelSelect = document.getElementById('levelSelect');
        if (levelSelect) {
            levelSelect.addEventListener('change', (e) => this.filterByLevel(e.target.value));
        }

        // Action buttons
        document.getElementById('approveBtn')?.addEventListener('click', () => this.handleAction('approve'));
        document.getElementById('escalateBtn')?.addEventListener('click', () => this.handleAction('escalate'));
        document.getElementById('requestInfoBtn')?.addEventListener('click', () => this.handleAction('request_info'));
        document.getElementById('zendeskBtn')?.addEventListener('click', () => this.openZendeskDashboard());

        // Refresh button
        document.getElementById('refreshBtn')?.addEventListener('click', () => this.refreshData());

        // Search functionality
        document.getElementById('searchBtn')?.addEventListener('click', () => this.showSearch());
        
        // Setup real-time notification handlers
        this.setupNotificationHandlers();
    }

    setupNotificationHandlers() {
        // Listen for keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 'f':
                        e.preventDefault();
                        this.focusSearch();
                        break;
                    case 'r':
                        e.preventDefault();
                        this.refreshData();
                        break;
                }
            }
        });

        // Setup visibility change handler for real-time updates
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && this.supabaseClient) {
                // Refresh data when tab becomes visible
                this.refreshAnalysisData();
            }
        });
    }

    async refreshAnalysisData() {
        if (!this.currentClaim || !this.supabaseClient) return;

        try {
            console.log('🔄 Refreshing analysis data...');
            await this.loadClaimFromSupabase(this.currentClaim.claim_reference);
        } catch (error) {
            console.error('❌ Failed to refresh analysis data:', error);
        }
    }

    focusSearch() {
        const searchBtn = document.getElementById('searchBtn');
        if (searchBtn) {
            searchBtn.click();
        }
    }

    populateUI() {
        if (!this.currentClaim) return;

        console.log('🔄 Populating UI with claim data:', this.currentClaim.claim_reference);
        console.log('📄 Current claim documents:', this.currentClaim.documents);

        // Header information
        this.updateElement('claimReference', this.currentClaim.claim_reference);
        this.updateClaimStatus(this.currentClaim.status);
        this.updateOverallConfidence();

        // Render email and documents content
        this.renderStructuredDocuments(this.currentClaim);

        // Decision cards
        this.updateDecisionCards();

        // Analysis details
        this.updateAnalysisDetails();

        // Add enhanced highlighting with intelligent entity mapping
        this.initializeEnhancedHighlighting();
        
        // Progress indicators removed per user request
        
        // Add navigation icons to analysis fields (only once)
        if (!this.navigationIconsAdded) {
            setTimeout(() => {
                this.addSingleNavigationIcons();
                this.navigationIconsAdded = true;
            }, 100);
        }

        // Set default tab - always show Email tab first for now
        console.log('📊 Checking default tab selection...');
        console.log('📄 Documents available:', this.currentClaim.documents?.length || 0);
        console.log('📧 Email available:', !!(this.currentClaim.email_subject || this.currentClaim.email_body));

        // Always start with email tab to ensure something is visible
        console.log('📧 Switching to Email tab');
        this.switchTab('email');

        // Ensure content is visible - add fallback
        setTimeout(() => {
            this.ensureContentVisibility();
        }, 500);

        // Emergency fallback - force content immediately
        setTimeout(() => {
            this.forceContentDisplay();
        }, 1000);
    }

    ensureContentVisibility() {
        console.log('🔍 Checking content visibility...');

        const emailContent = document.getElementById('emailContent');
        const documentsContent = document.getElementById('documentsContent');

        console.log('📧 Email container:', !!emailContent);
        console.log('📄 Documents container:', !!documentsContent);

        if (emailContent) {
            const hasContent = emailContent.innerHTML.trim() &&
                              !emailContent.innerHTML.includes('Loading email content');
            console.log('📧 Email has content:', hasContent);

            if (!hasContent && this.currentClaim) {
                console.log('🔧 Adding fallback email content...');
                const emailSubject = this.currentClaim.email_subject || 'Email Subject';
                const emailBody = this.currentClaim.email_body || 'Email content will appear here.';
                const senderInfo = this.currentClaim.sender_name || this.currentClaim.sender_email || 'Unknown Sender';

                emailContent.innerHTML = `
                    <div class="document-section email-section">
                        <div class="document-header">
                            <div class="document-meta">
                                <span class="document-type">Email</span>
                                <div class="email-meta">
                                    <span>From: ${senderInfo}</span>
                                    <span>Subject: ${emailSubject}</span>
                                </div>
                            </div>
                        </div>
                        <div class="document-content">
                            <div class="email-subject">
                                <h3>${emailSubject}</h3>
                            </div>
                            <div class="email-body ocr-content" data-file-name="email">
                                ${this.escapeHtml(emailBody)}
                            </div>
                        </div>
                    </div>
                `;
                console.log('✅ Fallback email content added');
            }

            // Ensure email tab is active
            emailContent.classList.add('active');
        }

        if (documentsContent) {
            const hasContent = documentsContent.innerHTML.trim() &&
                              !documentsContent.innerHTML.includes('Loading documents');
            console.log('📄 Documents has content:', hasContent);

            if (!hasContent) {
                console.log('🔧 Adding fallback documents content...');
                documentsContent.innerHTML = `
                    <div class="no-documents">
                        <p>No documents available for this claim.</p>
                        <p>Documents will appear here when uploaded and processed.</p>
                    </div>
                `;
                console.log('✅ Fallback documents content added');
            }
        }
    }

    forceContentDisplay() {
        console.log('🚨 Creating unified scrollable document view');

        // Remove tab-based interface and create single scrollable view
        const documentViewer = document.querySelector('.document-panel .document-viewer');
        if (!documentViewer) {
            console.error('Document viewer not found');
            return;
        }

        // Clear existing content and create unified view
        // Build email content dynamically to avoid template literal issues
        const senderName = this.currentClaim?.sender_name || this.currentClaim?.sender_email || 'Jacques E.';
        const incidentDate = this.currentClaim?.incident_date || (this.currentClaim?.created_at ? this.currentClaim.created_at.split('T')[0] : 'August 27, 2020');
        const emailSubject = this.currentClaim?.email_subject || 'Claim Submission INJ';

        let emailBodyContent = '';
        if (this.currentClaim?.email_body) {
            emailBodyContent = this.currentClaim.email_body.split('\n').map(line =>
                line.trim() ? '<p>' + line.trim() + '</p>' : ''
            ).join('');
        } else {
            emailBodyContent = '<p>Dear Zurich Claims Team,</p>' +
                '<p>I am writing to formally submit the documents related to the injury I sustained on "August 27, 2020", due to a slip-and-fall incident at the [No Frills location] while I was approaching the checkout area.</p>' +
                '<p>As discussed in earlier communications, I slipped on what appeared to be water on the floor, resulting in injuries that required medical attention.</p>' +
                '<p>I am seeking support for the associated medical costs and any applicable compensation under the store\'s liability insurance.</p>' +
                '<p>Please find the following documents attached for your review:</p>';
        }

        documentViewer.innerHTML = `
            <div class="unified-document-view">
                <!-- Email Section -->
                <div class="document-section email-section">
                    <div class="section-header">
                        <h3 class="section-title">📧 Claim Submission Email</h3>
                    </div>
                    <div class="email-content-unified">
                        <div class="email-meta">
                            <div class="meta-row">
                                <span class="meta-label">From:</span>
                                <span class="meta-value">${senderName}</span>
                            </div>
                            <div class="meta-row">
                                <span class="meta-label">Date:</span>
                                <span class="meta-value">${incidentDate}</span>
                            </div>
                            <div class="meta-row">
                                <span class="meta-label">Subject:</span>
                                <span class="meta-value">${emailSubject}</span>
                            </div>
                        </div>
                        <div class="email-body">
                            ${emailBodyContent}
                        </div>
                    </div>
                </div>

                <!-- Documents Section -->
                <div class="document-section documents-section">
                    <div class="section-header">
                        <h3 class="section-title">📄 Supporting Documents</h3>
                    </div>
                    <div class="documents-list-unified" id="documentsListUnified">
                        <!-- Documents will be populated here -->
                    </div>
                </div>
            </div>
        `;

        console.log('✅ Unified document view created');
        this.populateDocumentsInUnifiedView();
    }

    populateDocumentsInUnifiedView() {
        const documentsContainer = document.getElementById('documentsListUnified');
        if (!documentsContainer) return;

        // Use real documents from current claim if available, otherwise fallback to sample data
        let documents = [];

        if (this.currentClaim && this.currentClaim.documents && this.currentClaim.documents.length > 0) {
            console.log('📄 Using real documents from Supabase:', this.currentClaim.documents.length);
            documents = this.currentClaim.documents.map(doc => ({
                name: doc.name || doc.file_name || 'Unknown Document',
                type: doc.type || doc.content_type || 'PDF Document',
                ocrText: doc.content || doc.ocr_text || 'No OCR text available for this document.'
            }));
        } else {
            console.log('📄 Using sample documents (no real data available)');
            documents = [
                {
                    name: 'Certificate of Insurance',
                    type: 'PDF Document',
                    ocrText: `CERTIFICATE OF GENERAL LIABILITY INSURANCE

Policy Number: GL-2020-NF-8846
Policy Holder: No Frills Supermarkets Ltd.
Coverage Period: January 1, 2020 - December 31, 2020
Coverage Limits: $5,000,000 per occurrence

This certificate confirms that the above-named insured has general liability insurance coverage in effect for the policy period shown. This coverage includes premises liability for slip and fall incidents occurring on the insured's property.

Certificate Holder: Zurich Insurance Company
Date Issued: August 15, 2020`
                },
            {
                name: 'Medical Authorization Form',
                type: 'PDF Document',
                ocrText: `MEDICAL AUTHORIZATION AND RELEASE FORM

Patient Name: Jacques E.
Date of Birth: [REDACTED]
Date of Incident: August 27, 2020

I, Jacques E., hereby authorize the release of my medical records related to the injuries sustained on August 27, 2020, at the No Frills location to Zurich Insurance Company for the purpose of processing my liability claim.

Medical Provider: Toronto General Hospital Emergency Department
Treatment Date: August 27, 2020
Diagnosis: Contusion to left hip, minor lacerations to left hand
Treatment: X-ray examination, wound cleaning and bandaging, pain medication prescribed

Patient Signature: [SIGNED]
Date: August 28, 2020`
            },
            {
                name: 'Incident Report',
                type: 'PDF Document',
                ocrText: `NO FRILLS INCIDENT REPORT

Store Location: 123 Main Street, Toronto, ON
Date of Incident: August 27, 2020
Time of Incident: 3:45 PM
Report Filed By: Sarah M., Store Manager

INCIDENT DESCRIPTION:
Customer Jacques E. slipped and fell near checkout area. Customer reported that there was water on the floor from a leaking freezer unit. Customer sustained injuries to hip and hand. Emergency services were called and customer was transported to hospital.

IMMEDIATE ACTIONS TAKEN:
- Area was immediately cordoned off
- Maintenance was notified to repair freezer leak
- Incident scene was photographed
- Customer information was collected
- Insurance company was notified

WITNESSES:
- Employee: Mike T. (Cashier)
- Customer: Jennifer L.

Report Number: NF-INC-**********
Manager Signature: [SIGNED]`
            },
            {
                name: 'Medical Bills',
                type: 'PDF Document',
                ocrText: `TORONTO GENERAL HOSPITAL
EMERGENCY DEPARTMENT BILLING

Patient: Jacques E.
Date of Service: August 27, 2020
Account Number: TGH-ER-***********

SERVICES PROVIDED:
- Emergency Department Visit (Level 3) - $450.00
- X-Ray Examination (Hip/Pelvis) - $125.00
- Wound Care and Bandaging - $75.00
- Pain Medication (Prescription) - $35.00

TOTAL CHARGES: $685.00

FOLLOW-UP CARE:
- Physiotherapy Assessment - $150.00
- Follow-up X-Ray (2 weeks) - $125.00
- Physiotherapy Sessions (6 sessions) - $900.00

TOTAL MEDICAL EXPENSES: $1,860.00

Payment Status: Pending Insurance Review
Billing Date: September 15, 2020`
            },
            {
                name: 'Witness Statement',
                type: 'PDF Document',
                ocrText: `WITNESS STATEMENT

Witness Name: Mike T.
Position: Cashier, No Frills
Date of Statement: August 28, 2020

STATEMENT:
I was working at checkout lane 3 on August 27, 2020, around 3:45 PM when I heard a customer fall near the entrance to the checkout area. I immediately looked over and saw a customer (later identified as Jacques E.) on the ground.

I noticed there was water on the floor near where the customer fell. This water appeared to be coming from the freezer unit that had been having issues that day. I had reported the leak to maintenance earlier in my shift, but it had not been repaired yet.

The customer appeared to be in pain and was holding his hip. His hand was also bleeding from where he had tried to break his fall. I immediately called for the manager and helped the customer to a chair.

The customer was conscious and alert but clearly injured. He mentioned that he had slipped on the water and hadn't seen it because it was clear against the floor.

I can confirm that the water was present on the floor at the time of the incident and that the freezer leak had been an ongoing issue that day.

Witness Signature: [SIGNED]
Date: August 28, 2020`
            }
        ];
        }

        documentsContainer.innerHTML = documents.map((doc, index) => `
            <div class="document-item-unified">
                <div class="document-header-unified">
                    <h4 class="document-title">${doc.name}</h4>
                    <span class="document-type-badge">${doc.type}</span>
                </div>
                <div class="document-content-unified">
                    <div class="ocr-text-content" data-document="${doc.name.toLowerCase().replace(/\s+/g, '-')}">
                        ${doc.ocrText.split('\n').map(line =>
                            line.trim() ? '<p class="ocr-line">' + line.trim() + '</p>' : '<br>'
                        ).join('')}
                    </div>
                </div>
            </div>
        `).join('');

        console.log('✅ Documents populated in unified view');

        // Re-initialize highlighting system now that documents are loaded
        if (this.entityHighlighter) {
            console.log('🔄 Re-initializing highlighting system for new documents...');
            setTimeout(() => {
                this.entityHighlighter.setupHighlightingSystem();
            }, 100);
        }
    }

    updateElement(id, value, fallback = 'N/A') {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value || fallback;
        }
    }

    updateClaimStatus(status) {
        const statusElement = document.getElementById('claimStatus');
        if (statusElement) {
            statusElement.textContent = this.formatStatus(status);
            statusElement.className = `status ${this.getStatusClass(status)}`;
        }
    }

    updateOverallConfidence() {
        const level01 = this.currentClaim.level01_analysis?.confidenceScore || 0;
        const level02 = this.currentClaim.level02_analysis?.confidence_score || 0;
        const level03 = this.currentClaim.level03_analysis?.determination_confidence || 0;
        const level04 = this.currentClaim.level04_analysis?.confidence || 0;

        const averageConfidence = (level01 + level02 + level03 + level04) / 4;
        const confidencePercent = Math.round(averageConfidence * 100);

        this.updateConfidenceMeter('confidenceProgress', 'confidenceScore', confidencePercent);
    }

    updateConfidenceMeter(progressId, textId, percent) {
        const progressElement = document.getElementById(progressId);
        const textElement = document.getElementById(textId);
        
        if (progressElement) {
            progressElement.style.width = `${percent}%`;
        }
        if (textElement) {
            textElement.textContent = `${percent}%`;
        }
    }

    updateEmailContent() {
        this.updateElement('emailSubject', this.currentClaim.email_subject);
        this.updateElement('emailSender', this.currentClaim.sender_name);
        this.updateElement('emailDate', new Date().toLocaleDateString());
        
        const emailBody = document.getElementById('emailBody');
        if (emailBody && this.currentClaim.email_body) {
            emailBody.textContent = this.currentClaim.email_body;
        }
    }

    updateDocuments() {
        const documentsContent = document.getElementById('documentsContent');
        if (documentsContent && this.currentClaim) {
            let html = '';

            // Email section first - display subject as header, then body
            const emailSubject = this.currentClaim.email_subject || 'No Subject';
            const emailBody = this.currentClaim.email_body || '';
            html += `
                <div class="document-content-wrapper email-section">
                    <h3 class="document-header-title">📧 ${this.escapeHtml(emailSubject)}</h3>
                    <div class="document-meta">
                        <span class="document-type">Email Content</span>
                        <span class="sender-info">From: ${this.escapeHtml(this.currentClaim.sender_name || this.currentClaim.sender_email || 'Unknown')}</span>
                    </div>
                    <div class="document-content ocr-content email-body-content" id="emailBodyBlock" data-file-name="email">
                        ${this.renderEmailContentWithNER(emailBody)}
                    </div>
                </div>
                <hr class="document-separator">
            `;

            // Each document - display filename as header, then OCR text
            if (this.currentClaim.documents && this.currentClaim.documents.length > 0) {
                this.currentClaim.documents.forEach((doc, index) => {
                    html += `
                        <div class="document-content-wrapper">
                            <h3 class="document-header-title">📄 ${this.escapeHtml(doc.name)}</h3>
                            <div class="document-meta">
                                <span class="document-type">${this.formatDocumentType(doc.type)}</span>
                                ${doc.confidence ? `<span class="confidence-badge">OCR Confidence: ${Math.round(doc.confidence * 100)}%</span>` : ''}
                                ${doc.processingStatus ? `<span class="processing-status">${doc.processingStatus}</span>` : ''}
                            </div>
                            <div class="document-content ocr-content" data-document-index="${index}" data-file-name="${doc.name}" id="docContent${index}">
                                ${this.renderDocumentContent(doc.content)}
                            </div>
                        </div>
                        <hr class="document-separator">
                    `;
                });
            }

            documentsContent.querySelector('.document-list').innerHTML = html;

            // Initialize enhanced highlighting after content is rendered
            setTimeout(() => {
                this.initializeEnhancedHighlighting();
            }, 100);
        }
    }

    // Robust document content rendering with advanced table detection
    renderDocumentContent(content) {
        if (!content || typeof content !== 'string') {
            return '<div class="ocr-line">[No content available]</div>';
        }

        console.log('📄 Rendering document with NER highlighting...');
        console.log('📄 Original content:', content.substring(0, 200) + '...');

        // Extract NER entities and apply highlighting BEFORE rendering
        const nerEntities = this.extractNEREntities();
        console.log('🏷️ Entities for document highlighting:', nerEntities);

        let highlightedContent = this.applyNERHighlightingToText(content, nerEntities);
        console.log('✨ Document highlighted content:', highlightedContent.substring(0, 200) + '...');

        // Split highlighted content into sections using table delimiters
        const sections = highlightedContent.split(/-+Table information-+/g);
        let html = '';
        let tableCount = 0;

        sections.forEach((section, sectionIndex) => {
            if (!section.trim()) return;

            const lines = section.split(/\r?\n/).filter(line => line.trim() !== '');
            
            // Simple table detection: if section contains "Table information" or has semicolons
            const hasTableDelimiter = section.includes('Table information');
            const hasSemicolons = lines.some(line => line.includes(';') && line.split(';').length > 2);
            
            if (hasTableDelimiter || hasSemicolons) {
                const tableHtml = this.renderTableFromLines(lines, ++tableCount);
                html += tableHtml;
            } else {
                // Render as narrative text with proper formatting
                const narrativeHtml = this.renderNarrativeText(lines);
                html += narrativeHtml;
            }

            // Add separator between sections (except for the last one)
            if (sectionIndex < sections.length - 1) {
                html += '<hr class="section-separator">';
            }
        });

        return html;
    }

    // Advanced table detection using multiple heuristics
    isTableSection(lines) {
        if (lines.length < 2) return false;

        let tableIndicators = 0;
        let totalLines = 0;

        lines.forEach(line => {
            if (line.trim() === '') return;
            totalLines++;

            // Check for semicolon-separated columns
            const semicolonColumns = line.split(';').length;
            if (semicolonColumns > 2) {
                tableIndicators++;
            }

            // Check for multiple spaces/tabs (indicating columns)
            const spaceColumns = line.split(/\s{3,}/).length;
            if (spaceColumns > 2) {
                tableIndicators++;
            }

            // Check for common table headers
            if (/^(Coverage|Limit|Deductible|Amount|Policy|Section|Date|Name|Type|Status|Value|Description|From|To|None)/i.test(line)) {
                tableIndicators++;
            }

            // Check for currency patterns
            if (/\$\d+[,.]?\d*/.test(line)) {
                tableIndicators++;
            }

            // Check for percentage patterns
            if (/\d+%/.test(line)) {
                tableIndicators++;
            }
        });

        // If more than 60% of lines show table characteristics, treat as table
        return (tableIndicators / totalLines) > 0.6;
    }

    // Render table from lines with robust parsing
    renderTableFromLines(lines, tableNum) {
        const tableRows = [];
        let hasHeader = false;

        lines.forEach((line, lineIndex) => {
            if (line.trim() === '') return;

            // Try semicolon separation first, then space separation
            let cells = line.split(';').map(cell => cell.trim());
            
            // If semicolon didn't work well, try space separation
            if (cells.length <= 1 || cells.every(cell => !cell || cell === 'None')) {
                cells = line.split(/\s{3,}/).map(cell => cell.trim());
            }

            // Filter out empty cells and "None" values
            cells = cells.filter(cell => cell && cell !== 'None' && cell !== '');
            
            if (cells.length > 1) {
                // Check if this looks like a header row
                const isHeader = lineIndex === 0 || 
                    cells.some(cell => /^(Coverage|Limit|Deductible|Amount|Policy|Section|Date|Name|Type|Status|Value|Description|From|To)$/i.test(cell)) ||
                    cells.every(cell => /^[A-Z\s]+$/.test(cell));

                if (isHeader && !hasHeader) {
                    hasHeader = true;
                    tableRows.push({ cells, isHeader: true });
                } else {
                    tableRows.push({ cells, isHeader: false });
                }
            }
        });

        if (tableRows.length === 0) {
            // Fallback: render as preformatted text
            return `<pre class="ocr-narrative">${this.escapeHtml(lines.join('\n'))}</pre>`;
        }

        // Build table HTML
        let tableHtml = `<div class="ocr-table-container">`;
        if (tableNum) {
            tableHtml += `<div class="ocr-table-header">Table ${tableNum}</div>`;
        }
        
        tableHtml += `<table class="ocr-table"><tbody>`;
        
        tableRows.forEach(row => {
            const rowClass = row.isHeader ? 'table-header-row' : '';
            tableHtml += `<tr class="${rowClass}">`;
            row.cells.forEach(cell => {
                const tag = row.isHeader ? 'th' : 'td';
                tableHtml += `<${tag}>${this.escapeHtml(cell)}</${tag}>`;
            });
            tableHtml += `</tr>`;
        });
        
        tableHtml += `</tbody></table></div>`;
        
        return tableHtml;
    }

    // Render narrative text with proper formatting
    renderNarrativeText(lines) {
        if (lines.length === 0) return '';

        let html = '<div class="ocr-narrative-container">';
        
        lines.forEach(line => {
            if (line.trim() === '') {
                html += '<div class="ocr-line-spacer"></div>';
            } else {
                // Check if line looks like a header
                const isHeader = /^(From:|To:|Subject:|Date:|Claim Number:|Policy Number:|Insured:|Claimant:)/i.test(line) ||
                                /^[A-Z][A-Z\s]+:$/.test(line) ||
                                line.length < 50 && /^[A-Z\s]+$/.test(line);

                if (isHeader) {
                    html += `<div class="ocr-line-header">${this.escapeHtml(line)}</div>`;
                } else {
                    html += `<div class="ocr-line">${this.escapeHtml(line)}</div>`;
                }
            }
        });
        
        html += '</div>';
        return html;
    }

    renderTable(rows, tableNum) {
        if (!rows.length) return '';
        // Add a table header for visual separation if multiple tables
        let tableHeader = '';
        if (tableNum) {
            tableHeader = `<div class="ocr-table-header">Table ${tableNum}</div>`;
        }
        return `${tableHeader}<table class="ocr-table"><tbody>${rows.map(row => `<tr>${row.map(cell => `<td>${this.escapeHtml(cell)}</td>`).join('')}</tr>`).join('')}</tbody></table><div class="ocr-table-divider"></div>`;
    }

    // --- Robust Entity Mapping & Highlighting ---
    navigateToDocumentHighlight(fieldName) {
        if (!fieldName || typeof fieldName !== 'string') {
            this.showNotification('Invalid field name for highlighting', 'error');
            return;
        }

        // Remove all previous active highlights
        document.querySelectorAll('.highlight').forEach(h => h.classList.remove('active', 'highlight-entity-active'));

        // Case-insensitive, whitespace-insensitive matching
        const norm = s => s.replace(/\s+/g, '').toLowerCase();
        const target = norm(fieldName);
        const escapedFieldName = this.escapeRegex(fieldName);

        // Highlight in all document content types
        let matchCount = 0;
        let documentMatchCount = 0;

        document.querySelectorAll('.document-content').forEach(docBlock => {
            let docHasMatch = false;

            // Highlight in narrative text lines
            docBlock.querySelectorAll('.ocr-line, .ocr-line-header').forEach(line => {
                const originalHtml = line.innerHTML.replace(/(<span[^>]*class="highlight[^"]*"[^>]*>)(.*?)(<\/span>)/gi, '$2');
                const regex = new RegExp(`(${escapedFieldName})`, 'gi');
                if (regex.test(originalHtml)) {
                    line.innerHTML = originalHtml.replace(regex, '<span class="highlight highlight-entity-active">$1</span>');
                    matchCount++;
                    docHasMatch = true;
                }
            });

            // Highlight in table cells (both td and th)
            docBlock.querySelectorAll('td, th').forEach(cell => {
                const originalHtml = cell.innerHTML.replace(/(<span[^>]*class="highlight[^"]*"[^>]*>)(.*?)(<\/span>)/gi, '$2');
                const cellText = cell.textContent || cell.innerText;
                
                // Check for exact match or contains match
                if (norm(cellText).includes(target) || new RegExp(escapedFieldName, 'gi').test(originalHtml)) {
                    const regex = new RegExp(escapedFieldName, 'gi');
                    cell.innerHTML = originalHtml.replace(regex, '<span class="highlight highlight-entity-active">$1</span>');
                    matchCount++;
                    docHasMatch = true;
                }
            });

            // Highlight in preformatted text
            docBlock.querySelectorAll('pre.ocr-narrative').forEach(pre => {
                const originalHtml = pre.innerHTML.replace(/(<span[^>]*class="highlight[^"]*"[^>]*>)(.*?)(<\/span>)/gi, '$2');
                const regex = new RegExp(`(${escapedFieldName})`, 'gi');
                if (regex.test(originalHtml)) {
                    pre.innerHTML = originalHtml.replace(regex, '<span class="highlight highlight-entity-active">$1</span>');
                    matchCount++;
                    docHasMatch = true;
                }
            });

            // Highlight in email content
            docBlock.querySelectorAll('#emailBodyBlock').forEach(emailBlock => {
                const originalHtml = emailBlock.innerHTML.replace(/(<span[^>]*class="highlight[^"]*"[^>]*>)(.*?)(<\/span>)/gi, '$2');
                const regex = new RegExp(`(${escapedFieldName})`, 'gi');
                if (regex.test(originalHtml)) {
                    emailBlock.innerHTML = originalHtml.replace(regex, '<span class="highlight highlight-entity-active">$1</span>');
                    matchCount++;
                    docHasMatch = true;
                }
            });

            if (docHasMatch) {
                documentMatchCount++;
            }
        });

        // Scroll to first match in each document that has matches
        document.querySelectorAll('.document-content').forEach(docBlock => {
            const first = docBlock.querySelector('.highlight-entity-active');
            if (first) {
                // Add a small delay to ensure highlighting is complete
                setTimeout(() => {
                    first.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }, 100);
            }
        });

        // Show comprehensive notification
        if (matchCount > 0) {
            this.showNotification(
                `Found and highlighted "${fieldName}" in ${matchCount} location(s) across ${documentMatchCount} document(s)`, 
                'success'
            );
        } else {
            this.showNotification(
                `No matches found for "${fieldName}" in any documents`, 
                'warning'
            );
        }
    }

    toggleDocumentContent(index) {
        const documentItem = document.querySelector(`[data-document-index="${index}"]`);
        if (documentItem) {
            const preview = documentItem.querySelector('.document-preview');
            const fullText = documentItem.querySelector('.document-full-text');
            const toggleBtn = documentItem.querySelector('.toggle-text');
            
            if (fullText.style.display === 'none') {
                // Show full content
                preview.style.display = 'none';
                fullText.style.display = 'block';
                toggleBtn.textContent = 'Hide Full Content';
            } else {
                // Show preview
                preview.style.display = 'block';
                fullText.style.display = 'none';
                toggleBtn.textContent = 'View Full Content';
            }
        }
    }

    updateDecisionCards() {
        this.updateCoverageCard();
        this.updateFaultCard();
        this.updateQuantumCard();
        this.updateRiskCard();
    }

    updateCoverageCard() {
        const level02 = this.currentClaim.level02_analysis;
        if (!level02) return;

        const decisionElement = document.getElementById('coverageDecision');
        if (decisionElement) {
            decisionElement.textContent = this.formatCoverageDecision(level02.coverage_decision);
            decisionElement.className = `decision-status ${level02.coverage_decision === 'COVERED' ? 'covered' : 'not-covered'}`;
        }

        const confidencePercent = Math.round((level02.confidence_score || 0) * 100);
        this.updateConfidenceMeter('coverageConfidence', 'coverageConfidenceText', confidencePercent);

        this.updateElement('coverageJustification', 
            level02.coverage_justification?.detailedReasoning || 'Coverage analysis in progress...');
    }

    updateFaultCard() {
        const level03 = this.currentClaim.level03_analysis;
        if (!level03) return;

        this.updateElement('faultType', this.formatFaultType(level03.fault_type));

        const faultBreakdown = document.getElementById('faultBreakdown');
        if (faultBreakdown && level03.fault_parties) {
            faultBreakdown.innerHTML = level03.fault_parties.map(party => `
                <div class="fault-party">
                    <span class="fault-party-name">${this.formatPartyType(party.party_type)}</span>
                    <span class="fault-percentage">${party.fault_percentage}%</span>
                </div>
            `).join('');
        }

        const confidencePercent = Math.round((level03.determination_confidence || 0) * 100);
        this.updateConfidenceMeter('faultConfidence', 'faultConfidenceText', confidencePercent);
    }

    updateQuantumCard() {
        const level04 = this.currentClaim.level04_analysis;
        if (!level04) return;

        this.updateElement('settlementAmount', this.formatCurrency(level04.settlement_recommendation));

        const quantumBreakdown = document.getElementById('quantumBreakdown');
        if (quantumBreakdown && level04.quantum_breakdown) {
            const breakdown = level04.quantum_breakdown;
            quantumBreakdown.innerHTML = `
                <div class="quantum-item">
                    <span>Special Damages:</span>
                    <span>${this.formatCurrency(breakdown.total_special_damages)}</span>
                </div>
                <div class="quantum-item">
                    <span>General Damages:</span>
                    <span>${this.formatCurrency(breakdown.total_general_damages)}</span>
                </div>
                <div class="quantum-item">
                    <span>Fault Reduction:</span>
                    <span>${breakdown.fault_reduction_percentage}%</span>
                </div>
                <div class="quantum-item total">
                    <span>Net Recoverable:</span>
                    <span>${this.formatCurrency(breakdown.net_recoverable)}</span>
                </div>
            `;
        }

        const confidencePercent = Math.round((level04.confidence || 0) * 100);
        this.updateConfidenceMeter('quantumConfidence', 'quantumConfidenceText', confidencePercent);
    }

    updateRiskCard() {
        const riskFactors = document.getElementById('riskFactors');
        if (riskFactors) {
            riskFactors.innerHTML = `
                <div class="risk-factor">
                    <div class="risk-factor-icon"></div>
                    <span>Policy coverage confirmed</span>
                </div>
                <div class="risk-factor">
                    <div class="risk-factor-icon"></div>
                    <span>Investigation required for fault determination</span>
                </div>
                <div class="risk-factor">
                    <div class="risk-factor-icon"></div>
                    <span>Medical records review needed</span>
                </div>
            `;
        }
    }

    updateAnalysisDetails() {
        this.updateLevel01Details();
        this.updateLevel02Details();
        this.updateLevel03Details();
        this.updateLevel04Details();
        this.setupAnalysisFieldNavigation(); // Call this after updating analysis details
    }

    updateLevel01Details() {
        const level01 = this.currentClaim.level01_analysis;
        if (!level01) return;

        const details = level01.claimDetails || {};
        const policy = level01.policyDetails || {};

        this.updateElement('incidentDate', this.formatDate(details.incidentDate));
        this.updateElement('incidentLocation', details.incidentLocation);
        this.updateElement('claimantName', details.claimantName);
        this.updateElement('policyNumber', policy.policyNumber);
        this.updateElement('policyHolder', policy.policyHolder);
        this.updateElement('policyLimits', policy.policyLimits);

        // Set up click handlers AFTER the content is populated
        console.log('📊 Level01 analysis populated, setting up click handlers...');
        setTimeout(() => {
            this.setupAnalysisPanelClickHandlers();
            this.setupDirectClickHandlers(); // Add direct handlers as backup
        }, 500);
    }

    updateLevel02Details() {
        const level02 = this.currentClaim.level02_analysis;
        if (!level02) return;

        this.updateElement('coverageDecisionDetail', this.formatCoverageDecision(level02.coverage_decision));
        this.updateElement('coverageJustificationDetail', level02.coverage_justification?.detailedReasoning);
    }

    updateLevel03Details() {
        const level03 = this.currentClaim.level03_analysis;
        if (!level03) return;

        this.updateElement('faultTypeDetail', this.formatFaultType(level03.fault_type));

        const faultPartiesDetail = document.getElementById('faultPartiesDetail');
        if (faultPartiesDetail && level03.fault_parties) {
            faultPartiesDetail.innerHTML = level03.fault_parties.map(party => `
                <div class="fault-party-detail">
                    <div class="fault-party-header">
                        <span class="fault-party-type">${this.formatPartyType(party.party_type)}</span>
                        <span class="fault-party-percentage">${party.fault_percentage}%</span>
                    </div>
                    <div class="fault-factors">
                        Factors: ${party.fault_factors?.join(', ') || 'Under investigation'}
                    </div>
                </div>
            `).join('');
        }
    }

    updateLevel04Details() {
        const level04 = this.currentClaim.level04_analysis;
        if (!level04) return;

        this.updateElement('settlementRecommendation', this.formatCurrency(level04.settlement_recommendation));

        const quantumDetails = document.getElementById('quantumDetails');
        if (quantumDetails && level04.quantum_breakdown) {
            const breakdown = level04.quantum_breakdown;
            quantumDetails.innerHTML = `
                <div class="quantum-detail-item">
                    <span class="quantum-detail-label">Special Damages:</span>
                    <span class="quantum-detail-value">${this.formatCurrency(breakdown.total_special_damages)}</span>
                </div>
                <div class="quantum-detail-item">
                    <span class="quantum-detail-label">General Damages:</span>
                    <span class="quantum-detail-value">${this.formatCurrency(breakdown.total_general_damages)}</span>
                </div>
                <div class="quantum-detail-item">
                    <span class="quantum-detail-label">Fault Reduction:</span>
                    <span class="quantum-detail-value">${breakdown.fault_reduction_percentage}%</span>
                </div>
                <div class="quantum-detail-item">
                    <span class="quantum-detail-label">Net Recoverable:</span>
                    <span class="quantum-detail-value">${this.formatCurrency(breakdown.net_recoverable)}</span>
                </div>
            `;
        }
    }

    initializeEnhancedHighlighting() {
        if (!this.currentClaim) return;

        console.log('🎯 Initializing enhanced highlighting system...');

        // Clear any existing highlights
        this.clearAllHighlights();

        // Apply NER-style highlighting with entity boxes
        this.applyNERHighlighting();

        // Add simple click-to-search handlers
        this.addClickToSearchHandlers();

        console.log('✅ Enhanced highlighting system initialized');
    }

    addClickToSearchHandlers() {
        console.log('🔍 Click-to-search handlers will be set up after analysis data loads...');
        // Click handlers are now set up in updateLevel01Details() after content is populated
    }

    setupAnalysisPanelClickHandlers() {
        console.log('🔧 Setting up analysis panel click handlers...');

        // Find all clickable elements in the analysis panel
        const analysisPanel = document.querySelector('.analysis-panel');
        if (!analysisPanel) {
            console.log('❌ Analysis panel not found');
            return;
        }

        console.log('✅ Analysis panel found:', analysisPanel);

        // Target the actual data values, not labels
        const valueSelectors = [
            '.value',           // Main value spans
            '.field-value',
            '.analysis-value',
            '.claim-detail-value',
            '.policy-detail-value',
            '#incidentDate',    // Specific IDs
            '#incidentLocation',
            '#claimant',
            '#policyNumber',
            '#policyHolder',
            '#coverageLimit'
        ];

        console.log('🎯 Looking for elements with selectors:', valueSelectors);

        const clickableElements = analysisPanel.querySelectorAll(valueSelectors.join(', '));

        console.log(`🎯 Found ${clickableElements.length} clickable value elements in analysis panel`);
        console.log('📋 All found elements:', Array.from(clickableElements).map(el => ({
            id: el.id,
            className: el.className,
            textContent: el.textContent?.trim(),
            tagName: el.tagName
        })));

        clickableElements.forEach((element, index) => {
            const text = element.textContent?.trim();

            console.log(`🔍 Processing element ${index + 1}:`, {
                id: element.id,
                className: element.className,
                textContent: text,
                tagName: element.tagName
            });

            // Skip if empty, loading, or not searchable
            if (!text || text === 'Loading...' || text === 'N/A' || text.length < 2) {
                console.log(`⏭️ Skipping element ${index + 1}: empty or loading`);
                return;
            }

            console.log(`✅ Making clickable: "${text}" (${element.id || element.className})`);

            element.style.cursor = 'pointer';
            element.style.textDecoration = 'underline';
            element.style.color = '#2196F3';
            element.title = `Click to search for "${text}" in documents`;

            element.addEventListener('click', (e) => {
                e.stopPropagation();

                // Get the CURRENT text content at click time, not the cached text
                const currentText = element.textContent?.trim();

                console.log(`🔍 CLICK EVENT TRIGGERED!`);
                console.log(`🔍 Element clicked:`, {
                    id: element.id,
                    className: element.className,
                    textContent: currentText,
                    tagName: element.tagName,
                    innerHTML: element.innerHTML
                });
                console.log(`🔍 Cached text: "${text}"`);
                console.log(`🔍 Current text: "${currentText}"`);

                // Use current text, not cached text
                const searchText = currentText || text;
                console.log(`🔍 Final search text: "${searchText}"`);

                // Skip if it's still loading or looks like an ID
                if (searchText === 'Loading...' || searchText === 'N/A' || searchText.includes('Date') || searchText.includes('Location')) {
                    console.log(`⚠️ Skipping search for: "${searchText}" (looks like label or loading)`);
                    return;
                }

                this.searchAndHighlightInDocuments(searchText);
            });
        });

        // Also scan for any other searchable text patterns
        setTimeout(() => {
            this.addDynamicClickHandlers(analysisPanel);
            this.addDebugButton();
        }, 2000); // Wait for content to load
    }

    

    setupDirectClickHandlers() {
        console.log('🎯 Setting up simplified direct click handlers...');

        try {
            // Simple direct mapping without cloning elements
            const directMappings = [
                { id: 'incidentDate', searchText: 'August 27, 2020' },
                { id: 'incidentLocation', searchText: 'No Frills' },
                { id: 'claimantName', searchText: 'Jacques E.' },
                { id: 'policyNumber', searchText: 'PS8846' },
                { id: 'policyHolder', searchText: 'Franco\'s No Frills' }
            ];

            directMappings.forEach(mapping => {
                const element = document.getElementById(mapping.id);
                if (element) {
                    console.log(`🎯 Adding simple handler for #${mapping.id}`);

                    // Style as clickable
                    element.style.cursor = 'pointer';
                    element.style.textDecoration = 'underline';
                    element.style.color = '#2196F3';
                    element.title = `Click to search for "${mapping.searchText}"`;

                    // Add simple click handler
                    element.onclick = (e) => {
                        e.stopPropagation();
                        console.log(`🎯 Simple click on #${mapping.id} - searching for: "${mapping.searchText}"`);
                        this.searchAndHighlightInDocuments(mapping.searchText);
                    };
                } else {
                    console.log(`❌ Element #${mapping.id} not found`);
                }
            });

        } catch (error) {
            console.error('❌ Error in setupDirectClickHandlers:', error);
        }
    }

    addPatternBasedHandlers() {
        console.log('🔍 Skipping pattern-based handlers to prevent crashes');
        // Simplified approach - only use direct handlers for now
    }

    addDynamicClickHandlers(analysisPanel) {
        // Find text that looks like searchable data but wasn't caught by selectors
        const allElements = analysisPanel.querySelectorAll('*');

        allElements.forEach(element => {
            // Skip if already has click handler or contains child elements
            if (element.onclick || element.querySelector('*')) return;

            const text = element.textContent?.trim();
            if (text && this.isSearchableText(text)) {
                console.log(`🔍 Adding dynamic handler for: "${text}"`);

                element.style.cursor = 'pointer';
                element.style.textDecoration = 'underline';
                element.style.color = '#2196F3';
                element.title = `Click to search for "${text}" in documents`;

                element.addEventListener('click', (e) => {
                    e.stopPropagation();
                    console.log(`🔍 Dynamic click on: "${text}"`);
                    this.searchAndHighlightInDocuments(text);
                });
            }
        });
    }

    isSearchableText(text) {
        // Check if text looks like searchable data
        if (!text || text.length < 3) return false;

        // Date patterns
        if (/\d{4}-\d{2}-\d{2}/.test(text)) return true;
        if (/\w+\s+\d{1,2},\s+\d{4}/.test(text)) return true;

        // Money patterns
        if (/\$[\d,]+/.test(text)) return true;

        // Names (capitalized words)
        if (/^[A-Z][a-z]+\s+[A-Z]\.?$/.test(text)) return true;

        // Policy numbers
        if (/[A-Z]{2,}-?\d+/.test(text)) return true;

        // Locations
        if (/No Frills|Hospital|Street|Avenue|Road/.test(text)) return true;

        return false;
    }

    searchAndHighlightInDocuments(searchText) {
        console.log(`🔍 ===== SEARCH STARTED =====`);
        console.log(`🔍 Searching for: "${searchText}" (length: ${searchText.length})`);
        console.log(`🔍 Search text type:`, typeof searchText);

        // Clear previous search highlights
        this.clearSearchHighlights();

        // Get all document containers
        const documentContainers = document.querySelectorAll('.document-content, .email-body-content, .ocr-content');

        console.log(`📄 Found ${documentContainers.length} document containers to search in`);

        documentContainers.forEach((container, index) => {
            console.log(`📄 Container ${index + 1}:`, {
                className: container.className,
                textLength: container.textContent?.length || 0,
                textPreview: container.textContent?.substring(0, 100) + '...'
            });
        });

        let foundMatch = false;
        let firstMatchElement = null;

        documentContainers.forEach((container, index) => {
            console.log(`🔍 Searching in container ${index + 1}...`);
            const matches = this.findFuzzyMatches(container, searchText);

            if (matches.length > 0) {
                console.log(`✅ Found ${matches.length} matches in container ${index + 1}`);

                // Only highlight the first match found
                const firstMatch = matches[0];
                console.log(`🎯 Highlighting first match:`, firstMatch);
                const highlightedElement = this.highlightMatch(container, firstMatch, searchText);

                // Store first match for scrolling
                if (!firstMatchElement && highlightedElement) {
                    firstMatchElement = highlightedElement;
                    console.log(`📍 First match element stored for scrolling`);
                }

                foundMatch = true;
                return; // Stop after first match found
            } else {
                console.log(`❌ No matches in container ${index + 1}`);
            }
        });

        console.log(`🏁 Search completed.`);

        if (foundMatch && firstMatchElement) {
            // Scroll to first match
            console.log(`📍 Scrolling to first match...`);
            this.scrollToElement(firstMatchElement);
            console.log(`✅ Found and highlighted "${searchText}"`);
        } else {
            // Just log, no UI popup
            console.log(`❌ No matches found for "${searchText}" in any documents`);
        }

        console.log(`🔍 ===== SEARCH ENDED =====`);
    }



    findFuzzyMatches(container, searchText) {
        const matches = [];
        const containerText = container.textContent || container.innerText;

        // Try different matching strategies

        // 1. Exact match (case insensitive)
        const exactMatches = this.findExactMatches(containerText, searchText);
        matches.push(...exactMatches);

        // 2. Fuzzy match with Levenshtein distance
        const fuzzyMatches = this.findLevenshteinMatches(containerText, searchText);
        matches.push(...fuzzyMatches);

        // 3. Date format variations
        if (this.isDateText(searchText)) {
            const dateMatches = this.findDateMatches(containerText, searchText);
            matches.push(...dateMatches);
        }

        // Remove duplicates
        return [...new Set(matches)];
    }

    findExactMatches(text, searchText) {
        const matches = [];
        const normalizedText = text.toLowerCase();
        const normalizedSearch = searchText.toLowerCase();

        let index = normalizedText.indexOf(normalizedSearch);
        while (index !== -1) {
            // Get the actual text with original casing
            const actualMatch = text.substring(index, index + searchText.length);
            matches.push({
                text: actualMatch,
                index: index,
                similarity: 1.0
            });
            index = normalizedText.indexOf(normalizedSearch, index + 1);
        }

        return matches;
    }

    findLevenshteinMatches(text, searchText, threshold = 0.7) {
        const matches = [];
        const words = text.split(/\s+/);
        const searchWords = searchText.split(/\s+/);

        // For single word search
        if (searchWords.length === 1) {
            words.forEach((word, index) => {
                const cleanWord = word.replace(/[^\w]/g, '');
                const similarity = this.calculateSimilarity(cleanWord.toLowerCase(), searchText.toLowerCase());

                if (similarity >= threshold) {
                    matches.push({
                        text: word,
                        index: text.indexOf(word),
                        similarity: similarity
                    });
                }
            });
        } else {
            // For multi-word search, look for phrase matches
            for (let i = 0; i <= words.length - searchWords.length; i++) {
                const phrase = words.slice(i, i + searchWords.length).join(' ');
                const similarity = this.calculateSimilarity(phrase.toLowerCase(), searchText.toLowerCase());

                if (similarity >= threshold) {
                    matches.push({
                        text: phrase,
                        index: text.indexOf(phrase),
                        similarity: similarity
                    });
                }
            }
        }

        return matches;
    }

    findDateMatches(text, searchText) {
        const matches = [];

        // Convert search text to different date formats
        const dateVariations = this.generateDateVariations(searchText);

        dateVariations.forEach(variation => {
            const exactMatches = this.findExactMatches(text, variation);
            matches.push(...exactMatches);
        });

        return matches;
    }

    generateDateVariations(dateText) {
        const variations = [dateText];

        // Try to parse and convert date formats
        const datePatterns = [
            /(\w+)\s+(\d{1,2}),\s+(\d{4})/,  // "August 27, 2020"
            /(\d{4})-(\d{2})-(\d{2})/,        // "2020-08-27"
            /(\d{1,2})\/(\d{1,2})\/(\d{4})/   // "08/27/2020"
        ];

        // August 27, 2020 -> 2020-08-27
        const monthMatch = dateText.match(/(\w+)\s+(\d{1,2}),\s+(\d{4})/);
        if (monthMatch) {
            const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
                              'July', 'August', 'September', 'October', 'November', 'December'];
            const monthIndex = monthNames.indexOf(monthMatch[1]);
            if (monthIndex !== -1) {
                const month = String(monthIndex + 1).padStart(2, '0');
                const day = String(monthMatch[2]).padStart(2, '0');
                variations.push(`${monthMatch[3]}-${month}-${day}`);
            }
        }

        // 2020-08-27 -> August 27, 2020
        const isoMatch = dateText.match(/(\d{4})-(\d{2})-(\d{2})/);
        if (isoMatch) {
            const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
                              'July', 'August', 'September', 'October', 'November', 'December'];
            const monthName = monthNames[parseInt(isoMatch[2]) - 1];
            const day = parseInt(isoMatch[3]);
            variations.push(`${monthName} ${day}, ${isoMatch[1]}`);
        }

        return variations;
    }

    isDateText(text) {
        const datePatterns = [
            /\d{4}-\d{2}-\d{2}/,              // 2020-08-27
            /\w+\s+\d{1,2},\s+\d{4}/,        // August 27, 2020
            /\d{1,2}\/\d{1,2}\/\d{4}/        // 08/27/2020
        ];

        return datePatterns.some(pattern => pattern.test(text));
    }

    clearSearchHighlights() {
        // Remove previous search highlights
        document.querySelectorAll('.search-highlight').forEach(el => {
            const parent = el.parentNode;
            parent.replaceChild(document.createTextNode(el.textContent), el);
            parent.normalize();
        });
    }

    highlightMatch(container, match, originalSearchText) {
        try {
            const text = match.text;
            const innerHTML = container.innerHTML;

            // Simple yellow highlighting
            const highlightClass = 'search-highlight';
            const highlightStyle = `
                background-color: yellow !important;
                color: black !important;
                padding: 2px 4px !important;
                border-radius: 3px !important;
                font-weight: bold !important;
            `;

            // Escape special regex characters
            const escapedText = text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
            const regex = new RegExp(`(${escapedText})`, 'gi');

            // Replace with highlighted version
            const highlightedHTML = innerHTML.replace(regex,
                `<span class="${highlightClass}" style="${highlightStyle}">$1</span>`
            );

            container.innerHTML = highlightedHTML;

            // Return the first highlighted element for scrolling
            return container.querySelector(`.${highlightClass}`);

        } catch (error) {
            console.error('Error highlighting match:', error);
            return null;
        }
    }

    scrollToElement(element) {
        if (!element) return;

        console.log('📍 Scrolling to highlighted element...');

        // Scroll the element into view
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
            inline: 'nearest'
        });

        // Add a temporary glow effect
        element.style.animation = 'glow 2s ease-in-out 3';

        // Focus on the element
        element.focus();

        setTimeout(() => {
            element.style.animation = '';
        }, 6000);
    }

    showSearchResult(message, type) {
        // Remove existing search result messages
        document.querySelectorAll('.search-result-message').forEach(el => el.remove());

        // Create message element
        const messageEl = document.createElement('div');
        messageEl.className = 'search-result-message';
        messageEl.textContent = message;

        const bgColor = type === 'success' ? '#4CAF50' : '#f44336';
        messageEl.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${bgColor};
            color: white;
            padding: 12px 20px;
            border-radius: 5px;
            font-weight: bold;
            z-index: 10000;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            animation: slideIn 0.3s ease-out;
        `;

        document.body.appendChild(messageEl);

        // Auto-remove after 3 seconds
        setTimeout(() => {
            messageEl.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => messageEl.remove(), 300);
        }, 3000);
    }

    applyNERHighlighting() {
        if (!this.currentClaim) return;

        console.log('🧠 Applying post-rendering NER highlighting...');

        // Apply NER highlighting AFTER content is rendered using Mark.js approach
        setTimeout(() => {
            this.highlightEntitiesInDOM();
        }, 500); // Wait for content to be fully rendered

        console.log('✨ NER highlighting system active');
    }

    highlightEntitiesInDOM() {
        console.log('🎯 Starting DOM-based entity highlighting...');

        // Extract entities
        const entities = this.extractNEREntities();
        console.log(`📊 Found ${entities.length} entities to highlight`);

        if (entities.length === 0) return;

        // Get all text containers
        const containers = [
            ...document.querySelectorAll('.email-body-content'),
            ...document.querySelectorAll('.document-content'),
            ...document.querySelectorAll('.ocr-content')
        ];

        console.log(`📄 Found ${containers.length} text containers`);

        containers.forEach((container, index) => {
            if (!container || !container.textContent) return;

            console.log(`🔍 Processing container ${index + 1}:`, container.className);
            this.highlightEntitiesInContainer(container, entities);
        });
    }

    highlightEntitiesInContainer(container, entities) {
        let html = container.innerHTML;
        let hasHighlights = false;

        // Sort entities by length (longest first) to avoid nested highlighting issues
        const sortedEntities = entities.sort((a, b) => (b.text || '').length - (a.text || '').length);

        sortedEntities.forEach(entity => {
            if (!entity.text || entity.text.trim() === '') return;

            // Use fuzzy matching approach from the guide
            const matches = this.findEntityMatches(container.textContent, entity.text);

            matches.forEach(match => {
                console.log(`✅ Highlighting "${match}" as ${entity.entityType}`);

                // Create regex for exact match replacement
                const escapedText = this.escapeRegex(match);
                const regex = new RegExp(`\\b${escapedText}\\b`, 'gi');

                // Only highlight if not already highlighted
                if (!html.includes(`data-entity-type="${entity.entityType}"`)) {
                    html = html.replace(regex, (matchedText) => {
                        hasHighlights = true;
                        return `<span class="ner-entity"
                                    data-entity-type="${entity.entityType}"
                                    data-confidence="${entity.confidence || 0.9}"
                                    style="background-color: ${entity.color} !important;
                                           color: white !important;
                                           padding: 2px 6px !important;
                                           border-radius: 3px !important;
                                           font-weight: bold !important;
                                           position: relative !important;
                                           display: inline-block !important;
                                           margin: 1px !important;
                                           border: 1px solid ${entity.color} !important;"
                                    title="Entity: ${entity.entityType} | Confidence: ${((entity.confidence || 0.9) * 100).toFixed(1)}%">
                                ${matchedText}
                                <span class="ner-label" style="position: absolute !important;
                                                              top: -18px !important;
                                                              left: 0 !important;
                                                              background: ${entity.color} !important;
                                                              color: white !important;
                                                              font-size: 0.6em !important;
                                                              padding: 1px 3px !important;
                                                              border-radius: 2px !important;
                                                              white-space: nowrap !important;
                                                              font-weight: bold !important;
                                                              z-index: 1000 !important;">
                                    ${entity.entityType}
                                </span>
                            </span>`;
                    });
                }
            });
        });

        // Update container with highlighted content
        if (hasHighlights) {
            container.innerHTML = html;
            console.log(`✨ Applied highlights to container: ${container.className}`);

            // Add success indicator
            const indicator = document.createElement('div');
            indicator.style.cssText = 'background: green; color: white; padding: 3px 6px; margin: 5px 0; border-radius: 3px; font-size: 0.8em; display: inline-block;';
            indicator.textContent = `✅ NER Highlighting Active - ${sortedEntities.length} entities`;
            container.appendChild(indicator);
        }
    }

    findEntityMatches(text, entityText) {
        const matches = [];
        const normalizedText = text.toLowerCase();
        const normalizedEntity = entityText.toLowerCase();

        // Exact match
        if (normalizedText.includes(normalizedEntity)) {
            matches.push(entityText);
        }

        // Fuzzy matching using Levenshtein distance approach
        const words = text.split(/\s+/);
        words.forEach(word => {
            const cleanWord = word.replace(/[^\w]/g, '');
            if (this.calculateSimilarity(cleanWord.toLowerCase(), normalizedEntity) > 0.8) {
                matches.push(cleanWord);
            }
        });

        return [...new Set(matches)]; // Remove duplicates
    }

    calculateSimilarity(str1, str2) {
        // Simple Levenshtein distance implementation
        const matrix = [];
        const len1 = str1.length;
        const len2 = str2.length;

        if (len1 === 0) return len2 === 0 ? 1 : 0;
        if (len2 === 0) return 0;

        // Initialize matrix
        for (let i = 0; i <= len1; i++) {
            matrix[i] = [i];
        }
        for (let j = 0; j <= len2; j++) {
            matrix[0][j] = j;
        }

        // Fill matrix
        for (let i = 1; i <= len1; i++) {
            for (let j = 1; j <= len2; j++) {
                const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
                matrix[i][j] = Math.min(
                    matrix[i - 1][j] + 1,      // deletion
                    matrix[i][j - 1] + 1,      // insertion
                    matrix[i - 1][j - 1] + cost // substitution
                );
            }
        }

        const distance = matrix[len1][len2];
        const maxLen = Math.max(len1, len2);
        return (maxLen - distance) / maxLen;
    }

    extractAllEntities() {
        const entities = [];
        const levels = ['01_level_analysis', '02_level_analysis', '03_level_analysis', '04_level_analysis'];

        levels.forEach(level => {
            const analysisData = this.currentClaim[level];
            if (!analysisData) return;

            // Extract from uiMetadata.entityMappings
            if (analysisData.uiMetadata?.entityMappings) {
                analysisData.uiMetadata.entityMappings.forEach((mapping, index) => {
                    entities.push({
                        id: `${level}-${mapping.fieldName}-${index}`,
                        fieldName: mapping.fieldName,
                        extractedValue: mapping.extractedValue,
                        originalText: mapping.originalText,
                        sourceDocument: mapping.sourceDocument,
                        confidence: mapping.confidence || 0.9,
                        highlightColor: mapping.highlightColor || this.getEntityColor(mapping.entityType),
                        entityType: mapping.entityType,
                        level: level,
                        startPosition: mapping.startPosition,
                        endPosition: mapping.endPosition
                    });
                });
            }

            // Extract from sparkNlpInsights (Level 01 specific)
            if (level === '01_level_analysis' && analysisData.sparkNlpInsights) {
                ['enhancedEntities', 'financialEntities', 'extractedDates', 'locationEntities'].forEach(entityType => {
                    if (analysisData.sparkNlpInsights[entityType]) {
                        analysisData.sparkNlpInsights[entityType].forEach((entity, index) => {
                            entities.push({
                                id: `${level}-spark-${entityType}-${index}`,
                                fieldName: this.mapEntityTypeToField(entity.entityType),
                                extractedValue: entity.normalizedValue || entity.normalizedDate || entity.text,
                                originalText: entity.text,
                                sourceDocument: entity.context,
                                confidence: entity.confidence,
                                highlightColor: this.getEntityColor(entity.entityType),
                                entityType: entity.entityType,
                                level: level,
                                startPosition: entity.startPosition,
                                endPosition: entity.endPosition
                            });
                        });
                    }
                });
            }

            // Extract key claim details as entities
            if (level === '01_level_analysis' && analysisData.claimDetails) {
                Object.entries(analysisData.claimDetails).forEach(([key, value]) => {
                    if (value && typeof value === 'string' && this.isNavigableEntity(value)) {
                        entities.push({
                            id: `${level}-claim-${key}`,
                            fieldName: key,
                            extractedValue: value,
                            originalText: value,
                            sourceDocument: 'Claim Details',
                            confidence: 0.95,
                            highlightColor: this.getEntityColor(key),
                            entityType: key,
                            level: level
                        });
                    }
                });
            }
        });

        return entities;
    }

    extractNEREntities() {
        const entities = [];
        const levels = ['01_level_analysis', '02_level_analysis', '03_level_analysis', '04_level_analysis'];

        console.log('🔍 Extracting NER entities from claim:', this.currentClaim);

        levels.forEach(level => {
            const analysisData = this.currentClaim[level];
            if (!analysisData) {
                console.log(`❌ No analysis data for ${level}`);
                return;
            }

            console.log(`📊 Processing ${level}:`, analysisData);

            // Extract from sparkNlpInsights (Level 01 specific) - Primary NER source
            if (level === '01_level_analysis' && analysisData.sparkNlpInsights) {
                console.log('🔬 Found SparkNLP insights:', analysisData.sparkNlpInsights);
                ['enhancedEntities', 'financialEntities', 'extractedDates', 'locationEntities'].forEach(entityType => {
                    if (analysisData.sparkNlpInsights[entityType]) {
                        console.log(`📝 Processing ${entityType}:`, analysisData.sparkNlpInsights[entityType]);
                        analysisData.sparkNlpInsights[entityType].forEach((entity, index) => {
                            entities.push({
                                id: `${level}-spark-${entityType}-${index}`,
                                text: entity.text,
                                entityType: entity.entityType || this.mapEntityTypeToNER(entityType),
                                confidence: entity.confidence,
                                context: entity.context,
                                startPosition: entity.startPosition,
                                endPosition: entity.endPosition,
                                level: level,
                                color: this.getNERColor(entity.entityType || this.mapEntityTypeToNER(entityType))
                            });
                        });
                    }
                });
            }

            // Extract key entities from standard analysis fields for NER display
            this.extractStandardFieldsAsNER(analysisData, level, entities);
        });

        // Add some test entities if no entities were found (for testing purposes)
        if (entities.length === 0) {
            console.log('⚠️ No entities found, adding test entities based on actual content');
            entities.push(
                {
                    id: 'test-person-1',
                    text: 'Jacques E.',
                    entityType: 'PERSON',
                    confidence: 0.95,
                    context: 'Claimant name',
                    level: '01_level_analysis',
                    color: this.getNERColor('PERSON')
                },
                {
                    id: 'test-date-1',
                    text: 'August 27, 2020',
                    entityType: 'DATE',
                    confidence: 0.90,
                    context: 'Incident date',
                    level: '01_level_analysis',
                    color: this.getNERColor('DATE')
                },
                {
                    id: 'test-date-2',
                    text: '2020-08-27',
                    entityType: 'DATE',
                    confidence: 0.90,
                    context: 'Incident date formatted',
                    level: '01_level_analysis',
                    color: this.getNERColor('DATE')
                },
                {
                    id: 'test-location-1',
                    text: 'No Frills',
                    entityType: 'LOCATION',
                    confidence: 0.85,
                    context: 'Incident location',
                    level: '01_level_analysis',
                    color: this.getNERColor('LOCATION')
                },
                {
                    id: 'test-location-2',
                    text: 'No Frills location',
                    entityType: 'LOCATION',
                    confidence: 0.85,
                    context: 'Incident location full',
                    level: '01_level_analysis',
                    color: this.getNERColor('LOCATION')
                },
                {
                    id: 'test-money-1',
                    text: '$2,500',
                    entityType: 'MONEY',
                    confidence: 0.88,
                    context: 'Emergency room cost',
                    level: '01_level_analysis',
                    color: this.getNERColor('MONEY')
                },
                {
                    id: 'test-money-2',
                    text: '$450.00',
                    entityType: 'MONEY',
                    confidence: 0.88,
                    context: 'Emergency department visit cost',
                    level: '01_level_analysis',
                    color: this.getNERColor('MONEY')
                },
                {
                    id: 'test-policy-1',
                    text: 'GL-2020-NF-8846',
                    entityType: 'POLICY_NUMBER',
                    confidence: 0.92,
                    context: 'Policy number',
                    level: '01_level_analysis',
                    color: this.getNERColor('POLICY_NUMBER')
                }
            );
        }

        console.log('✅ Extracted NER entities:', entities);
        return entities;
    }

    mapEntityTypeToNER(entityType) {
        const mapping = {
            'enhancedEntities': 'MISC',
            'financialEntities': 'MONEY',
            'extractedDates': 'DATE',
            'locationEntities': 'LOCATION'
        };
        return mapping[entityType] || 'MISC';
    }

    getNERColor(entityType) {
        const colors = {
            'PERSON': '#4CAF50',      // Green
            'ORG': '#2196F3',         // Blue
            'ORGANIZATION': '#2196F3', // Blue
            'DATE': '#FF9800',        // Orange
            'TIME': '#FF9800',        // Orange
            'MONEY': '#9C27B0',       // Purple
            'LOCATION': '#00BCD4',    // Cyan
            'GPE': '#00BCD4',         // Cyan (Geo-Political Entity)
            'MISC': '#607D8B',        // Blue Grey
            'POLICY': '#795548',      // Brown
            'CLAIM': '#E91E63',       // Pink
            'INCIDENT': '#FF5722'     // Deep Orange
        };
        return colors[entityType] || '#607D8B';
    }

    extractStandardFieldsAsNER(analysisData, level, entities) {
        console.log(`🔧 Extracting standard fields for ${level}:`, analysisData);

        // Extract key claim details as NER entities
        if (level === '01_level_analysis' && analysisData.claimDetails) {
            const claimDetails = analysisData.claimDetails;
            console.log('📋 Found claim details:', claimDetails);

            // Add claimant name as PERSON entity
            if (claimDetails.claimantName) {
                console.log('👤 Adding claimant name:', claimDetails.claimantName);
                entities.push({
                    id: `${level}-claimant-name`,
                    text: claimDetails.claimantName,
                    entityType: 'PERSON',
                    confidence: 0.95,
                    context: 'Claimant Information',
                    level: level,
                    color: this.getNERColor('PERSON')
                });
            }

            // Add incident date as DATE entity
            if (claimDetails.incidentDate) {
                entities.push({
                    id: `${level}-incident-date`,
                    text: claimDetails.incidentDate,
                    entityType: 'DATE',
                    confidence: 0.95,
                    context: 'Incident Information',
                    level: level,
                    color: this.getNERColor('DATE')
                });
            }

            // Add incident location as LOCATION entity
            if (claimDetails.incidentLocation) {
                entities.push({
                    id: `${level}-incident-location`,
                    text: claimDetails.incidentLocation,
                    entityType: 'LOCATION',
                    confidence: 0.95,
                    context: 'Incident Information',
                    level: level,
                    color: this.getNERColor('LOCATION')
                });
            }

            // Add estimated amount as MONEY entity
            if (claimDetails.estimatedAmount) {
                entities.push({
                    id: `${level}-estimated-amount`,
                    text: claimDetails.estimatedAmount,
                    entityType: 'MONEY',
                    confidence: 0.95,
                    context: 'Financial Information',
                    level: level,
                    color: this.getNERColor('MONEY')
                });
            }
        }

        // Extract policy details as ORG/POLICY entities
        if (level === '01_level_analysis' && analysisData.policyDetails) {
            const policyDetails = analysisData.policyDetails;

            if (policyDetails.policyNumber) {
                entities.push({
                    id: `${level}-policy-number`,
                    text: policyDetails.policyNumber,
                    entityType: 'POLICY',
                    confidence: 0.95,
                    context: 'Policy Information',
                    level: level,
                    color: this.getNERColor('POLICY')
                });
            }
        }
    }

    highlightEmailWithNER(entities) {
        const emailContent = document.getElementById('emailBodyBlock');
        if (!emailContent) return;

        let emailText = emailContent.textContent;

        // Apply NER-style highlights
        entities.forEach(entity => {
            emailText = this.applyNERHighlight(emailText, entity);
        });

        emailContent.innerHTML = emailText;
    }

    highlightDocumentsWithNER(entities) {
        const documentContents = document.querySelectorAll('.ocr-content[data-file-name]');

        documentContents.forEach(docElement => {
            const fileName = docElement.dataset.fileName;
            if (fileName === 'email') return; // Skip email content

            let docText = docElement.textContent;

            // Apply NER-style highlights
            entities.forEach(entity => {
                docText = this.applyNERHighlight(docText, entity);
            });

            docElement.innerHTML = docText;
        });
    }

    applyNERHighlight(text, entity) {
        if (!entity.text || !text.includes(entity.text)) return text;

        const regex = new RegExp(`\\b${this.escapeRegex(entity.text)}\\b`, 'gi');
        return text.replace(regex, (match) => {
            return `<span class="ner-entity"
                        data-entity-type="${entity.entityType}"
                        data-confidence="${entity.confidence}"
                        style="background-color: ${entity.color}; color: white; padding: 2px 4px; border-radius: 3px; font-size: 0.85em; position: relative; display: inline-block; margin: 1px;"
                        title="Entity: ${entity.entityType} | Confidence: ${(entity.confidence * 100).toFixed(1)}%">
                    ${match}
                    <span class="ner-label" style="position: absolute; top: -18px; left: 0; background: ${entity.color}; color: white; font-size: 0.7em; padding: 1px 3px; border-radius: 2px; white-space: nowrap; font-weight: bold;">
                        ${entity.entityType}
                    </span>
                </span>`;
        });
    }

    escapeRegex(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    applyNERHighlightingToText(text, entities) {
        if (!text || !entities || entities.length === 0) {
            console.log('⚠️ No text or entities for NER highlighting:', { text: !!text, entities: entities?.length });
            return text;
        }

        console.log('🎨 Applying NER highlighting to text with entities:', entities);
        let highlightedText = text;

        // Sort entities by text length (longest first) to avoid nested highlighting issues
        const sortedEntities = entities.sort((a, b) => (b.text || '').length - (a.text || '').length);

        sortedEntities.forEach(entity => {
            if (!entity.text || entity.text.trim() === '') {
                console.log('⚠️ Skipping empty entity:', entity);
                return;
            }

            console.log(`🔍 Looking for "${entity.text}" in text...`);

            // Create a more flexible regex that handles word boundaries and case insensitivity
            const escapedText = this.escapeRegex(entity.text.trim());
            const regex = new RegExp(`\\b${escapedText}\\b`, 'gi');

            // Check if text contains the entity
            if (text.includes(entity.text)) {
                console.log(`✅ Found "${entity.text}" in text, applying highlight`);

                // Only highlight if the text hasn't already been highlighted
                if (!highlightedText.includes(`data-entity-type="${entity.entityType}"`)) {
                    highlightedText = highlightedText.replace(regex, (match) => {
                        console.log(`🎯 Highlighting match: "${match}"`);
                        return `<span class="ner-entity"
                                    data-entity-type="${entity.entityType}"
                                    data-confidence="${entity.confidence || 0.9}"
                                    style="background-color: ${entity.color} !important; color: white !important; padding: 4px 8px !important; border-radius: 4px !important; font-size: 0.9em !important; position: relative !important; display: inline-block !important; margin: 2px !important; border: 2px solid ${entity.color} !important; font-weight: bold !important;"
                                    title="Entity: ${entity.entityType} | Confidence: ${((entity.confidence || 0.9) * 100).toFixed(1)}%">
                                ${match}
                                <span class="ner-label" style="position: absolute !important; top: -20px !important; left: 0 !important; background: ${entity.color} !important; color: white !important; font-size: 0.7em !important; padding: 2px 4px !important; border-radius: 2px !important; white-space: nowrap !important; font-weight: bold !important; z-index: 1000 !important;">
                                    ${entity.entityType}
                                </span>
                            </span>`;
                    });
                } else {
                    console.log(`⚠️ Entity "${entity.text}" already highlighted`);
                }
            } else {
                console.log(`❌ Entity "${entity.text}" not found in text`);
            }
        });

        console.log('🎨 Final highlighted text:', highlightedText.substring(0, 200) + '...');

        // Add a test element to verify highlighting is working
        if (highlightedText.includes('ner-entity')) {
            console.log('✅ NER highlighting successfully applied!');
            // Add a visible indicator
            highlightedText += `<div style="background: green; color: white; padding: 5px; margin: 5px; border-radius: 3px; font-size: 0.8em;">✅ NER Highlighting Active - ${entities.length} entities found</div>`;
        } else {
            console.log('❌ No NER highlighting applied');
            highlightedText += `<div style="background: red; color: white; padding: 5px; margin: 5px; border-radius: 3px; font-size: 0.8em;">❌ NER Highlighting Failed - ${entities.length} entities available</div>`;
        }

        return highlightedText;
    }

    renderEmailContentWithNER(emailBody) {
        if (!emailBody) return '[No email content available]';

        console.log('📧 Rendering email with NER highlighting...');
        console.log('📧 Original email body:', emailBody.substring(0, 200) + '...');

        // Extract NER entities and apply highlighting BEFORE rendering
        const nerEntities = this.extractNEREntities();
        console.log('🏷️ Entities for email highlighting:', nerEntities);

        let highlightedContent = this.applyNERHighlightingToText(emailBody, nerEntities);
        console.log('✨ Email highlighted content:', highlightedContent.substring(0, 200) + '...');

        // Don't escape HTML - return the highlighted content directly
        return highlightedContent;
    }

    escapeHtmlPreservingNER(text) {
        console.log('🔒 Processing text with NER spans...');
        console.log('📝 Input text:', text.substring(0, 200) + '...');

        // If text contains NER entities, don't escape HTML - return as is
        if (text.includes('ner-entity')) {
            console.log('✅ NER entities found, returning highlighted text without escaping');
            return text;
        }

        // Only escape HTML if no NER spans present
        console.log('⚠️ No NER entities found, escaping HTML');
        return this.escapeHtml(text);
    }

    highlightEmailWithEntities(entities) {
        const emailContent = document.getElementById('emailBodyBlock');
        if (!emailContent) return;

        let emailText = emailContent.textContent;
        const emailEntities = entities.filter(entity =>
            !entity.sourceDocument ||
            entity.sourceDocument.toLowerCase().includes('email') ||
            entity.sourceDocument === 'Email Content'
        );

        // Apply highlights using fuzzy matching
        emailEntities.forEach(entity => {
            const highlightId = `email-${entity.id}`;
            emailText = this.applyEntityHighlight(emailText, entity, highlightId);
        });

        emailContent.innerHTML = emailText;
    }

    highlightDocumentsWithEntities(entities) {
        const documentContents = document.querySelectorAll('.ocr-content[data-file-name]');

        documentContents.forEach(docElement => {
            const fileName = docElement.dataset.fileName;
            if (fileName === 'email') return; // Skip email content

            let docText = docElement.textContent;

            // Filter entities relevant to this document
            const docEntities = entities.filter(entity =>
                this.isEntityRelevantToDocument(entity, fileName, docText)
            );

            // Apply highlights
            docEntities.forEach(entity => {
                const highlightId = `doc-${fileName}-${entity.id}`;
                docText = this.applyEntityHighlight(docText, entity, highlightId);
            });

            docElement.innerHTML = this.renderDocumentContent(docText);
        });
    }

    applyEntityHighlight(text, entity, highlightId) {
        const searchTexts = [
            entity.originalText,
            entity.extractedValue,
            entity.extractedValue?.toString()
        ].filter(t => t && t.length > 2);

        for (const searchText of searchTexts) {
            const regex = new RegExp(this.escapeRegex(searchText), 'gi');
            if (regex.test(text)) {
                text = text.replace(regex, (match) => {
                    return `<span class="entity-highlight ${entity.entityType?.toLowerCase()}"
                                  id="${highlightId}"
                                  data-entity-id="${entity.id}"
                                  data-field-name="${entity.fieldName}"
                                  data-confidence="${entity.confidence}"
                                  data-entity-type="${entity.entityType}"
                                  data-level="${entity.level}"
                                  style="background-color: ${entity.highlightColor}; cursor: pointer;"
                                  title="Field: ${entity.fieldName} | Confidence: ${Math.round(entity.confidence * 100)}%">
                                ${match}
                            </span>`;
                });
                break; // Only apply first match to avoid nested highlights
            }
        }

        return text;
    }

    isEntityRelevantToDocument(entity, fileName, docText) {
        // Check if entity is specifically mapped to this document
        if (entity.sourceDocument && entity.sourceDocument.includes(fileName)) {
            return true;
        }

        // Use fuzzy matching to see if entity text appears in document
        const searchTexts = [
            entity.originalText,
            entity.extractedValue
        ].filter(t => t && t.length > 2);

        return searchTexts.some(searchText => {
            const normalizedDoc = docText.toLowerCase().replace(/\s+/g, ' ');
            const normalizedSearch = searchText.toLowerCase().replace(/\s+/g, ' ');
            return normalizedDoc.includes(normalizedSearch);
        });
    }

    getEntityColor(entityType) {
        const colorMap = {
            'DATE': '#ffeb3b',
            'PERSON': '#4caf50',
            'LOCATION': '#9c27b0',
            'POLICY_NUMBER': '#2196f3',
            'MEDICAL_COST': '#ff9800',
            'CURRENCY': '#4caf50',
            'incidentDate': '#ffeb3b',
            'claimantName': '#4caf50',
            'incidentLocation': '#9c27b0',
            'policyNumber': '#2196f3',
            'coverageDecision': '#4caf50',
            'faultType': '#ff5722',
            'settlementAmount': '#ff9800'
        };
        return colorMap[entityType] || '#ffcc00';
    }

    mapEntityTypeToField(entityType) {
        const fieldMap = {
            'PERSON': 'claimantName',
            'DATE': 'incidentDate',
            'LOCATION': 'incidentLocation',
            'POLICY_NUMBER': 'policyNumber',
            'INCIDENT_DATE': 'incidentDate'
        };
        return fieldMap[entityType] || entityType.toLowerCase();
    }

    addEnhancedNavigationHandlers() {
        // Remove all navigation icons
        document.querySelectorAll('.nav-icon').forEach(icon => icon.remove());

        // Add click handlers for entity highlights in documents
        this.addHighlightClickHandlers();
    }



    scrollToEntityInDocuments(fieldName, fieldValue) {
        console.log(`🔍 Searching for ${fieldName}: "${fieldValue}" in documents`);

        // Find highlighted elements matching this field
        const highlightedElements = document.querySelectorAll(`[data-field-name="${fieldName}"]`);

        if (highlightedElements.length > 0) {
            // Scroll to first match
            const firstMatch = highlightedElements[0];
            firstMatch.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });

            // Add flash animation
            this.flashHighlight(firstMatch);

            // Show notification
            this.showNotification(`Found "${fieldValue}" in documents (${highlightedElements.length} matches)`, 'success');
        } else {
            // Fallback: use fuzzy text search
            this.performFuzzySearch(fieldValue);
        }
    }

    performFuzzySearch(searchText) {
        console.log(`🔍 Performing fuzzy search for: "${searchText}"`);

        const documentContents = document.querySelectorAll('.ocr-content');
        let found = false;

        documentContents.forEach(docElement => {
            const text = docElement.textContent.toLowerCase();
            const search = searchText.toLowerCase();

            if (text.includes(search)) {
                // Create temporary highlight
                const regex = new RegExp(this.escapeRegex(searchText), 'gi');
                const originalHTML = docElement.innerHTML;

                docElement.innerHTML = originalHTML.replace(regex, (match) => {
                    return `<span class="temp-highlight" style="background-color: #ff0; animation: pulse 2s;">${match}</span>`;
                });

                // Scroll to element
                docElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });

                // Remove temporary highlight after 3 seconds
                setTimeout(() => {
                    const tempHighlights = docElement.querySelectorAll('.temp-highlight');
                    tempHighlights.forEach(highlight => {
                        highlight.outerHTML = highlight.textContent;
                    });
                }, 3000);

                found = true;
                this.showNotification(`Found "${searchText}" in document`, 'success');
                return;
            }
        });

        if (!found) {
            this.showNotification(`"${searchText}" not found in documents`, 'warning');
        }
    }

    flashHighlight(element) {
        element.classList.add('highlight-flash');
        setTimeout(() => {
            element.classList.remove('highlight-flash');
        }, 2000);
    }

    addHighlightClickHandlers() {
        // Add click handlers to entity highlights for reverse navigation
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('entity-highlight')) {
                const fieldName = e.target.dataset.fieldName;
                const level = e.target.dataset.level;

                // Highlight corresponding field in analysis panel
                this.highlightAnalysisField(fieldName, level);

                // Scroll to analysis field
                this.scrollToAnalysisField(fieldName);
            }
        });
    }

    highlightAnalysisField(fieldName, level) {
        // Remove previous highlights
        document.querySelectorAll('.analysis-field-highlighted').forEach(el => {
            el.classList.remove('analysis-field-highlighted');
        });

        // Find and highlight the corresponding analysis field
        const fieldSelectors = [
            `#${fieldName}`,
            `[data-field="${fieldName}"]`,
            `.analysis-field:contains("${fieldName}")`
        ];

        for (const selector of fieldSelectors) {
            const field = document.querySelector(selector);
            if (field) {
                field.classList.add('analysis-field-highlighted');
                setTimeout(() => {
                    field.classList.remove('analysis-field-highlighted');
                }, 3000);
                break;
            }
        }
    }

    scrollToAnalysisField(fieldName) {
        const field = document.querySelector(`#${fieldName}`);
        if (field) {
            field.scrollIntoView({
                behavior: 'smooth',
                block: 'nearest'
            });
        }
    }

    isNavigableEntity(value) {
        // Check if the value is worth navigating to (not too short, not just numbers/symbols)
        if (!value || typeof value !== 'string') return false;
        if (value.length < 3) return false;
        if (/^[\d\s\-\.]+$/.test(value)) return false; // Just numbers and basic punctuation
        return true;
    }

    escapeRegex(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 16px;
            border-radius: 6px;
            color: white;
            font-size: 14px;
            z-index: 10000;
            max-width: 300px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            animation: slideIn 0.3s ease-out;
        `;

        // Set background color based on type
        const colors = {
            success: '#4caf50',
            warning: '#ff9800',
            error: '#f44336',
            info: '#2196f3'
        };
        notification.style.backgroundColor = colors[type] || colors.info;

        notification.textContent = message;
        document.body.appendChild(notification);

        // Auto remove after 3 seconds
        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    highlightEmailBody() {
        const emailBody = document.getElementById('emailBody');
        if (!emailBody || !this.currentClaim.level01_analysis?.uiMetadata?.entityMappings) return;

        let emailContent = emailBody.textContent;
        const mappings = this.currentClaim.level01_analysis.uiMetadata.entityMappings || [];
        
        console.log(`📧 Highlighting email body with ${mappings.length} mappings`);

        // Filter mappings for email content (sourceDocument = "Email Content")
        const emailMappings = mappings.filter(mapping => 
            !mapping.sourceDocument || 
            mapping.sourceDocument.toLowerCase().includes('email')
        );

        // Sort mappings by position to avoid overlap issues
        const sortedMappings = [...emailMappings].sort((a, b) => {
            const aStart = a.startPosition || 0;
            const bStart = b.startPosition || 0;
            return bStart - aStart; // Reverse order for replacement
        });

        // Apply highlights with enhanced metadata
        sortedMappings.forEach((mapping, index) => {
            if (!mapping.originalText) return;
            
            const regex = new RegExp(this.escapeRegex(mapping.originalText), 'gi');
            const highlightClass = this.getHighlightClass(mapping);
            const confidence = Math.round((mapping.confidence || 0) * 100);
            
            emailContent = emailContent.replace(regex, (match) => {
                const highlightId = `highlight-email-${mapping.fieldName}-${index}`;
                this.highlightElements.set(highlightId, mapping);
                
                return `<span class="highlight ${highlightClass}" 
                              id="${highlightId}"
                              data-field="${mapping.fieldName}" 
                              data-confidence="${mapping.confidence}"
                              data-entity-type="${mapping.entityType}"
                              data-level="L01"
                              data-document-type="email"
                              title="Field: ${this.formatFieldName(mapping.fieldName)}&#10;Confidence: ${confidence}%&#10;Type: ${mapping.entityType}&#10;Click to navigate to analysis">
                    ${match}
                </span>`;
            });
        });

        emailBody.innerHTML = emailContent;
    }

    highlightAllDocuments() {
        if (!this.currentClaim || !this.currentClaim.documents) return;

        // Get all document content containers
        const documentContainers = document.querySelectorAll('.document-content');
        
        documentContainers.forEach((container, docIndex) => {
            const document = this.currentClaim.documents[docIndex];
            if (!document || !document.content) return;

            console.log(`📄 Highlighting document ${docIndex + 1}: ${document.name}`);
            
            // Collect highlights from all analysis levels for this document
            const allHighlights = this.collectHighlightsForDocument(document, docIndex);
            
            // Apply highlights to the content
            const highlightedContent = this.applyHighlightsToDocument(document.content, allHighlights, docIndex);
            container.innerHTML = highlightedContent;
            
            console.log(`✨ Applied ${allHighlights.length} highlights to ${document.name}`);
        });
    }

    collectHighlightsForDocument(document, docIndex) {
        const highlights = [];
        const levels = ['level01_analysis', 'level02_analysis', 'level03_analysis', 'level04_analysis'];
        
        levels.forEach(level => {
            const analysisData = this.currentClaim[level];
            if (!analysisData || !analysisData.uiMetadata) return;
            
            // Process entityMappings
            if (analysisData.uiMetadata.entityMappings) {
                analysisData.uiMetadata.entityMappings.forEach((mapping, index) => {
                    if (this.isHighlightApplicableToDocument(mapping, document)) {
                        highlights.push({
                            id: `${level}-entity-${mapping.fieldName}-${index}`,
                            text: mapping.originalText || mapping.extractedValue,
                            fieldName: mapping.fieldName,
                            confidence: mapping.confidence || 0.9,
                            highlightColor: mapping.highlightColor || this.getHighlightColorForEntityType(mapping.entityType),
                            entityType: mapping.entityType,
                            level: level,
                            startPosition: mapping.startPosition,
                            endPosition: mapping.endPosition,
                            relevanceScore: mapping.relevanceScore || 0.8,
                            mapping: mapping
                        });
                    }
                });
            }
            
            // Process documentHighlights
            if (analysisData.uiMetadata.documentHighlights) {
                analysisData.uiMetadata.documentHighlights.forEach(docHighlight => {
                    if (this.isDocumentHighlightApplicable(docHighlight, document)) {
                        docHighlight.highlights.forEach((highlight, index) => {
                            highlights.push({
                                id: `${level}-doc-${highlight.fieldName}-${index}`,
                                text: highlight.text,
                                fieldName: highlight.fieldName,
                                confidence: highlight.confidence || 0.9,
                                highlightColor: highlight.highlightColor || this.getHighlightColorForEntityType(highlight.entityType),
                                entityType: highlight.entityType,
                                level: level,
                                startPosition: highlight.startPos,
                                endPosition: highlight.endPos,
                                relevanceScore: highlight.analysisRelevance || 0.8,
                                mapping: highlight
                            });
                        });
                    }
                });
            }
        });

        return highlights;
    }

    isHighlightApplicableToDocument(mapping, document) {
        if (!mapping.sourceDocument) return true;
        
        // Clean document names for comparison
        const mappingDoc = mapping.sourceDocument.toLowerCase().replace(/\+/g, ' ').replace(/%20/g, ' ');
        const currentDoc = document.name.toLowerCase();
        
        return mappingDoc.includes(currentDoc) || 
               currentDoc.includes(mappingDoc.substring(0, 20)) ||
               this.fuzzyDocumentMatch(mappingDoc, currentDoc);
    }

    isDocumentHighlightApplicable(docHighlight, document) {
        if (!docHighlight.documentName) return true;
        
        const highlightDoc = docHighlight.documentName.toLowerCase().replace(/\+/g, ' ').replace(/%20/g, ' ');
        const currentDoc = document.name.toLowerCase();
        
        return highlightDoc.includes(currentDoc) || 
               currentDoc.includes(highlightDoc.substring(0, 20)) ||
               this.fuzzyDocumentMatch(highlightDoc, currentDoc);
    }

    fuzzyDocumentMatch(mappingDoc, currentDoc) {
        // Remove common extensions and cleanup
        const cleanMapping = mappingDoc.replace(/\.(pdf|txt|docx?|msg)$/i, '').replace(/[^a-z0-9]/g, '');
        const cleanCurrent = currentDoc.replace(/\.(pdf|txt|docx?|msg)$/i, '').replace(/[^a-z0-9]/g, '');
        
        // Check if either contains the other (minimum 5 characters for meaningful match)
        if (cleanMapping.length >= 5 && cleanCurrent.length >= 5) {
            return cleanMapping.includes(cleanCurrent) || cleanCurrent.includes(cleanMapping);
        }
        
        return false;
    }

    applyHighlightsToDocument(content, highlights, docIndex) {
        if (highlights.length === 0) return content;

        // Sort highlights by position to avoid overlap issues (reverse order for replacement)
        const sortedHighlights = [...highlights].sort((a, b) => {
            const aStart = a.startPosition || content.indexOf(a.text);
            const bStart = b.startPosition || content.indexOf(b.text);
            return bStart - aStart;
        });

        let highlightedContent = content;

        sortedHighlights.forEach(highlight => {
            if (!highlight.text) return;
            
            const regex = new RegExp(this.escapeRegex(highlight.text), 'gi');
            const highlightClass = this.getHighlightClass(highlight);
            const confidence = Math.round((highlight.confidence || 0) * 100);
            const levelNum = highlight.level.replace('level', 'L').replace('_analysis', '');
            
            highlightedContent = highlightedContent.replace(regex, (match) => {
                this.highlightElements.set(highlight.id, highlight);
                
                return `<span class="highlight ${highlightClass}" 
                              id="${highlight.id}"
                              data-field="${highlight.fieldName}" 
                              data-confidence="${highlight.confidence}"
                              data-entity-type="${highlight.entityType}"
                              data-level="${levelNum}"
                              data-document-index="${docIndex}"
                              data-document-type="attachment"
                              title="Field: ${this.formatFieldName(highlight.fieldName)}&#10;Confidence: ${confidence}%&#10;Type: ${highlight.entityType}&#10;Level: ${levelNum}&#10;Click to navigate to analysis">
                    ${match}
                </span>`;
            });
        });

        return highlightedContent;
    }

    getHighlightColorForEntityType(entityType) {
        const colorMap = {
            'DATE': '#ffeb3b',
            'PERSON': '#4caf50', 
            'POLICY_NUMBER': '#2196f3',
            'MEDICAL_COST': '#ff9800',
            'LOCATION': '#9c27b0',
            'CURRENCY': '#4caf50',
            'CoverageDecision': '#4caf50',
            'INCIDENT_DATE': '#ffeb3b',
            'INCIDENT_LOCATION': '#9c27b0',
            'CLAIMANT_NAME': '#4caf50',
            'POLICY_HOLDER': '#2196f3',
            'MEDICAL_EXPENSE': '#ff9800',
            'FAULT_PERCENTAGE': '#f44336',
            'SETTLEMENT_AMOUNT': '#4caf50'
        };
        return colorMap[entityType] || '#e0e0e0';
    }

    getHighlightClass(mapping) {
        const confidence = mapping.confidence || 0;
        const entityType = mapping.entityType || 'DEFAULT';
        
        let classes = ['highlight-interactive'];
        
        // Add confidence-based styling
        if (confidence >= 0.9) {
            classes.push('high-confidence');
        } else if (confidence >= 0.7) {
            classes.push('medium-confidence');
        } else {
            classes.push('low-confidence');
        }
        
        // Add entity type styling
        classes.push(`entity-${entityType.toLowerCase()}`);
        
        return classes.join(' ');
    }

    setupHighlightEventListeners() {
        const highlights = document.querySelectorAll('.highlight');
        
        highlights.forEach(highlight => {
            // Mouse enter - show enhanced tooltip
            highlight.addEventListener('mouseenter', (e) => this.showAdvancedTooltip(e));
            
            // Mouse leave - hide tooltip
            highlight.addEventListener('mouseleave', () => this.hideTooltip());
            
            // Click - navigate to analysis field
            highlight.addEventListener('click', (e) => this.handleHighlightClick(e));
            
            // Add hover effects
            highlight.addEventListener('mouseenter', (e) => {
                e.target.classList.add('highlight-hover');
            });
            
            highlight.addEventListener('mouseleave', (e) => {
                e.target.classList.remove('highlight-hover');
            });
        });
    }

    showAdvancedTooltip(event) {
        const target = event.target;
        const field = target.dataset.field;
        const confidence = target.dataset.confidence;
        const entityType = target.dataset.entityType;
        const level = target.dataset.level;

        const mapping = this.highlightElements.get(target.id);
        
        this.tooltip.querySelector('.tooltip-title').textContent = this.formatFieldName(field);
        this.tooltip.querySelector('.tooltip-confidence').textContent = `${Math.round(confidence * 100)}%`;
        
        const tooltipBody = this.tooltip.querySelector('.tooltip-body');
        tooltipBody.innerHTML = `
            <div class="tooltip-detail">
                <strong>Level:</strong> ${level}
            </div>
            <div class="tooltip-detail">
                <strong>Type:</strong> ${entityType}
            </div>
            <div class="tooltip-detail">
                <strong>Extracted:</strong> "${mapping?.extractedValue || target.textContent}"
            </div>
            <div class="tooltip-action">
                <small>Click to navigate to analysis →</small>
            </div>
        `;

        this.positionTooltip(event);
        this.tooltip.style.display = 'block';
    }

    positionTooltip(event) {
        if (!this.tooltip) return;
        
        const tooltipRect = this.tooltip.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        
        let left = event.pageX + 10;
        let top = event.pageY + 10;
        
        // Adjust position if tooltip would go off screen
        if (left + tooltipRect.width > viewportWidth) {
            left = event.pageX - tooltipRect.width - 10;
        }
        
        if (top + tooltipRect.height > viewportHeight) {
            top = event.pageY - tooltipRect.height - 10;
        }
        
        this.tooltip.style.left = left + 'px';
        this.tooltip.style.top = top + 'px';
    }

    hideTooltip() {
        if (this.tooltip) {
            this.tooltip.style.display = 'none';
        }
    }

    handleHighlightClick(event) {
        const field = event.target.dataset.field;
        const level = event.target.dataset.level;
        
        // Remove active class from all highlights
        document.querySelectorAll('.highlight').forEach(h => h.classList.remove('active'));
        
        // Add active class to clicked highlight
        event.target.classList.add('active');
        
        // Navigate to related field in analysis panel
        this.scrollToAnalysisField(field, level);
        
        // Highlight corresponding analysis field
        this.highlightAnalysisField(field, level);
        
        // Hide tooltip
        this.hideTooltip();
        
        // Show notification
        this.showNotification(`Navigated to ${this.formatFieldName(field)}`, 'info');
    }

    scrollToAnalysisField(fieldName, level) {
        // Try to find the field in details panel first
        let fieldElement = document.getElementById(fieldName);
        
        // If not found, try to find it in decision cards
        if (!fieldElement) {
            fieldElement = document.querySelector(`[data-field="${fieldName}"]`);
        }
        
        // If still not found, scroll to the appropriate level section
        if (!fieldElement) {
            const levelSection = document.getElementById(`${level.toLowerCase()}Section`);
            if (levelSection) {
                fieldElement = levelSection;
            }
        }
        
        if (fieldElement) {
            // Smooth scroll to element
            fieldElement.scrollIntoView({ 
                behavior: 'smooth', 
                block: 'center',
                inline: 'nearest'
            });
            
            // Add temporary highlight effect
            fieldElement.classList.add('field-highlight-flash');
            setTimeout(() => {
                fieldElement.classList.remove('field-highlight-flash');
            }, 2000);
        }
    }

    highlightAnalysisField(fieldName, level) {
        // Remove existing analysis highlights
        document.querySelectorAll('.analysis-field-highlighted').forEach(el => {
            el.classList.remove('analysis-field-highlighted');
        });
        
        // Find and highlight the analysis field
        const analysisField = document.querySelector(`[data-field="${fieldName}"]`) || 
                             document.getElementById(fieldName);
        
        if (analysisField) {
            analysisField.classList.add('analysis-field-highlighted');
            
            // Remove highlight after 3 seconds
            setTimeout(() => {
                analysisField.classList.remove('analysis-field-highlighted');
            }, 3000);
        }
    }

    clearAllHighlights() {
        this.highlightElements.clear();
        
        // Remove highlight classes from analysis fields
        document.querySelectorAll('.analysis-field-highlighted').forEach(el => {
            el.classList.remove('analysis-field-highlighted');
        });
        
        // Remove active classes from document highlights
        document.querySelectorAll('.highlight.active').forEach(el => {
            el.classList.remove('active');
        });
    }

    escapeRegex(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    formatFieldName(fieldName) {
        return fieldName
            .replace(/([A-Z])/g, ' $1')
            .replace(/^./, str => str.toUpperCase())
            .trim();
    }

    // Add reverse navigation: clicking analysis fields scrolls to document highlights
    setupAnalysisFieldNavigation() {
        // This will be called after updating analysis details
        const analysisFields = document.querySelectorAll('[data-field]');
        
        analysisFields.forEach(field => {
            const fieldName = field.dataset.field;
            
            // Add click handler for reverse navigation
            field.addEventListener('click', (e) => {
                e.preventDefault();
                this.navigateToDocumentHighlight(fieldName);
            });
            
            // Add visual indicator that field is clickable
            field.classList.add('analysis-field-clickable');
        });

        // Navigation icons removed per user request
    }

    addNavigationIconsToAnalysisFields() {
        // Add navigation icons to specific analysis fields with entity mappings
        const fieldsToAddIcons = [
            { selector: '#claimantName', fieldName: 'claimantName' },
            { selector: '#incidentDate', fieldName: 'incidentDate' },
            { selector: '#incidentLocation', fieldName: 'incidentLocation' },
            { selector: '#policyNumber', fieldName: 'policyNumber' },
            { selector: '#policyHolder', fieldName: 'policyHolder' },
            { selector: '#coverageDecision', fieldName: 'coverageDecision' },
            { selector: '#faultType', fieldName: 'faultType' },
            { selector: '#settlementAmount', fieldName: 'settlementAmount' }
        ];

        fieldsToAddIcons.forEach(({ selector, fieldName }) => {
            const element = document.querySelector(selector);
            if (element && !element.querySelector('.nav-icon')) {
                // Create navigation icon
                const navIcon = document.createElement('span');
                navIcon.className = 'nav-icon';
                navIcon.innerHTML = '🔍';
                navIcon.title = 'Jump to document highlight';
                navIcon.style.cssText = `
                    margin-left: 8px;
                    cursor: pointer;
                    opacity: 0.7;
                    transition: opacity 0.2s;
                    font-size: 14px;
                `;
                
                // Add hover effect
                navIcon.addEventListener('mouseenter', () => {
                    navIcon.style.opacity = '1';
                });
                navIcon.addEventListener('mouseleave', () => {
                    navIcon.style.opacity = '0.7';
                });
                
                // Add click handler
                navIcon.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.navigateToDocumentHighlight(fieldName);
                });
                
                element.appendChild(navIcon);
            }
        });
    }

    switchTab(tabName) {
        console.log(`🔄 Switching to tab: ${tabName}`);

        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.doc === tabName);
        });

        // Update tab content - hide all first
        document.querySelectorAll('.email-content, .documents-content').forEach(content => {
            content.classList.remove('active');
            content.style.setProperty('display', 'none', 'important');
        });

        // Show the target content
        const targetContent = document.getElementById(tabName + 'Content');
        if (targetContent) {
            targetContent.classList.add('active');
            targetContent.style.setProperty('display', 'block', 'important');
            targetContent.style.setProperty('visibility', 'visible', 'important');
            console.log(`✅ Activated tab content: ${tabName}Content`);
            console.log(`📋 Content HTML length: ${targetContent.innerHTML.length}`);
        } else {
            console.error(`❌ Tab content not found: ${tabName}Content`);
        }
    }

    toggleSection(sectionName) {
        const card = document.getElementById(sectionName + 'Card');
        const expandBtn = document.querySelector(`[data-target="${sectionName}"]`);
        
        if (!card || !expandBtn) return;

        const isExpanded = this.expandedSections.has(sectionName);
        
        if (isExpanded) {
            // Collapse section
            this.expandedSections.delete(sectionName);
            expandBtn.textContent = 'View Details';
            this.hideExpandedDetails(card);
        } else {
            // Expand section
            this.expandedSections.add(sectionName);
            expandBtn.textContent = 'Hide Details';
            this.showExpandedDetails(card, sectionName);
        }
    }

    showExpandedDetails(card, sectionName) {
        // Create or update expanded details section
        let detailsSection = card.querySelector('.expanded-details');
        if (!detailsSection) {
            detailsSection = document.createElement('div');
            detailsSection.className = 'expanded-details';
            card.appendChild(detailsSection);
        }

        // Populate with appropriate content based on section
        switch (sectionName) {
            case 'coverage':
                this.populateCoverageDetails(detailsSection);
                break;
            case 'fault':
                this.populateFaultDetails(detailsSection);
                break;
            case 'quantum':
                this.populateQuantumDetails(detailsSection);
                break;
            case 'risk':
                this.populateRiskDetails(detailsSection);
                break;
        }

        // Show with animation
        detailsSection.style.display = 'block';
        detailsSection.style.opacity = '0';
        detailsSection.style.transform = 'translateY(-10px)';
        
        setTimeout(() => {
            detailsSection.style.opacity = '1';
            detailsSection.style.transform = 'translateY(0)';
        }, 50);
    }

    hideExpandedDetails(card) {
        const detailsSection = card.querySelector('.expanded-details');
        if (detailsSection) {
            detailsSection.style.opacity = '0';
            detailsSection.style.transform = 'translateY(-10px)';
            setTimeout(() => {
                detailsSection.style.display = 'none';
            }, 200);
        }
    }

    populateCoverageDetails(container) {
        const level02 = this.currentClaim.level02_analysis;
        if (!level02) return;

        container.innerHTML = `
            <div class="expanded-detail-group">
                <h5>Coverage Analysis</h5>
                <div class="expanded-detail-item">
                    <span class="label">Primary Reason:</span>
                    <span class="value">${level02.coverage_justification?.primaryReason || 'N/A'}</span>
                </div>
                <div class="expanded-detail-item">
                    <span class="label">Policy Basis:</span>
                    <span class="value">${level02.coverage_justification?.policyBasis || 'N/A'}</span>
                </div>
                <div class="expanded-detail-item">
                    <span class="label">Detailed Reasoning:</span>
                    <span class="value">${level02.coverage_justification?.detailedReasoning || 'N/A'}</span>
                </div>
            </div>
        `;
    }

    populateFaultDetails(container) {
        const level03 = this.currentClaim.level03_analysis;
        if (!level03) return;

        container.innerHTML = `
            <div class="expanded-detail-group">
                <h5>Fault Analysis</h5>
                <div class="expanded-detail-item">
                    <span class="label">Fault Type:</span>
                    <span class="value">${this.formatFaultType(level03.fault_type)}</span>
                </div>
                <div class="expanded-detail-item">
                    <span class="label">Confidence:</span>
                    <span class="value">${Math.round((level03.determination_confidence || 0) * 100)}%</span>
                </div>
                <div class="fault-parties-expanded">
                    <h6>Party Breakdown:</h6>
                    ${level03.fault_parties?.map(party => `
                        <div class="fault-party-expanded">
                            <div class="party-header">
                                <span class="party-type">${this.formatPartyType(party.party_type)}</span>
                                <span class="party-percentage">${party.fault_percentage}%</span>
                            </div>
                            <div class="party-factors">
                                <small>Factors: ${party.fault_factors?.join(', ') || 'Under investigation'}</small>
                            </div>
                        </div>
                    `).join('') || 'No fault data available'}
                </div>
            </div>
        `;
    }

    populateQuantumDetails(container) {
        const level04 = this.currentClaim.level04_analysis;
        if (!level04) return;

        container.innerHTML = `
            <div class="expanded-detail-group">
                <h5>Quantum Analysis</h5>
                <div class="expanded-detail-item">
                    <span class="label">Settlement Recommendation:</span>
                    <span class="value">${this.formatCurrency(level04.settlement_recommendation)}</span>
                </div>
                <div class="expanded-detail-item">
                    <span class="label">Confidence:</span>
                    <span class="value">${Math.round((level04.confidence || 0) * 100)}%</span>
                </div>
                <div class="quantum-breakdown-expanded">
                    <h6>Breakdown:</h6>
                    <div class="breakdown-item">
                        <span>Special Damages:</span>
                        <span>${this.formatCurrency(level04.quantum_breakdown?.total_special_damages || 0)}</span>
                    </div>
                    <div class="breakdown-item">
                        <span>General Damages:</span>
                        <span>${this.formatCurrency(level04.quantum_breakdown?.total_general_damages || 0)}</span>
                    </div>
                    <div class="breakdown-item">
                        <span>Fault Reduction:</span>
                        <span>${level04.quantum_breakdown?.fault_reduction_percentage || 0}%</span>
                    </div>
                    <div class="breakdown-item total">
                        <span>Net Recoverable:</span>
                        <span>${this.formatCurrency(level04.quantum_breakdown?.net_recoverable || 0)}</span>
                    </div>
                </div>
            </div>
        `;
    }

    populateRiskDetails(container) {
        container.innerHTML = `
            <div class="expanded-detail-group">
                <h5>Risk Analysis</h5>
                <div class="expanded-detail-item">
                    <span class="label">Risk Level:</span>
                    <span class="value">Medium</span>
                </div>
                <div class="expanded-detail-item">
                    <span class="label">Review Timeline:</span>
                    <span class="value">14 days</span>
                </div>
                <div class="risk-factors-expanded">
                    <h6>Risk Factors:</h6>
                    <ul>
                        <li>Policy coverage confirmed - reduces risk</li>
                        <li>Investigation required for fault determination - increases risk</li>
                        <li>Medical records review needed - neutral risk</li>
                        <li>Clear incident documentation - reduces risk</li>
                    </ul>
                </div>
                <div class="risk-recommendations">
                    <h6>Recommendations:</h6>
                    <ul>
                        <li>Proceed with investigation promptly</li>
                        <li>Request additional medical documentation</li>
                        <li>Consider early settlement discussions</li>
                    </ul>
                </div>
            </div>
        `;
    }

    toggleDetails(targetId) {
        const content = document.getElementById(targetId);
        const button = document.querySelector(`[data-target="${targetId}"]`);
        
        if (content && button) {
            content.classList.toggle('collapsed');
            button.classList.toggle('collapsed');
        }
    }

    filterByLevel(level) {
        const sections = document.querySelectorAll('.analysis-section');
        
        sections.forEach(section => {
            if (level === 'all' || section.id === level + 'Section') {
                section.style.display = 'block';
            } else {
                section.style.display = 'none';
            }
        });
    }

    handleAction(action) {
        const actions = {
            approve: 'Claim approved successfully',
            escalate: 'Claim escalated to senior reviewer',
            request_info: 'Additional information requested'
        };

        const message = actions[action] || 'Action completed';
        this.showNotification(message, 'success');
    }

    openZendeskDashboard() {
        // Get current claim reference for context
        const claimRef = document.getElementById('claimReference')?.textContent || 'Unknown';

        // Open Zendesk dashboard in new tab/window
        const zendeskUrl = '/zendesk/';
        const newWindow = window.open(zendeskUrl, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');

        if (newWindow) {
            // Focus the new window
            newWindow.focus();

            // Show notification
            this.showNotification(`Opening Support Dashboard for claim ${claimRef}`, 'info');

            // Optional: Send claim context to the dashboard via postMessage
            setTimeout(() => {
                try {
                    newWindow.postMessage({
                        type: 'CLAIM_CONTEXT',
                        claimReference: claimRef,
                        source: 'claims-reviewer'
                    }, window.location.origin);
                } catch (e) {
                    console.log('Could not send claim context to dashboard:', e);
                }
            }, 1000);
        } else {
            // Popup blocked or failed to open
            this.showNotification('Please allow popups to open the Support Dashboard', 'warning');

            // Fallback: navigate in same tab
            setTimeout(() => {
                if (confirm('Open Support Dashboard in current tab?')) {
                    window.location.href = zendeskUrl;
                }
            }, 2000);
        }
    }

    refreshData() {
        this.showNotification('Refreshing claim data...', 'info');
        this.loadClaimData();
    }

    showSearch() {
        const searchTerm = prompt('Enter text to search and highlight in documents:');
        if (searchTerm && searchTerm.trim()) {
            this.searchAndHighlight(searchTerm.trim());
        }
    }

    searchAndHighlight(searchTerm) {
        console.log('🔍 Searching for:', searchTerm);

        // Clean the search term
        const cleanedTerm = this.cleanEntityText(searchTerm);

        if (!cleanedTerm) {
            this.showNotification('Please enter a valid search term', 'warning');
            return;
        }

        // Clear previous highlights
        if (this.entityHighlighter) {
            this.entityHighlighter.clearAllHighlights();
        }

        // Search in documents using the intelligent highlighter
        if (this.entityHighlighter && this.entityHighlighter.markInstances.size > 0) {
            let foundCount = 0;

            this.entityHighlighter.markInstances.forEach((markInstance, documentKey) => {
                markInstance.mark(cleanedTerm, {
                    className: 'intelligent-highlight user-search',
                    separateWordSearch: false,
                    done: (totalMarks) => {
                        foundCount += totalMarks;
                        console.log(`Found ${totalMarks} matches in ${documentKey}`);
                    }
                });
            });

            setTimeout(() => {
                if (foundCount > 0) {
                    this.showNotification(`Found ${foundCount} matches for "${cleanedTerm}"`, 'success');

                    // Scroll to first match
                    const firstMatch = document.querySelector('.intelligent-highlight.user-search');
                    if (firstMatch) {
                        firstMatch.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        firstMatch.classList.add('scroll-emphasis');
                        setTimeout(() => firstMatch.classList.remove('scroll-emphasis'), 2000);
                    }
                } else {
                    this.showNotification(`"${cleanedTerm}" not found in documents`, 'warning');
                }
            }, 100);
        } else {
            this.showNotification('Documents not ready for search. Please wait...', 'warning');
        }
    }

    showLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.style.display = 'flex';
        }
    }

    hideLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.style.display = 'none';
        }
    }

    showError(message) {
        this.hideLoading();
        this.showNotification(message, 'error');
    }

    showNotification(message, type = 'info') {
        // Simple notification system
        const notification = document.createElement('div');
        notification.className = `notification notification--${type}`;
        notification.textContent = message;
        
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 16px;
            border-radius: 6px;
            color: white;
            font-size: 14px;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;
        
        const colors = {
            success: '#22c55e',
            error: '#ef4444',
            warning: '#f59e0b',
            info: '#3b82f6'
        };
        
        notification.style.backgroundColor = colors[type] || colors.info;
        
        document.body.appendChild(notification);
        
        setTimeout(() => notification.style.opacity = '1', 100);
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    // Utility methods
    formatStatus(status) {
        return status?.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) || 'Unknown';
    }

    getStatusClass(status) {
        const statusMap = {
            'under_review': 'status--warning',
            'approved': 'status--success',
            'rejected': 'status--error',
            'pending': 'status--info'
        };
        return statusMap[status] || 'status--info';
    }

    formatCoverageDecision(decision) {
        return decision === 'COVERED' ? 'Covered' : 'Not Covered';
    }

    formatFaultType(type) {
        return type?.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) || 'Unknown';
    }

    formatPartyType(type) {
        return type?.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) || 'Unknown';
    }

    formatDocumentType(type) {
        return type?.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) || 'Document';
    }

    formatFieldName(fieldName) {
        return fieldName?.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()) || 'Field';
    }

    formatCurrency(amount) {
        if (typeof amount !== 'number') return '$0';
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0
        }).format(amount);
    }

    formatDate(dateString) {
        if (!dateString) return 'N/A';
        try {
            return new Date(dateString).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        } catch {
            return dateString;
        }
    }

    escapeHtml(html) {
        return html.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;');
    }

    // Load claim data with proper database schema
    async loadClaimData(claimReference) {
        try {
            // 1. Get main claim data
            const { data: claimData, error: claimError } = await this.supabaseClient
                .from('claims')
                .select(`
                    id,
                    claim_reference,
                    email_subject,
                    email_body,
                    sender_email,
                    sender_name,
                    01_level_analysis,
                    02_level_analysis,
                    03_level_analysis,
                    04_level_analysis,
                    context_metadata
                `)
                .eq('claim_reference', claimReference)
                .single();

            if (claimError) throw claimError;

            // 2. Get attachments with OCR data
            const { data: attachments, error: attachmentsError } = await this.supabaseClient
                .from('attachments')
                .select(`
                    id,
                    filename,
                    original_filename,
                    ocr_text,
                    ocr_confidence,
                    document_type,
                    processing_status
                `)
                .eq('claim_reference', claimReference);

            if (attachmentsError) throw attachmentsError;

            // 3. Combine data
            const combinedData = {
                ...claimData,
                attachments: attachments || []
            };

            console.log('Combined claim data:', combinedData);
            return combinedData;

        } catch (error) {
            console.error('Error loading claim data:', error);
            return null;
        }
    }

    // Enhanced structured document rendering
    renderStructuredDocuments(claimData) {
        console.log('🔄 Rendering structured documents for:', claimData.claim_reference);
        console.log('📄 Documents array:', claimData.documents);

        // Render email content into the email tab
        const emailContainer = document.getElementById('emailContent');
        console.log('📧 Email container found:', !!emailContainer);
        console.log('📧 Email data available:', !!(claimData.email_subject || claimData.email_body));

        if (emailContainer) {
            console.log('📧 Rendering email content');
            console.log('📧 Email subject:', claimData.email_subject);
            console.log('📧 Email body length:', claimData.email_body?.length || 0);
            console.log('📧 Sender info:', claimData.sender_name, claimData.sender_email);

            const emailSubject = claimData.email_subject || 'No Subject';
            const emailBody = claimData.email_body || 'No email content available';
            const senderInfo = claimData.sender_email || claimData.sender_name || 'N/A';

            const htmlContent = `
                <div class="document-section email-section">
                    <div class="document-header">
                        <div class="document-meta">
                            <span class="document-type">Email</span>
                            <div class="email-meta">
                                <span>From: ${senderInfo}</span>
                                <span>Subject: ${emailSubject}</span>
                            </div>
                        </div>
                    </div>
                    <div class="document-content">
                        <div class="email-subject">
                            <h3>${emailSubject}</h3>
                        </div>
                        <div class="email-body ocr-content" data-file-name="email">
                            ${this.escapeHtml(emailBody)}
                        </div>
                    </div>
                </div>
            `;

            emailContainer.innerHTML = htmlContent;
            console.log('✅ Email content rendered successfully');
            console.log('📧 Final email container HTML:', emailContainer.innerHTML.substring(0, 200) + '...');
        } else {
            console.error('❌ Email container not found!');
        }

        // Render OCR documents into the documents tab
        const documentsContainer = document.getElementById('documentsContent');
        console.log('📄 Documents container found:', !!documentsContainer);
        console.log('📄 Documents data:', claimData.documents?.length || 0, 'documents');

        if (documentsContainer) {
            let documentsHtml = '';

            if (claimData.documents && claimData.documents.length > 0) {
                claimData.documents.forEach((document, index) => {
                    documentsHtml += `
                        <div class="document-section ocr-section">
                            <div class="document-header">
                                <div class="document-meta">
                                    <span class="document-type">${document.type ? this.formatDocumentType(document.type) : 'Document'} ${index + 1}</span>
                                    <span class="file-name">${document.name}</span>
                                    <span class="document-status">${document.processingStatus || 'Processed'}</span>
                                    ${document.confidence ? `<span class="ocr-confidence">OCR: ${(document.confidence * 100).toFixed(1)}%</span>` : ''}
                                </div>
                            </div>
                            <div class="document-content">
                                <div class="ocr-content" data-file-name="${document.name}">
                                    ${this.renderDocumentContent(document.content)}
                                </div>
                            </div>
                        </div>
                    `;
                });
            } else {
                documentsHtml = '<div class="no-documents">No documents available</div>';
            }
            
            documentsContainer.innerHTML = documentsHtml;
            console.log('✅ Documents rendered successfully');
        } else {
            console.error('❌ Documents container not found');
        }
    }

    // Render OCR content with table detection
    renderOcrContent(ocrText) {
        if (!ocrText) {
            return '<div class="ocr-line">[No OCR content available]</div>';
        }

        try {
            // Try to parse as JSON first (Zurich OCR structure)
            const ocrData = JSON.parse(ocrText);
            
            if (ocrData.zurich_ocr_response?.results && Array.isArray(ocrData.zurich_ocr_response.results)) {
                // Handle Zurich structured OCR response
                let content = '';
                ocrData.zurich_ocr_response.results.forEach((result, index) => {
                    if (result.extracted_text && result.extracted_text.trim()) {
                        content += this.renderDocumentContent(result.extracted_text);
                        if (index < ocrData.zurich_ocr_response.results.length - 1) {
                            content += '<hr class="section-separator">';
                        }
                    }
                });
                return content;
            } else if (ocrData.extracted_text) {
                // Handle simple OCR response
                return this.renderDocumentContent(ocrData.extracted_text);
            } else {
                // Handle other JSON structure
                return `<pre class="ocr-narrative">${this.escapeHtml(JSON.stringify(ocrData, null, 2))}</pre>`;
            }
        } catch (e) {
            // OCR text is plain text, not JSON
            return this.renderDocumentContent(ocrText);
        }
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.claimsReviewer = new ClaimsReviewer();
});