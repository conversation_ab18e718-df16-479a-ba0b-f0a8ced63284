<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zurich Workflow System - Main Dashboard</title>
    <link rel="icon" type="image/svg+xml" href="./assets/zurich-logo.svg">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="gradient-bg text-white shadow-lg">
        <div class="container mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="w-10 h-10 bg-white rounded-lg flex items-center justify-center">
                        <span class="text-blue-600 font-bold text-xl">Z</span>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold">Zurich Workflow System</h1>
                        <p class="text-blue-100">AI-Powered Claims Processing Platform</p>
                    </div>
                </div>
                <div class="text-right">
                    <p class="text-sm text-blue-100">Local Development</p>
                    <p class="text-xs text-blue-200">Version 1.0.0</p>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-6 py-12">
        <!-- Welcome Section -->
        <div class="text-center mb-12">
            <h2 class="text-4xl font-bold text-gray-800 mb-4">Welcome to Zurich Workflow System</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                A comprehensive AI-powered platform for insurance claims processing, 
                featuring advanced document analysis, workflow automation, and intelligent decision support.
            </p>
        </div>

        <!-- Application Cards -->
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            <!-- Claims Reviewer -->
            <div class="bg-white rounded-xl shadow-lg p-6 card-hover">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-800 mb-2">Enhanced Claims Reviewer</h3>
                <p class="text-gray-600 mb-4">Advanced AI-powered interface for comprehensive claims analysis with Level 1-4 processing workflow.</p>
                <a href="/enhanced-claims-reviewer/" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    Open Reviewer
                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </a>
            </div>

            <!-- Claim Tracker -->
            <div class="bg-white rounded-xl shadow-lg p-6 card-hover">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-800 mb-2">Simple Claim Tracker</h3>
                <p class="text-gray-600 mb-4">Streamlined interface for tracking claim status and monitoring processing progress.</p>
                <a href="/simple-claim-tracker/" class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                    Open Tracker
                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </a>
            </div>

            <!-- Support Dashboard -->
            <div class="bg-white rounded-xl shadow-lg p-6 card-hover">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 12h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-800 mb-2">Support Dashboard</h3>
                <p class="text-gray-600 mb-4">Zendesk integration for support ticket management and customer communication.</p>
                <a href="http://localhost:2000" target="_blank" class="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                    Open Dashboard
                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                    </svg>
                </a>
            </div>
        </div>

        <!-- System Status -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
            <h3 class="text-xl font-semibold text-gray-800 mb-4">System Status</h3>
            <div class="grid md:grid-cols-4 gap-4">
                <div class="text-center">
                    <div class="w-3 h-3 bg-green-500 rounded-full mx-auto mb-2"></div>
                    <p class="text-sm font-medium text-gray-800">Backend API</p>
                    <p class="text-xs text-gray-500">Port 8000</p>
                </div>
                <div class="text-center">
                    <div class="w-3 h-3 bg-green-500 rounded-full mx-auto mb-2"></div>
                    <p class="text-sm font-medium text-gray-800">Frontend</p>
                    <p class="text-xs text-gray-500">Port 3000</p>
                </div>
                <div class="text-center">
                    <div class="w-3 h-3 bg-green-500 rounded-full mx-auto mb-2"></div>
                    <p class="text-sm font-medium text-gray-800">Dashboard</p>
                    <p class="text-xs text-gray-500">Port 2000</p>
                </div>
                <div class="text-center">
                    <div class="w-3 h-3 bg-green-500 rounded-full mx-auto mb-2"></div>
                    <p class="text-sm font-medium text-gray-800">N8N Workflows</p>
                    <p class="text-xs text-gray-500">Port 5678</p>
                </div>
            </div>
        </div>

        <!-- Quick Links -->
        <div class="bg-white rounded-xl shadow-lg p-6">
            <h3 class="text-xl font-semibold text-gray-800 mb-4">Quick Links</h3>
            <div class="grid md:grid-cols-2 gap-4">
                <a href="http://localhost:8000/docs" target="_blank" class="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="font-medium text-gray-800">API Documentation</p>
                        <p class="text-sm text-gray-500">Interactive Swagger docs</p>
                    </div>
                </a>
                <a href="http://localhost:5678" target="_blank" class="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                        <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="font-medium text-gray-800">N8N Workflows</p>
                        <p class="text-sm text-gray-500">Automation platform</p>
                    </div>
                </a>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8 mt-12">
        <div class="container mx-auto px-6 text-center">
            <p class="text-gray-400">© 2025 Zurich Workflow System - Local Development Environment</p>
        </div>
    </footer>
</body>
</html>
