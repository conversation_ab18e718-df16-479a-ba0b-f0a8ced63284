# Zurich Insurance Claim Upload Page

A secure document upload interface for insurance claimants to submit required documentation.

## Features

- **Zurich Insurance Branding** - Matches email design and corporate styling
- **Secure File Upload** - Direct upload to Supabase storage
- **File Validation** - Size limits, type checking, and error handling
- **Progress Tracking** - Real-time upload progress with visual feedback
- **Webhook Integration** - Automatic notification to N8N workflow
- **Responsive Design** - Works on desktop and mobile devices

## Setup Instructions

### 1. Configure Supabase

Update the configuration in `app.js`:

```javascript
const SUPABASE_CONFIG = {
    url: 'https://your-project.supabase.co',
    anon<PERSON>ey: 'your-anon-key',
    bucketName: 'claims-attachments',
    // ... other settings
};
```

### 2. Create Supabase Storage Bucket

1. Go to your Supabase dashboard
2. Navigate to Storage
3. Create a new bucket named `claims-attachments`
4. Set appropriate permissions for file uploads

### 3. URL Parameters

The page expects these query parameters:

- `claimId` - The claim reference number
- `webhookUrl` - Base64 encoded webhook URL for notifications

Example URL:
```
https://your-domain.com/claim-upload/?claimId=CLM-2025-001&webhookUrl=aHR0cHM6Ly9uOG4tZXhhbXBsZS5jb20vd2ViaG9vay8xMjM=
```

### 4. N8N Integration

In your N8N workflow, generate the upload URL like this:

```javascript
const claimId = "CLM-2025-001";
const webhookUrl = $execution.resumeUrl;
const encodedWebhookUrl = Buffer.from(webhookUrl).toString('base64');
const uploadUrl = `https://your-domain.com/claim-upload/?claimId=${claimId}&webhookUrl=${encodedWebhookUrl}`;
```

## File Upload Process

1. **File Selection** - Users select documents (PDF, JPG, PNG, DOC, DOCX)
2. **Validation** - Files are checked for size (max 10MB) and type
3. **Upload** - Files are uploaded to `claims-attachments/claim/{claim-id}/`
4. **Notification** - Webhook is triggered with upload details
5. **Confirmation** - User sees success message

## Webhook Payload

When files are uploaded, the webhook receives:

```json
{
  "claimId": "CLM-2025-001",
  "uploadedFiles": [
    {
      "name": "document.pdf",
      "originalName": "document.pdf",
      "storedName": "1641234567890_document.pdf",
      "path": "claim/CLM-2025-001/1641234567890_document.pdf",
      "size": 1024000,
      "type": "application/pdf",
      "uploadedAt": "2025-01-03T10:30:00.000Z"
    }
  ],
  "uploadTimestamp": "2025-01-03T10:30:00.000Z",
  "status": "completed",
  "totalFiles": 1,
  "totalSize": 1024000
}
```

## Security Features

- **File Type Validation** - Only allowed file types accepted
- **Size Limits** - Maximum 10MB per file
- **Secure Storage** - Files stored in Supabase with proper permissions
- **Sanitized Filenames** - Special characters removed from filenames
- **Error Handling** - Comprehensive error messages and recovery

## Styling

The page uses Zurich Insurance corporate colors and styling:
- Primary Blue: #005AAF
- Success Green: #28a745
- Warning Yellow: #ffc107
- Error Red: #dc3545

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Deployment

1. Place files in your web server directory
2. Update Supabase configuration
3. Test with sample claim ID and webhook URL
4. Integrate with your N8N workflows

## Troubleshooting

### Upload Fails
- Check Supabase credentials and bucket permissions
- Verify file size and type restrictions
- Check browser console for detailed error messages

### Webhook Not Triggered
- Verify webhook URL is properly base64 encoded
- Check N8N workflow is running and accessible
- Review network requests in browser developer tools

### Styling Issues
- Ensure `style.css` is properly linked
- Check for CSS conflicts with other stylesheets
- Verify responsive design on different screen sizes
