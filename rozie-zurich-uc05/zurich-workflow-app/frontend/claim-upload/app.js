// Zurich Insurance Claim Upload Application

// Configuration - Update these with your actual Supabase credentials
const SUPABASE_CONFIG = {
    url: process.env.SUPABASE_URL || 'https://your-supabase-url.supabase.co',
    anonKey: process.env.SUPABASE_ANON_KEY || 'your-supabase-anon-key',
    bucketName: 'claims-attachments',
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
};

// Initialize Supabase client
const supabase = window.supabase.createClient(SUPABASE_CONFIG.url, SUPABASE_CONFIG.anonKey);

// Global variables
let claimId = '';
let webhookUrl = '';

// Initialize page when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
});

/**
 * Initialize the page with URL parameters and setup
 */
function initializePage() {
    // Get URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    claimId = urlParams.get('claimId') || 'Unknown';
    const encodedWebhookUrl = urlParams.get('webhookUrl') || '';
    
    // Decode webhook URL from base64
    try {
        webhookUrl = atob(encodedWebhookUrl);
        console.log('Webhook URL decoded successfully');
    } catch (e) {
        console.error('Error decoding webhook URL:', e);
        webhookUrl = '';
    }
    
    // Update UI with claim ID
    document.getElementById('claimId').textContent = claimId;
    document.getElementById('claimIdFooter').textContent = claimId;
    
    // Set current date
    const currentDate = new Date().toLocaleString('en-US', {
        year: 'numeric',
        month: 'long',
        day: '2-digit'
    });
    document.getElementById('uploadDate').textContent = currentDate;
    
    // Setup file input validation
    setupFileInputValidation();
}

/**
 * Setup file input validation and preview
 */
function setupFileInputValidation() {
    const fileInput = document.getElementById('fileInput');

    fileInput.addEventListener('change', function(e) {
        const files = e.target.files;
        if (files.length > 0) {
            displaySelectedFiles(files);
            validateSelectedFiles(files);
        } else {
            hideSelectedFiles();
        }
    });
}

/**
 * Display selected files with preview
 */
function displaySelectedFiles(files) {
    const selectedFilesDiv = document.getElementById('selectedFiles');
    const filesList = document.getElementById('filesList');
    const totalSizeSpan = document.getElementById('totalSize');

    // Clear previous list
    filesList.innerHTML = '';

    let totalSize = 0;

    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        totalSize += file.size;

        const li = document.createElement('li');
        li.innerHTML = `
            <div>
                <span class="file-name">${file.name}</span>
                <span class="file-type">${getFileExtension(file.name)}</span>
            </div>
            <div>
                <span class="file-size">${formatFileSize(file.size)}</span>
                <button class="remove-file" onclick="removeFile(${i})" title="Remove file">×</button>
            </div>
        `;
        filesList.appendChild(li);
    }

    totalSizeSpan.textContent = formatFileSize(totalSize);
    selectedFilesDiv.style.display = 'block';
}

/**
 * Hide selected files preview
 */
function hideSelectedFiles() {
    const selectedFilesDiv = document.getElementById('selectedFiles');
    selectedFilesDiv.style.display = 'none';
}

/**
 * Remove a file from selection
 */
function removeFile(index) {
    const fileInput = document.getElementById('fileInput');
    const files = Array.from(fileInput.files);

    // Create new FileList without the removed file
    const dt = new DataTransfer();
    files.forEach((file, i) => {
        if (i !== index) {
            dt.items.add(file);
        }
    });

    fileInput.files = dt.files;

    // Update display
    if (fileInput.files.length > 0) {
        displaySelectedFiles(fileInput.files);
        validateSelectedFiles(fileInput.files);
    } else {
        hideSelectedFiles();
    }
}

/**
 * Get file extension for display
 */
function getFileExtension(filename) {
    return filename.split('.').pop().toUpperCase();
}

/**
 * Validate selected files before upload
 */
function validateSelectedFiles(files) {
    const errors = [];
    
    for (let file of files) {
        // Check file size
        if (file.size > SUPABASE_CONFIG.maxFileSize) {
            errors.push(`"${file.name}" is too large (${formatFileSize(file.size)}). Maximum size is 10MB.`);
        }
        
        // Check file type
        if (!SUPABASE_CONFIG.allowedTypes.includes(file.type)) {
            errors.push(`"${file.name}" is not a supported file type.`);
        }
    }
    
    if (errors.length > 0) {
        showStatus(errors.join('<br>'), 'error');
        return false;
    }
    
    // Clear any previous errors
    hideStatus();
    return true;
}

/**
 * Main upload function
 */
async function uploadFiles() {
    const fileInput = document.getElementById('fileInput');
    const files = fileInput.files;
    
    if (files.length === 0) {
        showStatus('Please select at least one file to upload.', 'error');
        return;
    }
    
    // Validate files
    if (!validateSelectedFiles(files)) {
        return;
    }
    
    // Disable upload button
    const uploadBtn = document.getElementById('uploadBtn');
    uploadBtn.disabled = true;
    uploadBtn.textContent = 'Uploading...';
    
    // Show progress
    showProgress(true);
    updateProgress(0, 'Starting upload...');
    
    try {
        const uploadedFiles = [];
        
        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            const fileProgress = (i / files.length) * 90; // Base progress for this file
            const nextFileProgress = ((i + 1) / files.length) * 90; // Progress when this file completes

            updateProgress(fileProgress, `Uploading file ${i + 1} of ${files.length}: ${file.name}...`);

            // Generate unique filename with timestamp
            const timestamp = Date.now() + i; // Add index to ensure uniqueness
            const sanitizedFileName = sanitizeFileName(file.name);
            const fileName = `${timestamp}_${sanitizedFileName}`;
            const filePath = `claim/${claimId}/${fileName}`;

            try {
                // Upload to Supabase Storage
                const { error } = await supabase.storage
                    .from(SUPABASE_CONFIG.bucketName)
                    .upload(filePath, file, {
                        cacheControl: '3600',
                        upsert: false
                    });

                if (error) {
                    throw new Error(`Failed to upload ${file.name}: ${error.message}`);
                }

                uploadedFiles.push({
                    name: file.name,
                    originalName: file.name,
                    storedName: fileName,
                    path: filePath,
                    size: file.size,
                    type: file.type,
                    uploadedAt: new Date().toISOString()
                });

                // Update progress for completed file
                updateProgress(nextFileProgress, `Uploaded ${file.name} successfully`);

            } catch (fileError) {
                // If one file fails, continue with others but log the error
                console.error(`Failed to upload ${file.name}:`, fileError);
                updateProgress(nextFileProgress, `Failed to upload ${file.name}, continuing with remaining files...`);

                // Add to failed files list
                uploadedFiles.push({
                    name: file.name,
                    originalName: file.name,
                    error: fileError.message,
                    status: 'failed',
                    uploadedAt: new Date().toISOString()
                });
            }
        }
        
        // Trigger webhook notification
        updateProgress(95, 'Notifying claims team...');
        await triggerWebhook(uploadedFiles);
        
        // Complete upload
        updateProgress(100, 'Upload completed successfully!');
        
        setTimeout(() => {
            showProgress(false);
            showSuccessMessage();
            resetUploadForm();
        }, 1000);
        
    } catch (error) {
        console.error('Upload error:', error);
        showProgress(false);
        showStatus(`Upload failed: ${error.message}`, 'error');
        
        // Re-enable upload button
        uploadBtn.disabled = false;
        uploadBtn.textContent = 'Upload Documents';
    }
}

/**
 * Trigger webhook to notify N8N workflow
 */
async function triggerWebhook(uploadedFiles) {
    if (!webhookUrl) {
        console.warn('No webhook URL provided - skipping notification');
        return;
    }
    
    try {
        // Separate successful and failed uploads
        const successfulUploads = uploadedFiles.filter(file => !file.error);
        const failedUploads = uploadedFiles.filter(file => file.error);

        const payload = {
            claimId: claimId,
            uploadedFiles: successfulUploads,
            failedFiles: failedUploads,
            uploadTimestamp: new Date().toISOString(),
            status: failedUploads.length === 0 ? 'completed' : 'partial',
            totalFiles: uploadedFiles.length,
            successfulFiles: successfulUploads.length,
            failedFiles: failedUploads.length,
            totalSize: successfulUploads.reduce((sum, file) => sum + (file.size || 0), 0)
        };
        
        const response = await fetch(webhookUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(payload)
        });
        
        if (!response.ok) {
            throw new Error(`Webhook failed with status: ${response.status}`);
        }
        
        console.log('Webhook triggered successfully');
        
    } catch (error) {
        console.error('Webhook error:', error);
        // Don't fail the entire upload if webhook fails
        // The files are still uploaded successfully
    }
}

/**
 * Utility Functions
 */

function showProgress(show) {
    const container = document.getElementById('progressContainer');
    container.style.display = show ? 'block' : 'none';
}

function updateProgress(percent, text) {
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    
    progressBar.style.width = Math.min(percent, 100) + '%';
    progressText.textContent = text;
}

function showStatus(message, type) {
    const statusDiv = document.getElementById('uploadStatus');
    statusDiv.style.display = 'block';
    statusDiv.innerHTML = message;
    statusDiv.className = `upload-status ${type}`;
}

function hideStatus() {
    const statusDiv = document.getElementById('uploadStatus');
    statusDiv.style.display = 'none';
}

function showSuccessMessage() {
    document.getElementById('successMessage').style.display = 'block';
    
    // Hide upload controls
    document.getElementById('uploadBtn').style.display = 'none';
    document.getElementById('fileInput').style.display = 'none';
    document.querySelector('.file-info').style.display = 'none';
}

function resetUploadForm() {
    const fileInput = document.getElementById('fileInput');
    fileInput.value = '';
    
    const uploadBtn = document.getElementById('uploadBtn');
    uploadBtn.disabled = false;
    uploadBtn.textContent = 'Upload Documents';
}

function sanitizeFileName(fileName) {
    // Remove special characters and spaces, keep only alphanumeric, dots, hyphens, underscores
    return fileName.replace(/[^a-zA-Z0-9.-_]/g, '_');
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
