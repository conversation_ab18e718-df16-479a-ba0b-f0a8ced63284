/* Zurich Insurance Claim Upload Styling */
body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    max-width: 600px;
    margin: 0 auto;
    background-color: #f4f4f4;
    padding: 20px;
}

.container {
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Header */
.header {
    text-align: center;
    border-bottom: 3px solid #005AAF;
    padding-bottom: 20px;
    margin-bottom: 30px;
}

.header h1 {
    color: #005AAF;
    margin: 0;
    font-size: 28px;
}

.header p {
    color: #666;
    margin: 5px 0 0 0;
    font-size: 16px;
}

/* Main Content */
.main-content h2 {
    color: #005AAF;
    margin: 0 0 20px 0;
}

/* Claim Details Table */
.claim-details {
    margin: 25px 0;
}

.claim-details table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid #ddd;
    border-radius: 5px;
    overflow: hidden;
}

.claim-details td {
    padding: 8px 15px;
    border-bottom: 1px solid #eee;
}

.claim-details .label {
    font-weight: bold;
    background-color: #f8f9fa;
}

.claim-details .value {
    background-color: #f8f9fa;
}

/* Upload Section */
.upload-section {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
}

.upload-section h3 {
    color: #005AAF;
    margin: 0 0 15px 0;
}

.file-input-container {
    margin: 15px 0;
}

#fileInput {
    width: 100%;
    padding: 10px;
    border: 2px dashed #005AAF;
    border-radius: 5px;
    background-color: white;
    cursor: pointer;
}

.file-info {
    margin: 10px 0 0 0;
    font-size: 14px;
    color: #666;
}

/* Selected Files Preview */
.selected-files {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 15px;
    margin: 15px 0;
}

.selected-files h4 {
    color: #005AAF;
    margin: 0 0 10px 0;
    font-size: 16px;
}

.selected-files ul {
    list-style: none;
    padding: 0;
    margin: 0 0 10px 0;
}

.selected-files li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.selected-files li:last-child {
    border-bottom: none;
}

.file-name {
    font-weight: bold;
    color: #333;
}

.file-size {
    color: #666;
    font-size: 12px;
}

.file-type {
    background-color: #005AAF;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    text-transform: uppercase;
}

.total-size {
    font-weight: bold;
    color: #005AAF;
    margin: 10px 0 0 0;
    text-align: right;
}

.remove-file {
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 3px;
    padding: 2px 6px;
    font-size: 10px;
    cursor: pointer;
}

.remove-file:hover {
    background-color: #c82333;
}

.upload-button-container {
    text-align: center;
    margin: 20px 0;
}

.upload-button {
    background-color: #005AAF;
    color: white;
    padding: 12px 30px;
    border: none;
    border-radius: 5px;
    font-weight: bold;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

.upload-button:hover {
    background-color: #004080;
}

.upload-button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

/* Progress Bar */
.progress-container {
    margin: 15px 0;
}

.progress-bar-bg {
    background-color: #e0e0e0;
    border-radius: 10px;
    overflow: hidden;
}

.progress-bar {
    background-color: #28a745;
    height: 20px;
    width: 0%;
    transition: width 0.3s;
}

.progress-text {
    text-align: center;
    margin: 10px 0 0 0;
    font-size: 14px;
}

/* Upload Status */
.upload-status {
    margin: 15px 0;
    padding: 15px;
    border-radius: 5px;
    border: 1px solid;
}

.upload-status.error {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.upload-status.success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

/* Success Message */
.success-message {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
}

.success-message h3 {
    color: #155724;
    margin: 0 0 15px 0;
    display: flex;
    align-items: center;
}

.success-icon {
    background-color: #28a745;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    font-weight: bold;
}

.success-message p {
    margin: 0;
    color: #155724;
}

/* Important Notes */
.important-notes {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
}

.important-notes h3 {
    color: #856404;
    margin: 0 0 15px 0;
}

.important-notes ul {
    margin: 0;
    padding-left: 20px;
    color: #856404;
}

.important-notes li {
    margin-bottom: 8px;
}

/* Contact Information */
.contact-info {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin: 25px 0;
}

.contact-info h3 {
    color: #005AAF;
    margin: 0 0 10px 0;
}

.contact-info p {
    margin: 0 0 10px 0;
}

/* Footer */
.footer {
    border-top: 1px solid #eee;
    padding-top: 20px;
    margin-top: 30px;
    text-align: center;
    color: #666;
    font-size: 14px;
}

.footer p {
    margin: 5px 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }
    
    .container {
        padding: 20px;
    }
    
    .header h1 {
        font-size: 24px;
    }
    
    .upload-button {
        padding: 10px 20px;
        font-size: 14px;
    }
}
