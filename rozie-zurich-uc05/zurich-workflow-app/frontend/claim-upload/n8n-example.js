// N8N Workflow Example for Claim Upload Integration
// This code shows how to generate the upload URL in your N8N workflow

// Example 1: Generate Upload URL in N8N
function generateUploadUrl() {
    // Get claim ID from previous node or set manually
    const claimId = $('Gmail Trigger').first().json.claimId || 'CLM-2025-001';
    
    // Get the webhook resume URL from N8N
    const webhookUrl = $execution.resumeUrl;
    
    // Encode webhook URL to base64 for URL parameter
    const encodedWebhookUrl = Buffer.from(webhookUrl).toString('base64');
    
    // Your frontend domain (update this to your actual domain)
    const frontendDomain = 'https://zurich-workflow.dev-scc-demo.rozie.ai';
    
    // Generate the complete upload URL
    const uploadUrl = `${frontendDomain}/claim-upload/?claimId=${claimId}&webhookUrl=${encodedWebhookUrl}`;
    
    return {
        claimId: claimId,
        uploadUrl: uploadUrl,
        webhookUrl: webhookUrl,
        encodedWebhookUrl: encodedWebhookUrl
    };
}

// Example 2: Email Template with Upload Link
function generateEmailWithUploadLink() {
    const uploadData = generateUploadUrl();
    
    const emailTemplate = `
    <div style="margin: 15px 0;">
        <p style="margin: 0 0 10px 0;"><strong>Option 2: Online Portal</strong></p>
        <div style="text-align: center; padding-left: 15px;">
            <a href="${uploadData.uploadUrl}" 
               style="background-color: #005AAF; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">
               Upload Documents
            </a>
        </div>
    </div>
    `;
    
    return {
        ...uploadData,
        emailTemplate: emailTemplate
    };
}

// Example 3: Handle Webhook Response (when files are uploaded)
function handleUploadWebhook() {
    // This function processes the webhook data when files are uploaded
    const webhookData = $input.all()[0].json;
    
    const response = {
        claimId: webhookData.claimId,
        uploadStatus: webhookData.status,
        filesUploaded: webhookData.totalFiles,
        totalSize: webhookData.totalSize,
        uploadTimestamp: webhookData.uploadTimestamp,
        files: webhookData.uploadedFiles.map(file => ({
            name: file.originalName,
            storedPath: file.path,
            size: file.size,
            type: file.type
        }))
    };
    
    // You can now:
    // 1. Update claim status in database
    // 2. Send confirmation email to claimant
    // 3. Notify claims team
    // 4. Process uploaded documents with OCR
    
    return response;
}

// Example 4: Complete N8N Workflow Structure
const workflowExample = {
    "nodes": [
        {
            "name": "Gmail Trigger",
            "type": "Gmail Trigger",
            "description": "Receives claim emails"
        },
        {
            "name": "Extract Claim Info",
            "type": "Code",
            "description": "Extract claim ID and details from email"
        },
        {
            "name": "Generate Upload URL",
            "type": "Code", 
            "description": "Create upload URL with webhook",
            "code": "return generateUploadUrl();"
        },
        {
            "name": "Send Upload Email",
            "type": "Gmail",
            "description": "Send email with upload link to claimant"
        },
        {
            "name": "Wait for Upload",
            "type": "Webhook",
            "description": "Wait for file upload completion"
        },
        {
            "name": "Process Uploaded Files",
            "type": "Code",
            "description": "Handle uploaded files",
            "code": "return handleUploadWebhook();"
        },
        {
            "name": "Update Claim Status",
            "type": "HTTP Request",
            "description": "Update claim in database"
        },
        {
            "name": "Send Confirmation",
            "type": "Gmail",
            "description": "Send confirmation to claimant"
        }
    ]
};

// Example 5: Configuration for Different Environments
const environmentConfig = {
    development: {
        frontendDomain: 'http://localhost:3000',
        supabaseUrl: 'https://dev-project.supabase.co'
    },
    staging: {
        frontendDomain: 'https://zurich-workflow-staging.dev-scc-demo.rozie.ai',
        supabaseUrl: 'https://staging-project.supabase.co'
    },
    production: {
        frontendDomain: 'https://zurich-workflow.dev-scc-demo.rozie.ai',
        supabaseUrl: 'https://prod-project.supabase.co'
    }
};

// Export functions for use in N8N
module.exports = {
    generateUploadUrl,
    generateEmailWithUploadLink,
    handleUploadWebhook,
    workflowExample,
    environmentConfig
};
