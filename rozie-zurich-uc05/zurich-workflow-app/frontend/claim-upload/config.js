// Supabase Configuration for Claim Upload
// Update these values with your actual Supabase project details

window.CLAIM_UPLOAD_CONFIG = {
    // Supabase Settings
    supabase: {
        url: 'https://tlduggpohclrgxbvuzhd.supabase.co',
        anon<PERSON><PERSON>: 'https://tlduggpohclrgxbvuzhd.supabase.co'
    },
    
    // Upload Settings
    upload: {
        bucketName: 'claims-attachments',
        maxFileSize: 10 * 1024 * 1024, // 10MB
        allowAllTypes: true, // Accept all file types
        allowedTypes: [], // Empty array means all types allowed
        allowedExtensions: [] // Empty array means all extensions allowed
    },
    
    // UI Settings
    ui: {
        showFilePreview: true,
        allowFileRemoval: true,
        showProgressDetails: true
    }
};
