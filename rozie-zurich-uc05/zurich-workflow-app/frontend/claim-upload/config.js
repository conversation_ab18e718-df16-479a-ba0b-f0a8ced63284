// Supabase Configuration for Claim Upload
// Update these values with your actual Supabase project details

window.CLAIM_UPLOAD_CONFIG = {
    // Supabase Settings
    supabase: {
        url: 'https://your-supabase-url.supabase.co',
        anon<PERSON><PERSON>: 'your-supabase-anon-key'
    },
    
    // Upload Settings
    upload: {
        bucketName: 'claims-attachments',
        maxFileSize: 10 * 1024 * 1024, // 10MB
        allowedTypes: [
            'application/pdf',
            'image/jpeg',
            'image/jpg', 
            'image/png',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ],
        allowedExtensions: ['.pdf', '.jpg', '.jpeg', '.png', '.doc', '.docx']
    },
    
    // UI Settings
    ui: {
        showFilePreview: true,
        allowFileRemoval: true,
        showProgressDetails: true
    }
};
