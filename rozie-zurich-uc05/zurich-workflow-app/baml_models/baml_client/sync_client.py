###############################################################################
#
#  Welcome to Baml! To use this generated code, please run the following:
#
#  $ pip install baml-py
#
###############################################################################

# This file was generated by BAML: please do not edit it. Instead, edit the
# BAML files and re-generate this code.
#
# ruff: noqa: E501,F401
# flake8: noqa: E501,F401
# pylint: disable=unused-import,line-too-long
# fmt: off
from typing import Dict, List, Optional, TypeVar, Union, cast
from typing_extensions import Literal

import baml_py

from . import _baml
from ._baml import BamlCallOptions
from .types import Checked, Check
from .parser import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LlmStreamParser
from .sync_request import HttpRequest, HttpStreamRequest
from .globals import DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_CTX, DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_RUNTIME


OutputType = TypeVar('OutputType')


class BamlSyncClient:
    __runtime: baml_py.BamlRuntime
    __ctx_manager: baml_py.BamlCtxManager
    __stream_client: "BamlStreamClient"
    __http_request: HttpRequest
    __http_stream_request: HttpStreamRequest
    __llm_response_parser: LlmResponseParser
    __llm_stream_parser: LlmStreamParser
    __baml_options: _baml.BamlCallOptions

    def __init__(self, runtime: baml_py.BamlRuntime, ctx_manager: baml_py.BamlCtxManager, baml_options: Optional[_baml.BamlCallOptions] = None):
      self.__runtime = runtime
      self.__ctx_manager = ctx_manager
      self.__stream_client = BamlStreamClient(self.__runtime, self.__ctx_manager, baml_options)
      self.__http_request = HttpRequest(self.__runtime, self.__ctx_manager)
      self.__http_stream_request = HttpStreamRequest(self.__runtime, self.__ctx_manager)
      self.__llm_response_parser = LlmResponseParser(self.__runtime, self.__ctx_manager)
      self.__llm_stream_parser = LlmStreamParser(self.__runtime, self.__ctx_manager)
      self.__baml_options = baml_options or {}

    @property
    def stream(self):
      return self.__stream_client

    @property
    def request(self):
      return self.__http_request

    @property
    def stream_request(self):
      return self.__http_stream_request

    @property
    def parse(self):
      return self.__llm_response_parser

    @property
    def parse_stream(self):
      return self.__llm_stream_parser

    def with_options(
      self,
      tb: Optional[_baml.type_builder.TypeBuilder] = None,
      client_registry: Optional[baml_py.baml_py.ClientRegistry] = None,
      collector: Optional[Union[baml_py.baml_py.Collector, List[baml_py.baml_py.Collector]]] = None,
      env: Optional[Dict[str, str]] = None,
    ) -> "BamlSyncClient":
      """
      Returns a new instance of BamlSyncClient with explicitly typed baml options
      for Python 3.8 compatibility.
      """
      new_options: _baml.BamlCallOptions = self.__baml_options.copy()

      # Override if any keyword arguments were provided.
      if tb is not None:
          new_options["tb"] = tb
      if client_registry is not None:
          new_options["client_registry"] = client_registry
      if collector is not None:
          new_options["collector"] = collector
      if env is not None:
          new_options["env"] = env
      return BamlSyncClient(self.__runtime, self.__ctx_manager, new_options)

    
    def AnalyzeClaimLevel01(
        self,
        claimInput: _baml.types.ClaimDocumentInput,
        baml_options: _baml.BamlCallOptions = {},
    ) -> _baml.types.Level01Analysis:
      options: _baml.BamlCallOptions = {**self.__baml_options, **(baml_options or {})}
      __tb__ = options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = options.get("client_registry", None)
      collector = options.get("collector", None)
      collectors = collector if isinstance(collector, list) else [collector] if collector is not None else []
      env = _baml.env_vars_to_dict(options.get("env", {}))
      raw = self.__runtime.call_function_sync(
        "AnalyzeClaimLevel01",
        {
          "claimInput": claimInput,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        collectors,
        env,
      )
      return cast(_baml.types.Level01Analysis, raw.cast_to(_baml.types, _baml.types, _baml.partial_types, False))
    
    def AnalyzeCoverageLevel02(
        self,
        claimId: str,claimType: str,policyNumber: str,incidentDate: str,primaryCause: str,level01Confidence: float,level01ExitPath: str,keyFindings: List[str],policyDocuments: str,additionalEvidence: str,canadianJurisdiction: str,
        baml_options: _baml.BamlCallOptions = {},
    ) -> _baml.types.Level02CoverageAnalysis:
      options: _baml.BamlCallOptions = {**self.__baml_options, **(baml_options or {})}
      __tb__ = options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = options.get("client_registry", None)
      collector = options.get("collector", None)
      collectors = collector if isinstance(collector, list) else [collector] if collector is not None else []
      env = _baml.env_vars_to_dict(options.get("env", {}))
      raw = self.__runtime.call_function_sync(
        "AnalyzeCoverageLevel02",
        {
          "claimId": claimId,"claimType": claimType,"policyNumber": policyNumber,"incidentDate": incidentDate,"primaryCause": primaryCause,"level01Confidence": level01Confidence,"level01ExitPath": level01ExitPath,"keyFindings": keyFindings,"policyDocuments": policyDocuments,"additionalEvidence": additionalEvidence,"canadianJurisdiction": canadianJurisdiction,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        collectors,
        env,
      )
      return cast(_baml.types.Level02CoverageAnalysis, raw.cast_to(_baml.types, _baml.types, _baml.partial_types, False))
    
    def AssessDataCompleteness(
        self,
        claimId: str,claimType: str,level01Analysis: str,claimDetails: str,ocrTexts: List[str],attachmentsList: List[str],additionalContext: str,
        baml_options: _baml.BamlCallOptions = {},
    ) -> _baml.types.DataCompletenessAssessment:
      options: _baml.BamlCallOptions = {**self.__baml_options, **(baml_options or {})}
      __tb__ = options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = options.get("client_registry", None)
      collector = options.get("collector", None)
      collectors = collector if isinstance(collector, list) else [collector] if collector is not None else []
      env = _baml.env_vars_to_dict(options.get("env", {}))
      raw = self.__runtime.call_function_sync(
        "AssessDataCompleteness",
        {
          "claimId": claimId,"claimType": claimType,"level01Analysis": level01Analysis,"claimDetails": claimDetails,"ocrTexts": ocrTexts,"attachmentsList": attachmentsList,"additionalContext": additionalContext,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        collectors,
        env,
      )
      return cast(_baml.types.DataCompletenessAssessment, raw.cast_to(_baml.types, _baml.types, _baml.partial_types, False))
    
    def AssessPriorityAndRisk(
        self,
        claimId: str,claimType: str,coverageDecision: str,coverageConfidence: float,level01Analysis: str,claimDetails: str,policyInformation: str,dataCompleteness: float,additionalContext: str,
        baml_options: _baml.BamlCallOptions = {},
    ) -> _baml.types.PriorityRiskAssessment:
      options: _baml.BamlCallOptions = {**self.__baml_options, **(baml_options or {})}
      __tb__ = options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = options.get("client_registry", None)
      collector = options.get("collector", None)
      collectors = collector if isinstance(collector, list) else [collector] if collector is not None else []
      env = _baml.env_vars_to_dict(options.get("env", {}))
      raw = self.__runtime.call_function_sync(
        "AssessPriorityAndRisk",
        {
          "claimId": claimId,"claimType": claimType,"coverageDecision": coverageDecision,"coverageConfidence": coverageConfidence,"level01Analysis": level01Analysis,"claimDetails": claimDetails,"policyInformation": policyInformation,"dataCompleteness": dataCompleteness,"additionalContext": additionalContext,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        collectors,
        env,
      )
      return cast(_baml.types.PriorityRiskAssessment, raw.cast_to(_baml.types, _baml.types, _baml.partial_types, False))
    
    def ClassifyZurichEmail(
        self,
        emailData: _baml.types.EmailForClassification,
        baml_options: _baml.BamlCallOptions = {},
    ) -> _baml.types.ZurichEmailClassificationResult:
      options: _baml.BamlCallOptions = {**self.__baml_options, **(baml_options or {})}
      __tb__ = options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = options.get("client_registry", None)
      collector = options.get("collector", None)
      collectors = collector if isinstance(collector, list) else [collector] if collector is not None else []
      env = _baml.env_vars_to_dict(options.get("env", {}))
      raw = self.__runtime.call_function_sync(
        "ClassifyZurichEmail",
        {
          "emailData": emailData,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        collectors,
        env,
      )
      return cast(_baml.types.ZurichEmailClassificationResult, raw.cast_to(_baml.types, _baml.types, _baml.partial_types, False))
    
    def ClassifyZurichEmailFallback(
        self,
        emailData: _baml.types.EmailForClassification,
        baml_options: _baml.BamlCallOptions = {},
    ) -> _baml.types.ZurichEmailClassificationResult:
      options: _baml.BamlCallOptions = {**self.__baml_options, **(baml_options or {})}
      __tb__ = options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = options.get("client_registry", None)
      collector = options.get("collector", None)
      collectors = collector if isinstance(collector, list) else [collector] if collector is not None else []
      env = _baml.env_vars_to_dict(options.get("env", {}))
      raw = self.__runtime.call_function_sync(
        "ClassifyZurichEmailFallback",
        {
          "emailData": emailData,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        collectors,
        env,
      )
      return cast(_baml.types.ZurichEmailClassificationResult, raw.cast_to(_baml.types, _baml.types, _baml.partial_types, False))
    
    def EnhanceWithSparkNLP(
        self,
        emailContent: str,attachmentsText: List[str],
        baml_options: _baml.BamlCallOptions = {},
    ) -> _baml.types.SparkNlpEnhancedData:
      options: _baml.BamlCallOptions = {**self.__baml_options, **(baml_options or {})}
      __tb__ = options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = options.get("client_registry", None)
      collector = options.get("collector", None)
      collectors = collector if isinstance(collector, list) else [collector] if collector is not None else []
      env = _baml.env_vars_to_dict(options.get("env", {}))
      raw = self.__runtime.call_function_sync(
        "EnhanceWithSparkNLP",
        {
          "emailContent": emailContent,"attachmentsText": attachmentsText,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        collectors,
        env,
      )
      return cast(_baml.types.SparkNlpEnhancedData, raw.cast_to(_baml.types, _baml.types, _baml.partial_types, False))
    
    def ExtractLevel03FaultFactors(
        self,
        input: _baml.types.Level03AnalysisInput,
        baml_options: _baml.BamlCallOptions = {},
    ) -> _baml.types.LiabilityFactorExtraction:
      options: _baml.BamlCallOptions = {**self.__baml_options, **(baml_options or {})}
      __tb__ = options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = options.get("client_registry", None)
      collector = options.get("collector", None)
      collectors = collector if isinstance(collector, list) else [collector] if collector is not None else []
      env = _baml.env_vars_to_dict(options.get("env", {}))
      raw = self.__runtime.call_function_sync(
        "ExtractLevel03FaultFactors",
        {
          "input": input,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        collectors,
        env,
      )
      return cast(_baml.types.LiabilityFactorExtraction, raw.cast_to(_baml.types, _baml.types, _baml.partial_types, False))
    
    def ExtractLevel04QuantumDetails(
        self,
        input: _baml.types.Level04AnalysisInput,
        baml_options: _baml.BamlCallOptions = {},
    ) -> _baml.types.QuantumDamageExtraction:
      options: _baml.BamlCallOptions = {**self.__baml_options, **(baml_options or {})}
      __tb__ = options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = options.get("client_registry", None)
      collector = options.get("collector", None)
      collectors = collector if isinstance(collector, list) else [collector] if collector is not None else []
      env = _baml.env_vars_to_dict(options.get("env", {}))
      raw = self.__runtime.call_function_sync(
        "ExtractLevel04QuantumDetails",
        {
          "input": input,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        collectors,
        env,
      )
      return cast(_baml.types.QuantumDamageExtraction, raw.cast_to(_baml.types, _baml.types, _baml.partial_types, False))
    
    def ExtractResume(
        self,
        resume: str,
        baml_options: _baml.BamlCallOptions = {},
    ) -> _baml.types.Resume:
      options: _baml.BamlCallOptions = {**self.__baml_options, **(baml_options or {})}
      __tb__ = options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = options.get("client_registry", None)
      collector = options.get("collector", None)
      collectors = collector if isinstance(collector, list) else [collector] if collector is not None else []
      env = _baml.env_vars_to_dict(options.get("env", {}))
      raw = self.__runtime.call_function_sync(
        "ExtractResume",
        {
          "resume": resume,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        collectors,
        env,
      )
      return cast(_baml.types.Resume, raw.cast_to(_baml.types, _baml.types, _baml.partial_types, False))
    
    def GenerateDocumentHighlights(
        self,
        document_text: str,document_filename: str,claim_reference: str,email_subject: str,email_body: str,level01_analysis: str,level02_analysis: str,level03_analysis: str,level04_analysis: str,ocr_texts: List[str],
        baml_options: _baml.BamlCallOptions = {},
    ) -> List[_baml.types.ColorLegend]:
      options: _baml.BamlCallOptions = {**self.__baml_options, **(baml_options or {})}
      __tb__ = options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = options.get("client_registry", None)
      collector = options.get("collector", None)
      collectors = collector if isinstance(collector, list) else [collector] if collector is not None else []
      env = _baml.env_vars_to_dict(options.get("env", {}))
      raw = self.__runtime.call_function_sync(
        "GenerateDocumentHighlights",
        {
          "document_text": document_text,"document_filename": document_filename,"claim_reference": claim_reference,"email_subject": email_subject,"email_body": email_body,"level01_analysis": level01_analysis,"level02_analysis": level02_analysis,"level03_analysis": level03_analysis,"level04_analysis": level04_analysis,"ocr_texts": ocr_texts,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        collectors,
        env,
      )
      return cast(List[_baml.types.ColorLegend], raw.cast_to(_baml.types, _baml.types, _baml.partial_types, False))
    
    def GenerateMultiDocumentHighlights(
        self,
        documents: List[str],filenames: List[str],claim_reference: str,email_subject: str,email_body: str,level01_analysis: str,level02_analysis: str,level03_analysis: str,level04_analysis: str,
        baml_options: _baml.BamlCallOptions = {},
    ) -> List[_baml.types.ColorLegend]:
      options: _baml.BamlCallOptions = {**self.__baml_options, **(baml_options or {})}
      __tb__ = options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = options.get("client_registry", None)
      collector = options.get("collector", None)
      collectors = collector if isinstance(collector, list) else [collector] if collector is not None else []
      env = _baml.env_vars_to_dict(options.get("env", {}))
      raw = self.__runtime.call_function_sync(
        "GenerateMultiDocumentHighlights",
        {
          "documents": documents,"filenames": filenames,"claim_reference": claim_reference,"email_subject": email_subject,"email_body": email_body,"level01_analysis": level01_analysis,"level02_analysis": level02_analysis,"level03_analysis": level03_analysis,"level04_analysis": level04_analysis,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        collectors,
        env,
      )
      return cast(List[_baml.types.ColorLegend], raw.cast_to(_baml.types, _baml.types, _baml.partial_types, False))
    
    def ResearchCanadianLegalPrecedents(
        self,
        claimType: str,causeOfLoss: str,policyLanguage: str,jurisdiction: str,
        baml_options: _baml.BamlCallOptions = {},
    ) -> _baml.types.CanadianLegalAnalysis:
      options: _baml.BamlCallOptions = {**self.__baml_options, **(baml_options or {})}
      __tb__ = options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = options.get("client_registry", None)
      collector = options.get("collector", None)
      collectors = collector if isinstance(collector, list) else [collector] if collector is not None else []
      env = _baml.env_vars_to_dict(options.get("env", {}))
      raw = self.__runtime.call_function_sync(
        "ResearchCanadianLegalPrecedents",
        {
          "claimType": claimType,"causeOfLoss": causeOfLoss,"policyLanguage": policyLanguage,"jurisdiction": jurisdiction,
        },
        self.__ctx_manager.get(),
        tb,
        __cr__,
        collectors,
        env,
      )
      return cast(_baml.types.CanadianLegalAnalysis, raw.cast_to(_baml.types, _baml.types, _baml.partial_types, False))
    



class BamlStreamClient:
    __runtime: baml_py.BamlRuntime
    __ctx_manager: baml_py.BamlCtxManager
    __baml_options: _baml.BamlCallOptions
    def __init__(self, runtime: baml_py.BamlRuntime, ctx_manager: baml_py.BamlCtxManager, baml_options: Optional[_baml.BamlCallOptions] = None):
      self.__runtime = runtime
      self.__ctx_manager = ctx_manager
      self.__baml_options = baml_options or {}

    
    def AnalyzeClaimLevel01(
        self,
        claimInput: _baml.types.ClaimDocumentInput,
        baml_options: _baml.BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[_baml.partial_types.Level01Analysis, _baml.types.Level01Analysis]:
      options: _baml.BamlCallOptions = {**self.__baml_options, **(baml_options or {})}
      __tb__ = options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = options.get("client_registry", None)
      collector = options.get("collector", None)
      collectors = collector if isinstance(collector, list) else [collector] if collector is not None else []
      env = _baml.env_vars_to_dict(options.get("env", {}))
      raw = self.__runtime.stream_function_sync(
        "AnalyzeClaimLevel01",
        {
          "claimInput": claimInput,
        },
        None,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        collectors,
        env,
      )

      return baml_py.BamlSyncStream[_baml.partial_types.Level01Analysis, _baml.types.Level01Analysis](
        raw,
        lambda x: cast(_baml.partial_types.Level01Analysis, x.cast_to(_baml.types, _baml.types, _baml.partial_types, True)),
        lambda x: cast(_baml.types.Level01Analysis, x.cast_to(_baml.types, _baml.types, _baml.partial_types, False)),
        self.__ctx_manager.get(),
      )
    
    def AnalyzeCoverageLevel02(
        self,
        claimId: str,claimType: str,policyNumber: str,incidentDate: str,primaryCause: str,level01Confidence: float,level01ExitPath: str,keyFindings: List[str],policyDocuments: str,additionalEvidence: str,canadianJurisdiction: str,
        baml_options: _baml.BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[_baml.partial_types.Level02CoverageAnalysis, _baml.types.Level02CoverageAnalysis]:
      options: _baml.BamlCallOptions = {**self.__baml_options, **(baml_options or {})}
      __tb__ = options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = options.get("client_registry", None)
      collector = options.get("collector", None)
      collectors = collector if isinstance(collector, list) else [collector] if collector is not None else []
      env = _baml.env_vars_to_dict(options.get("env", {}))
      raw = self.__runtime.stream_function_sync(
        "AnalyzeCoverageLevel02",
        {
          "claimId": claimId,
          "claimType": claimType,
          "policyNumber": policyNumber,
          "incidentDate": incidentDate,
          "primaryCause": primaryCause,
          "level01Confidence": level01Confidence,
          "level01ExitPath": level01ExitPath,
          "keyFindings": keyFindings,
          "policyDocuments": policyDocuments,
          "additionalEvidence": additionalEvidence,
          "canadianJurisdiction": canadianJurisdiction,
        },
        None,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        collectors,
        env,
      )

      return baml_py.BamlSyncStream[_baml.partial_types.Level02CoverageAnalysis, _baml.types.Level02CoverageAnalysis](
        raw,
        lambda x: cast(_baml.partial_types.Level02CoverageAnalysis, x.cast_to(_baml.types, _baml.types, _baml.partial_types, True)),
        lambda x: cast(_baml.types.Level02CoverageAnalysis, x.cast_to(_baml.types, _baml.types, _baml.partial_types, False)),
        self.__ctx_manager.get(),
      )
    
    def AssessDataCompleteness(
        self,
        claimId: str,claimType: str,level01Analysis: str,claimDetails: str,ocrTexts: List[str],attachmentsList: List[str],additionalContext: str,
        baml_options: _baml.BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[_baml.partial_types.DataCompletenessAssessment, _baml.types.DataCompletenessAssessment]:
      options: _baml.BamlCallOptions = {**self.__baml_options, **(baml_options or {})}
      __tb__ = options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = options.get("client_registry", None)
      collector = options.get("collector", None)
      collectors = collector if isinstance(collector, list) else [collector] if collector is not None else []
      env = _baml.env_vars_to_dict(options.get("env", {}))
      raw = self.__runtime.stream_function_sync(
        "AssessDataCompleteness",
        {
          "claimId": claimId,
          "claimType": claimType,
          "level01Analysis": level01Analysis,
          "claimDetails": claimDetails,
          "ocrTexts": ocrTexts,
          "attachmentsList": attachmentsList,
          "additionalContext": additionalContext,
        },
        None,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        collectors,
        env,
      )

      return baml_py.BamlSyncStream[_baml.partial_types.DataCompletenessAssessment, _baml.types.DataCompletenessAssessment](
        raw,
        lambda x: cast(_baml.partial_types.DataCompletenessAssessment, x.cast_to(_baml.types, _baml.types, _baml.partial_types, True)),
        lambda x: cast(_baml.types.DataCompletenessAssessment, x.cast_to(_baml.types, _baml.types, _baml.partial_types, False)),
        self.__ctx_manager.get(),
      )
    
    def AssessPriorityAndRisk(
        self,
        claimId: str,claimType: str,coverageDecision: str,coverageConfidence: float,level01Analysis: str,claimDetails: str,policyInformation: str,dataCompleteness: float,additionalContext: str,
        baml_options: _baml.BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[_baml.partial_types.PriorityRiskAssessment, _baml.types.PriorityRiskAssessment]:
      options: _baml.BamlCallOptions = {**self.__baml_options, **(baml_options or {})}
      __tb__ = options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = options.get("client_registry", None)
      collector = options.get("collector", None)
      collectors = collector if isinstance(collector, list) else [collector] if collector is not None else []
      env = _baml.env_vars_to_dict(options.get("env", {}))
      raw = self.__runtime.stream_function_sync(
        "AssessPriorityAndRisk",
        {
          "claimId": claimId,
          "claimType": claimType,
          "coverageDecision": coverageDecision,
          "coverageConfidence": coverageConfidence,
          "level01Analysis": level01Analysis,
          "claimDetails": claimDetails,
          "policyInformation": policyInformation,
          "dataCompleteness": dataCompleteness,
          "additionalContext": additionalContext,
        },
        None,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        collectors,
        env,
      )

      return baml_py.BamlSyncStream[_baml.partial_types.PriorityRiskAssessment, _baml.types.PriorityRiskAssessment](
        raw,
        lambda x: cast(_baml.partial_types.PriorityRiskAssessment, x.cast_to(_baml.types, _baml.types, _baml.partial_types, True)),
        lambda x: cast(_baml.types.PriorityRiskAssessment, x.cast_to(_baml.types, _baml.types, _baml.partial_types, False)),
        self.__ctx_manager.get(),
      )
    
    def ClassifyZurichEmail(
        self,
        emailData: _baml.types.EmailForClassification,
        baml_options: _baml.BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[_baml.partial_types.ZurichEmailClassificationResult, _baml.types.ZurichEmailClassificationResult]:
      options: _baml.BamlCallOptions = {**self.__baml_options, **(baml_options or {})}
      __tb__ = options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = options.get("client_registry", None)
      collector = options.get("collector", None)
      collectors = collector if isinstance(collector, list) else [collector] if collector is not None else []
      env = _baml.env_vars_to_dict(options.get("env", {}))
      raw = self.__runtime.stream_function_sync(
        "ClassifyZurichEmail",
        {
          "emailData": emailData,
        },
        None,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        collectors,
        env,
      )

      return baml_py.BamlSyncStream[_baml.partial_types.ZurichEmailClassificationResult, _baml.types.ZurichEmailClassificationResult](
        raw,
        lambda x: cast(_baml.partial_types.ZurichEmailClassificationResult, x.cast_to(_baml.types, _baml.types, _baml.partial_types, True)),
        lambda x: cast(_baml.types.ZurichEmailClassificationResult, x.cast_to(_baml.types, _baml.types, _baml.partial_types, False)),
        self.__ctx_manager.get(),
      )
    
    def ClassifyZurichEmailFallback(
        self,
        emailData: _baml.types.EmailForClassification,
        baml_options: _baml.BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[_baml.partial_types.ZurichEmailClassificationResult, _baml.types.ZurichEmailClassificationResult]:
      options: _baml.BamlCallOptions = {**self.__baml_options, **(baml_options or {})}
      __tb__ = options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = options.get("client_registry", None)
      collector = options.get("collector", None)
      collectors = collector if isinstance(collector, list) else [collector] if collector is not None else []
      env = _baml.env_vars_to_dict(options.get("env", {}))
      raw = self.__runtime.stream_function_sync(
        "ClassifyZurichEmailFallback",
        {
          "emailData": emailData,
        },
        None,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        collectors,
        env,
      )

      return baml_py.BamlSyncStream[_baml.partial_types.ZurichEmailClassificationResult, _baml.types.ZurichEmailClassificationResult](
        raw,
        lambda x: cast(_baml.partial_types.ZurichEmailClassificationResult, x.cast_to(_baml.types, _baml.types, _baml.partial_types, True)),
        lambda x: cast(_baml.types.ZurichEmailClassificationResult, x.cast_to(_baml.types, _baml.types, _baml.partial_types, False)),
        self.__ctx_manager.get(),
      )
    
    def EnhanceWithSparkNLP(
        self,
        emailContent: str,attachmentsText: List[str],
        baml_options: _baml.BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[_baml.partial_types.SparkNlpEnhancedData, _baml.types.SparkNlpEnhancedData]:
      options: _baml.BamlCallOptions = {**self.__baml_options, **(baml_options or {})}
      __tb__ = options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = options.get("client_registry", None)
      collector = options.get("collector", None)
      collectors = collector if isinstance(collector, list) else [collector] if collector is not None else []
      env = _baml.env_vars_to_dict(options.get("env", {}))
      raw = self.__runtime.stream_function_sync(
        "EnhanceWithSparkNLP",
        {
          "emailContent": emailContent,
          "attachmentsText": attachmentsText,
        },
        None,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        collectors,
        env,
      )

      return baml_py.BamlSyncStream[_baml.partial_types.SparkNlpEnhancedData, _baml.types.SparkNlpEnhancedData](
        raw,
        lambda x: cast(_baml.partial_types.SparkNlpEnhancedData, x.cast_to(_baml.types, _baml.types, _baml.partial_types, True)),
        lambda x: cast(_baml.types.SparkNlpEnhancedData, x.cast_to(_baml.types, _baml.types, _baml.partial_types, False)),
        self.__ctx_manager.get(),
      )
    
    def ExtractLevel03FaultFactors(
        self,
        input: _baml.types.Level03AnalysisInput,
        baml_options: _baml.BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[_baml.partial_types.LiabilityFactorExtraction, _baml.types.LiabilityFactorExtraction]:
      options: _baml.BamlCallOptions = {**self.__baml_options, **(baml_options or {})}
      __tb__ = options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = options.get("client_registry", None)
      collector = options.get("collector", None)
      collectors = collector if isinstance(collector, list) else [collector] if collector is not None else []
      env = _baml.env_vars_to_dict(options.get("env", {}))
      raw = self.__runtime.stream_function_sync(
        "ExtractLevel03FaultFactors",
        {
          "input": input,
        },
        None,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        collectors,
        env,
      )

      return baml_py.BamlSyncStream[_baml.partial_types.LiabilityFactorExtraction, _baml.types.LiabilityFactorExtraction](
        raw,
        lambda x: cast(_baml.partial_types.LiabilityFactorExtraction, x.cast_to(_baml.types, _baml.types, _baml.partial_types, True)),
        lambda x: cast(_baml.types.LiabilityFactorExtraction, x.cast_to(_baml.types, _baml.types, _baml.partial_types, False)),
        self.__ctx_manager.get(),
      )
    
    def ExtractLevel04QuantumDetails(
        self,
        input: _baml.types.Level04AnalysisInput,
        baml_options: _baml.BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[_baml.partial_types.QuantumDamageExtraction, _baml.types.QuantumDamageExtraction]:
      options: _baml.BamlCallOptions = {**self.__baml_options, **(baml_options or {})}
      __tb__ = options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = options.get("client_registry", None)
      collector = options.get("collector", None)
      collectors = collector if isinstance(collector, list) else [collector] if collector is not None else []
      env = _baml.env_vars_to_dict(options.get("env", {}))
      raw = self.__runtime.stream_function_sync(
        "ExtractLevel04QuantumDetails",
        {
          "input": input,
        },
        None,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        collectors,
        env,
      )

      return baml_py.BamlSyncStream[_baml.partial_types.QuantumDamageExtraction, _baml.types.QuantumDamageExtraction](
        raw,
        lambda x: cast(_baml.partial_types.QuantumDamageExtraction, x.cast_to(_baml.types, _baml.types, _baml.partial_types, True)),
        lambda x: cast(_baml.types.QuantumDamageExtraction, x.cast_to(_baml.types, _baml.types, _baml.partial_types, False)),
        self.__ctx_manager.get(),
      )
    
    def ExtractResume(
        self,
        resume: str,
        baml_options: _baml.BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[_baml.partial_types.Resume, _baml.types.Resume]:
      options: _baml.BamlCallOptions = {**self.__baml_options, **(baml_options or {})}
      __tb__ = options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = options.get("client_registry", None)
      collector = options.get("collector", None)
      collectors = collector if isinstance(collector, list) else [collector] if collector is not None else []
      env = _baml.env_vars_to_dict(options.get("env", {}))
      raw = self.__runtime.stream_function_sync(
        "ExtractResume",
        {
          "resume": resume,
        },
        None,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        collectors,
        env,
      )

      return baml_py.BamlSyncStream[_baml.partial_types.Resume, _baml.types.Resume](
        raw,
        lambda x: cast(_baml.partial_types.Resume, x.cast_to(_baml.types, _baml.types, _baml.partial_types, True)),
        lambda x: cast(_baml.types.Resume, x.cast_to(_baml.types, _baml.types, _baml.partial_types, False)),
        self.__ctx_manager.get(),
      )
    
    def GenerateDocumentHighlights(
        self,
        document_text: str,document_filename: str,claim_reference: str,email_subject: str,email_body: str,level01_analysis: str,level02_analysis: str,level03_analysis: str,level04_analysis: str,ocr_texts: List[str],
        baml_options: _baml.BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[List[_baml.partial_types.ColorLegend], List[_baml.types.ColorLegend]]:
      options: _baml.BamlCallOptions = {**self.__baml_options, **(baml_options or {})}
      __tb__ = options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = options.get("client_registry", None)
      collector = options.get("collector", None)
      collectors = collector if isinstance(collector, list) else [collector] if collector is not None else []
      env = _baml.env_vars_to_dict(options.get("env", {}))
      raw = self.__runtime.stream_function_sync(
        "GenerateDocumentHighlights",
        {
          "document_text": document_text,
          "document_filename": document_filename,
          "claim_reference": claim_reference,
          "email_subject": email_subject,
          "email_body": email_body,
          "level01_analysis": level01_analysis,
          "level02_analysis": level02_analysis,
          "level03_analysis": level03_analysis,
          "level04_analysis": level04_analysis,
          "ocr_texts": ocr_texts,
        },
        None,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        collectors,
        env,
      )

      return baml_py.BamlSyncStream[List[_baml.partial_types.ColorLegend], List[_baml.types.ColorLegend]](
        raw,
        lambda x: cast(List[_baml.partial_types.ColorLegend], x.cast_to(_baml.types, _baml.types, _baml.partial_types, True)),
        lambda x: cast(List[_baml.types.ColorLegend], x.cast_to(_baml.types, _baml.types, _baml.partial_types, False)),
        self.__ctx_manager.get(),
      )
    
    def GenerateMultiDocumentHighlights(
        self,
        documents: List[str],filenames: List[str],claim_reference: str,email_subject: str,email_body: str,level01_analysis: str,level02_analysis: str,level03_analysis: str,level04_analysis: str,
        baml_options: _baml.BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[List[_baml.partial_types.ColorLegend], List[_baml.types.ColorLegend]]:
      options: _baml.BamlCallOptions = {**self.__baml_options, **(baml_options or {})}
      __tb__ = options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = options.get("client_registry", None)
      collector = options.get("collector", None)
      collectors = collector if isinstance(collector, list) else [collector] if collector is not None else []
      env = _baml.env_vars_to_dict(options.get("env", {}))
      raw = self.__runtime.stream_function_sync(
        "GenerateMultiDocumentHighlights",
        {
          "documents": documents,
          "filenames": filenames,
          "claim_reference": claim_reference,
          "email_subject": email_subject,
          "email_body": email_body,
          "level01_analysis": level01_analysis,
          "level02_analysis": level02_analysis,
          "level03_analysis": level03_analysis,
          "level04_analysis": level04_analysis,
        },
        None,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        collectors,
        env,
      )

      return baml_py.BamlSyncStream[List[_baml.partial_types.ColorLegend], List[_baml.types.ColorLegend]](
        raw,
        lambda x: cast(List[_baml.partial_types.ColorLegend], x.cast_to(_baml.types, _baml.types, _baml.partial_types, True)),
        lambda x: cast(List[_baml.types.ColorLegend], x.cast_to(_baml.types, _baml.types, _baml.partial_types, False)),
        self.__ctx_manager.get(),
      )
    
    def ResearchCanadianLegalPrecedents(
        self,
        claimType: str,causeOfLoss: str,policyLanguage: str,jurisdiction: str,
        baml_options: _baml.BamlCallOptions = {},
    ) -> baml_py.BamlSyncStream[_baml.partial_types.CanadianLegalAnalysis, _baml.types.CanadianLegalAnalysis]:
      options: _baml.BamlCallOptions = {**self.__baml_options, **(baml_options or {})}
      __tb__ = options.get("tb", None)
      if __tb__ is not None:
        tb = __tb__._tb # type: ignore (we know how to use this private attribute)
      else:
        tb = None
      __cr__ = options.get("client_registry", None)
      collector = options.get("collector", None)
      collectors = collector if isinstance(collector, list) else [collector] if collector is not None else []
      env = _baml.env_vars_to_dict(options.get("env", {}))
      raw = self.__runtime.stream_function_sync(
        "ResearchCanadianLegalPrecedents",
        {
          "claimType": claimType,
          "causeOfLoss": causeOfLoss,
          "policyLanguage": policyLanguage,
          "jurisdiction": jurisdiction,
        },
        None,
        self.__ctx_manager.get(),
        tb,
        __cr__,
        collectors,
        env,
      )

      return baml_py.BamlSyncStream[_baml.partial_types.CanadianLegalAnalysis, _baml.types.CanadianLegalAnalysis](
        raw,
        lambda x: cast(_baml.partial_types.CanadianLegalAnalysis, x.cast_to(_baml.types, _baml.types, _baml.partial_types, True)),
        lambda x: cast(_baml.types.CanadianLegalAnalysis, x.cast_to(_baml.types, _baml.types, _baml.partial_types, False)),
        self.__ctx_manager.get(),
      )
    


b = BamlSyncClient(DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_RUNTIME, DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_CTX)

__all__ = ["b", "BamlCallOptions"]