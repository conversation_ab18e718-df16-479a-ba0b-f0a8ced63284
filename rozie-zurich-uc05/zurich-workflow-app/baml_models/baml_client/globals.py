###############################################################################
#
#  Welcome to Baml! To use this generated code, please run the following:
#
#  $ pip install baml-py
#
###############################################################################

# This file was generated by BAML: please do not edit it. Instead, edit the
# BAML files and re-generate this code.
#
# ruff: noqa: E501,F401
# flake8: noqa: E501,F401
# pylint: disable=unused-import,line-too-long
# fmt: off
from __future__ import annotations
import os
import warnings

from baml_py import BamlCtxManager, BamlRuntime
from baml_py.baml_py import BamlError
from .inlinedbaml import get_baml_files
from typing import Dict, Any

DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_RUNTIME = BamlRuntime.from_files(
  "baml_src",
  get_baml_files(),
  os.environ.copy()
)
DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_CTX = BamlCtxManager(DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_RUNTIME)

def reset_baml_env_vars(env_vars: Dict[str, str]):
    warnings.warn(
        "reset_baml_env_vars is deprecated and is safe to remove, since environment variables are now lazily loaded on each function call",
        DeprecationWarning,
        stacklevel=2
    )
    if DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_CTX.allow_reset():
        DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_RUNTIME.reset(
            "baml_src",
            get_baml_files(),
            env_vars
        )
        DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_CTX.reset()
    else:
        raise BamlError("Cannot reset BAML environment variables while there are active BAML contexts.")

__all__ = []