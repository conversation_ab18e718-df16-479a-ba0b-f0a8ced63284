// ================================================================================================
// ZURICH EMAIL CLASSIFICATION - BAML IMPLEMENTATION
// ================================================================================================
// Purpose: Classify incoming emails to determine if they are claims-related
// Models: OpenAI GPT-4o (primary), GPT-4o-mini (fallback)
// Integration: N8N workflow automation
// ================================================================================================

// ================================================================================================
// CORE CLASSIFICATION SCHEMAS
// ================================================================================================

enum ClaimType {
  LIABILITY @description("General liability claims")
  PROPERTY @description("Property damage claims") 
  AUTO @description("Automotive insurance claims")
  MEDICAL_MALPRACTICE @description("Medical malpractice claims")
  PRODUCT_LIABILITY @description("Product liability claims")
  ENVIRONMENTAL @description("Environmental damage claims")
  WORKERS_COMPENSATION @description("Workers compensation claims")
  PROFESSIONAL_LIABILITY @description("Professional liability claims")
  CYBER_LIABILITY @description("Cyber security and data breach claims")
  NOT_CLAIM @description("Email is not insurance claim related")
}

enum CanadianProvince {
  ALBERTA @description("Alberta, Canada")
  BRITISH_COLUMBIA @description("British Columbia, Canada")
  MANITOBA @description("Manitoba, Canada")
  NEW_BRUNSWICK @description("New Brunswick, Canada")
  NEWFOUNDLAND_LABRADOR @description("Newfoundland and Labrador, Canada")
  NORTHWEST_TERRITORIES @description("Northwest Territories, Canada")
  NOVA_SCOTIA @description("Nova Scotia, Canada")
  NUNAVUT @description("Nunavut, Canada")
  ONTARIO @description("Ontario, Canada")
  PRINCE_EDWARD_ISLAND @description("Prince Edward Island, Canada")
  QUEBEC @description("Quebec, Canada")
  SASKATCHEWAN @description("Saskatchewan, Canada")
  YUKON @description("Yukon, Canada")
  UNKNOWN @description("Province cannot be determined from email")
}

enum UrgencyLevel {
  CRITICAL @description("Requires immediate attention - catastrophic incident")
  HIGH @description("High priority - significant damages or injuries")
  MEDIUM @description("Standard priority - routine claim processing")
  LOW @description("Low priority - minor incidents or inquiries")
}

enum AttachmentMismatchType {
  NO_MISMATCH @description("Attachments mentioned and provided, or neither mentioned nor provided")
  MENTIONS_BUT_MISSING @description("Email mentions attachments/documents but none are provided")
  PROVIDED_BUT_NOT_MENTIONED @description("Attachments provided but not mentioned in email text")
}

// ================================================================================================
// ATTACHMENT ANALYSIS SCHEMAS  
// ================================================================================================

class AttachmentInfo {
  filename string @description("Name of the attachment file")
  fileType string @description("File extension or MIME type")
  isClaimRelated bool @description("Whether filename suggests claim-related content")
  documentType string @description("Inferred document type from filename")
}

class AttachmentAnalysis {
  hasAttachments bool @description("Whether any attachments are provided")
  attachmentCount int @description("Total number of attachments")
  attachments AttachmentInfo[] @description("Details of each attachment")
  mentionsAttachments bool @description("Whether email text references attachments")
  attachmentMismatch AttachmentMismatchType @description("Mismatch between mentioned and provided attachments")
  suspiciousFilenames bool @description("Whether any filenames appear suspicious or non-business")
}

// ================================================================================================
// CLAIM INDICATORS SCHEMAS
// ================================================================================================

class ClaimIndicators {
  incidentKeywords string[] @description("Keywords suggesting an incident occurred")
  damageKeywords string[] @description("Keywords suggesting damage or loss")
  legalKeywords string[] @description("Keywords suggesting legal implications")
  medicalKeywords string[] @description("Keywords suggesting medical issues")
  timeIndicators string[] @description("Time-related phrases indicating when incident occurred")
  locationIndicators string[] @description("Location-related phrases indicating where incident occurred")
  partyIndicators string[] @description("References to other parties involved")
}

class RiskAssessment {
  liabilityRisk "low" | "medium" | "high" | "critical"
  financialRisk "low" | "medium" | "high" | "critical" 
  reputationalRisk "low" | "medium" | "high" | "critical"
  regulatoryRisk "low" | "medium" | "high" | "critical"
  overallRiskScore float @description("Overall risk score from 0.0 to 1.0")
}

// ================================================================================================
// MAIN CLASSIFICATION RESULT SCHEMA
// ================================================================================================

class ZurichEmailClassificationResult {
  // Primary Classification Decision
  isClaimRelated bool @description("PRIMARY DECISION: Whether this email is insurance claim related")
  claimType ClaimType @description("Type of claim if claim-related")
  workflowAction "PROCEED_TO_ZENDESK" | "IGNORE_EMAIL" | "REQUEST_ATTACHMENTS" | "HUMAN_REVIEW_REQUIRED"
  
  // Confidence and Analysis
  confidenceScore float @description("Confidence level from 0.0 to 1.0")
  urgencyLevel UrgencyLevel @description("Urgency level for processing priority")
  
  // Canadian Context
  canadianJurisdiction CanadianProvince @description("Relevant Canadian province/territory")
  
  // Attachment Analysis
  attachmentAnalysis AttachmentAnalysis @description("Comprehensive attachment analysis")
  
  // Claim Analysis (if applicable)
  claimIndicators ClaimIndicators? @description("Indicators that suggest this is a claim")
  riskAssessment RiskAssessment? @description("Risk assessment if claim-related")
  
  // Reasoning and Next Steps  
  classificationReasoning string @description("Detailed explanation of classification decision")
  recommendedNextSteps string[] @description("Recommended actions based on classification")
  flagsForHumanReview string[] @description("Specific items requiring human attention")
  
  // Metadata
  processingNotes string[] @description("Technical notes about the classification process")
  canadianLegalConsiderations string[] @description("Relevant Canadian legal factors identified")
}

// ================================================================================================
// INPUT SCHEMAS FOR N8N INTEGRATION
// ================================================================================================

class EmailAttachment {
  filename string @description("Name of the attachment file")
  contentType string @description("MIME type of the attachment")
  size int? @description("File size in bytes if available")
  // Note: Binary content not processed directly in this classification
}

class EmailForClassification {
  subject string @description("Email subject line")
  body string @description("Email body content (plain text or HTML)")
  senderEmail string @description("Sender's email address")
  senderName string? @description("Sender's display name if available")
  receivedDate string? @description("When email was received (ISO format)")
  attachments EmailAttachment[] @description("List of email attachments")
}

// ================================================================================================
// MAIN CLASSIFICATION FUNCTION
// ================================================================================================

function ClassifyZurichEmail(
  emailData: EmailForClassification
) -> ZurichEmailClassificationResult {
  client "openai/gpt-4o"
  prompt #"
    You are Zurich Insurance's expert email classification system for Canadian claims processing.
    
    CRITICAL TASK: Determine if this email is insurance claim-related and requires processing.
    
    EMAIL TO ANALYZE:
    Subject: {{ emailData.subject }}
    From: {{ emailData.senderEmail }} ({{ emailData.senderName }})
    Body: {{ emailData.body }}
    Attachments: {{ emailData.attachments }}
    
    CLASSIFICATION CRITERIA:
    
    1. CLAIM-RELATED INDICATORS:
       - Incident descriptions (accidents, damage, injuries, theft, fire, water damage)
       - Insurance terminology (policy, claim, coverage, deductible, premium)
       - Legal language (liability, negligence, fault, damages, settlement)
       - Loss descriptions (property damage, bodily injury, financial loss)
       - Time/location of incidents
       - References to other parties, witnesses, police reports
       - Medical terminology (if injury claims)
       
    2. ATTACHMENT ANALYSIS:
       - Check if email mentions attachments/documents but none provided
       - Analyze attachment filenames for claim relevance
       - Flag suspicious or non-business attachments
       
    3. CANADIAN CONTEXT:
       - Identify relevant Canadian province/territory
       - Consider provincial insurance regulations
       - Flag Quebec-specific considerations (civil law vs common law)
       
    4. URGENCY ASSESSMENT:
       - CRITICAL: Catastrophic incidents, fatalities, major property damage
       - HIGH: Significant injuries, substantial property damage, legal threats
       - MEDIUM: Standard claims, routine incidents
       - LOW: Minor incidents, information requests
       
    5. WORKFLOW DECISIONS:
       - PROCEED_TO_ZENDESK: Clear claim requiring processing
       - IGNORE_EMAIL: Not claim-related (spam, marketing, general inquiries)
       - REQUEST_ATTACHMENTS: Claims-related but mentions missing attachments
       - HUMAN_REVIEW_REQUIRED: Unclear, complex, or high-risk situations
    
    6. RISK FACTORS TO CONSIDER:
       - Multiple parties involved
       - Potential litigation
       - Regulatory compliance issues
       - High financial exposure
       - Media attention potential
       - Previous claim history mentions
       
    SPECIAL EXIT POINTS:
    - If email mentions "see attached", "please find attached", "attachment", "document" but NO attachments provided -> workflowAction: "REQUEST_ATTACHMENTS"
    - If suspicious files or potential fraud indicators -> workflowAction: "HUMAN_REVIEW_REQUIRED"
    - If extremely high value or complex legal issues -> workflowAction: "HUMAN_REVIEW_REQUIRED"
    
    CANADIAN LEGAL CONSIDERATIONS:
    - Provincial jurisdiction differences
    - Statute of limitations variations
    - No-fault insurance provinces (Ontario, Quebec, etc.)
    - Tort vs no-fault systems
    - Regulatory requirements by province
    
    Provide comprehensive analysis with clear reasoning for your classification decision.
    Focus on accuracy for Canadian insurance claims processing workflow.
    
    {{ ctx.output_format }}
  "#
}

// ================================================================================================
// FALLBACK FUNCTION WITH GPT-4O-MINI
// ================================================================================================

function ClassifyZurichEmailFallback(
  emailData: EmailForClassification
) -> ZurichEmailClassificationResult {
  client "openai/gpt-4o-mini"
  prompt #"
    You are a backup email classifier for Zurich Insurance Canada.
    
    Analyze this email and determine if it's an insurance claim:
    
    Subject: {{ emailData.subject }}
    From: {{ emailData.senderEmail }}
    Body: {{ emailData.body }}
    Attachments: {{ emailData.attachments }}
    
    Key decisions to make:
    1. Is this claim-related? (look for incidents, damage, injuries, losses)
    2. What type of claim? (auto, property, liability, etc.)
    3. What's the urgency? (critical, high, medium, low)
    4. Are attachments mentioned but missing?
    5. Which Canadian province applies?
    
    Special cases:
    - If mentions attachments but none provided -> REQUEST_ATTACHMENTS
    - If unclear or high-risk -> HUMAN_REVIEW_REQUIRED
    - If clearly not a claim -> IGNORE_EMAIL
    - If valid claim -> PROCEED_TO_ZENDESK
    
    {{ ctx.output_format }}
  "#
}

// ================================================================================================
// TEST CASES (COMMENTED OUT FOR BAML PLAYGROUND TESTING)
// ================================================================================================

// // TEST CASE 1: Clear Auto Claim
// test AutoClaimTest {
//   functions [ClassifyZurichEmail]
//   args {
//     emailData {
//       subject: "Car Accident - Need to File Claim - Policy #AC123456"
//       body: "Hi, I was in a car accident yesterday on Highway 401 in Toronto. The other driver ran a red light and hit my vehicle. I have photos and a police report. My car has significant front-end damage. Please let me know what documents you need to process my claim."
//       senderEmail: "<EMAIL>"
//       senderName: "John Doe"
//       attachments: [
//         {filename: "accident_photos.zip", contentType: "application/zip", size: 2048000},
//         {filename: "police_report.pdf", contentType: "application/pdf", size: 512000}
//       ]
//     }
//   }
// }

// // TEST CASE 2: Mentions Attachments But None Provided
// test MissingAttachmentsTest {
//   functions [ClassifyZurichEmail]
//   args {
//     emailData {
//       subject: "Property Damage Claim"
//       body: "Dear Zurich, my basement flooded last week due to a burst pipe. I have photos and repair estimates attached. Please see attached documents for damage assessment."
//       senderEmail: "<EMAIL>"
//       senderName: "Jane Smith"
//       attachments: []
//     }
//   }
// }

// // TEST CASE 3: Not Claim Related
// test NotClaimTest {
//   functions [ClassifyZurichEmail]
//   args {
//     emailData {
//       subject: "Marketing Newsletter Subscription"
//       body: "Hi, I would like to subscribe to your newsletter for insurance tips and updates. Please add me to your mailing list."
//       senderEmail: "<EMAIL>"
//       senderName: "Marketing Team"
//       attachments: []
//     }
//   }
// }

// // TEST CASE 4: High-Risk Liability Claim
// test HighRiskLiabilityTest {
//   functions [ClassifyZurichEmail]
//   args {
//     emailData {
//       subject: "URGENT: Serious Injury at Construction Site"
//       body: "This is to notify you of a serious workplace accident at our construction site in Calgary, Alberta. A worker fell from scaffolding and has been hospitalized with critical injuries. Legal counsel has been engaged. This incident may result in significant liability exposure."
//       senderEmail: "<EMAIL>"
//       senderName: "Legal Department"
//       attachments: [
//         {filename: "incident_report.pdf", contentType: "application/pdf", size: 1024000},
//         {filename: "witness_statements.docx", contentType: "application/vnd.openxmlformats-officedocument.wordprocessingml.document", size: 256000}
//       ]
//     }
//   }
// }

// // TEST CASE 5: Quebec Civil Law Consideration
// test QuebecClaimTest {
//   functions [ClassifyZurichEmail]
//   args {
//     emailData {
//       subject: "Réclamation - Dommages à la propriété"
//       body: "Bonjour, je souhaite déclarer un sinistre pour des dommages causés par la grêle à ma propriété à Montréal, Québec. Les dommages incluent le toit, les fenêtres et la voiture."
//       senderEmail: "<EMAIL>"
//       senderName: "Claude Martin"
//       attachments: [
//         {filename: "photos_dommages.jpg", contentType: "image/jpeg", size: 3072000}
//       ]
//     }
//   }
// } 