// ================================================================================================
// ZURICH LEVEL 02 COVERAGE ANALYSIS - BAML IMPLEMENTATION  
// ================================================================================================
// Purpose: Comprehensive coverage determination with intelligent exit path routing
// Integration: Follows Level 01 Analysis -> Level 02 Coverage -> Settlement/Denial workflow
// Enhancement: Includes CanLII legal precedent lookup for Canadian insurance law
// ================================================================================================

// ================================================================================================
// CORE COVERAGE DECISION ENUMS
// ================================================================================================

enum CoverageDecision {
  NOT_COVERED @description("Loss is not covered under policy terms - denial with detailed justification")
  COVERED @description("Loss is covered under policy terms - proceed to settlement analysis")
  INFORMATION_REQUIRED @description("Insufficient information to determine coverage - request specific data")
}

enum InformationSource {
  CLAIMANT @description("Information must be obtained directly from the claimant")
  THIRD_PARTY @description("Information must be obtained from external third parties")
  INSURANCE_AGENT @description("Information can be obtained by insurance agent/adjuster")
  EXPERT_ASSESSMENT @description("Requires independent expert evaluation")
  LEGAL_COUNSEL @description("Requires legal counsel interpretation")
  REGULATORY_AUTHORITY @description("Requires input from regulatory authorities")
}

enum CoverageReason {
  POLICY_TERMS_CLEAR @description("Policy terms clearly cover this type of loss")
  LEGAL_PRECEDENT @description("Canadian legal precedent supports coverage")
  REGULATORY_REQUIREMENT @description("Provincial regulations mandate coverage")
  STANDARD_INTERPRETATION @description("Standard industry interpretation supports coverage")
  EXCLUSION_APPLIES @description("Policy exclusion clearly applies to this loss")
  OUTSIDE_POLICY_PERIOD @description("Loss occurred outside policy period")
  CONDITION_NOT_MET @description("Policy condition not satisfied")
  INSUFFICIENT_EVIDENCE @description("Insufficient evidence to determine coverage")
}

enum RiskLevel {
  LOW @description("Low risk coverage decision with clear policy language")
  MEDIUM @description("Medium risk decision requiring careful documentation")
  HIGH @description("High risk decision requiring human expert review")
  CRITICAL @description("Critical risk decision requiring legal counsel review")
}

// ================================================================================================
// CANADIAN LEGAL INTEGRATION SCHEMAS
// ================================================================================================

class CanLIILegalPrecedent {
  caseId string @description("CanLII case identifier")
  caseName string @description("Name of the legal case")
  court string @description("Court that decided the case")
  jurisdiction string @description("Canadian jurisdiction (province/federal)")
  decisionDate string @description("Date of court decision")
  relevanceToCase string @description("How this precedent applies to current claim")
  keyPrinciple string @description("Key legal principle established")
  supportsCoverage bool @description("Whether precedent supports or denies coverage")
  confidenceLevel float @description("Confidence in precedent applicability (0.0-1.0)")
  canliiUrl string @description("Direct URL to CanLII case")
}

class CanadianLegalAnalysis {
  applicableLaw string @description("Primary Canadian law governing this claim type")
  provincialRegulations string[] @description("Relevant provincial insurance regulations")
  federalStatutes string[] @description("Applicable federal statutes")
  legalPrecedents CanLIILegalPrecedent[] @description("Relevant CanLII case law")
  regulatoryGuidance string[] @description("Relevant regulatory guidance documents")
  interpretationNotes string @description("Legal interpretation considerations")
  jurisdictionalCompliance bool @description("Whether claim meets jurisdictional requirements")
  legalRiskAssessment string @description("Assessment of legal risks in coverage decision")
}

// ================================================================================================
// POLICY ANALYSIS SCHEMAS
// ================================================================================================

class PolicyCoverageAnalysis {
  applicableCoverageTypes string[] @description("Policy coverage types that apply")
  coverageLimits string @description("Applicable coverage limits and sub-limits")
  deductibleAmount string @description("Applicable deductible amount")
  policyConditions string[] @description("Policy conditions that must be met")
  conditionsMet bool @description("Whether all policy conditions are satisfied")
  coverageInterpretation string @description("Interpretation of coverage language")
  ambiguousTerms string[] @description("Policy terms requiring interpretation")
  industryStandards string @description("Industry standard interpretation practices")
}

class ExclusionAnalysis {
  potentialExclusions PolicyExclusion[] @description("All exclusions that could potentially apply")
  applicableExclusions PolicyExclusion[] @description("Exclusions that definitively apply")
  exclusionJustification string @description("Detailed justification for exclusion application")
  canadianExclusionPrecedents CanLIILegalPrecedent[] @description("Canadian court decisions on similar exclusions")
  exclusionInterpretation string @description("Legal interpretation of exclusion language")
  exclusionRisk RiskLevel @description("Risk level of exclusion application")
}

class PolicyExclusion {
  exclusionType string @description("Type of policy exclusion")
  exclusionText string @description("Exact text of the exclusion from policy")
  applicabilityReason string @description("Why this exclusion applies to the claim")
  legalBasis string @description("Legal basis for exclusion application")
  precedentSupport string[] @description("Legal precedents supporting exclusion")
  interpretationChallenges string[] @description("Challenges in interpreting this exclusion")
}

// ================================================================================================
// CAUSE AND COVERAGE MAPPING SCHEMAS
// ================================================================================================

class CauseOfLossMapping {
  primaryCause string @description("Primary cause of loss from Level 01")
  proximateCause string @description("Legal proximate cause determination")
  coverageApplicability string @description("How cause maps to policy coverage")
  causationChain string[] @description("Sequence of events leading to loss")
  concurrentCauses string[] @description("Multiple causes contributing to loss")
  causationAnalysis string @description("Legal analysis of causation")
  canadianCausationLaw string @description("Canadian legal principles on causation")
}

class CoverageMapping {
  causeOfLoss string @description("Identified cause of loss")
  policySection string @description("Relevant policy section providing coverage")
  coverageRationale string @description("Rationale for coverage determination")
  coverageStrength float @description("Strength of coverage argument (0.0-1.0)")
  counterArguments string[] @description("Arguments against coverage")
  supportingArguments string[] @description("Arguments supporting coverage")
  industryPractice string @description("Standard industry practice for similar claims")
}

// ================================================================================================
// INFORMATION REQUIREMENTS SCHEMAS
// ================================================================================================

class InformationRequest {
  informationType string @description("Type of information needed")
  informationSource InformationSource @description("Who should provide this information")
  informationDetails string @description("Specific details of what is needed")
  justification string @description("Why this information is necessary for coverage decision")
  urgencyLevel "IMMEDIATE" | "HIGH" | "NORMAL" | "LOW" @description("Urgency of obtaining this information")
  timelineRequired string @description("Timeline for obtaining this information")
  alternativeSources string[] @description("Alternative sources if primary source unavailable")
  impactOnDecision string @description("How this information affects coverage decision")
}

class ClaimantInformationNeeded {
  documentsRequired string[] @description("Specific documents needed from claimant")
  clarificationsNeeded string[] @description("Clarifications needed about the incident")
  evidenceRequired string[] @description("Additional evidence needed from claimant")
  timelineForResponse string @description("Timeline for claimant to provide information")
  consequencesOfNonCompliance string @description("What happens if claimant doesn't provide info")
  assistanceAvailable string @description("How insurance company can assist claimant")
}

class ThirdPartyInformationNeeded {
  thirdPartyType string @description("Type of third party (witness, expert, authority, etc.)")
  thirdPartyContact string @description("Contact information if available")
  informationNeeded string @description("Specific information required from third party")
  legalBasisForRequest string @description("Legal basis for requesting information")
  voluntaryVsMandatory "VOLUNTARY" | "SUBPOENA_REQUIRED" | "COURT_ORDER_NEEDED" @description("How information can be obtained")
  costImplications string @description("Cost implications of obtaining this information")
  alternativeApproaches string[] @description("Alternative approaches if third party uncooperative")
}

class AgentObtainableInformation {
  informationType string @description("Type of information agent can obtain")
  sourceOfInformation string @description("Where agent can obtain this information")
  procedureRequired string @description("Procedure agent must follow")
  timelineToObtain string @description("Expected timeline for agent to obtain information")
  costToCompany string @description("Cost implications for insurance company")
  reliabilityLevel float @description("Reliability of this information source (0.0-1.0)")
}

// ================================================================================================
// COVERAGE DECISION SCHEMAS
// ================================================================================================

class CoverageJustification {
  primaryReason CoverageReason @description("Primary reason for coverage decision")
  detailedReasoning string @description("Comprehensive explanation of coverage decision")
  policyBasis string @description("Specific policy language supporting decision")
  legalBasis string @description("Legal basis for coverage decision")
  factualBasis string @description("Factual basis supporting decision")
  industryPractice string @description("Industry practice supporting decision")
  precedentSupport string[] @description("Legal precedents supporting decision")
  riskFactors string[] @description("Risk factors considered in decision")
  alternativeInterpretations string[] @description("Alternative interpretations considered")
  decisionStrength float @description("Strength of coverage decision (0.0-1.0)")
}

class CoverageDecisionSupport {
  supportingEvidence SupportingEvidence[] @description("Evidence supporting the coverage decision")
  contradictoryEvidence ContradictoryEvidence[] @description("Evidence that contradicts coverage")
  evidenceWeighting EvidenceWeighting @description("How different evidence was weighted")
  expertOpinions ExpertOpinion[] @description("Expert opinions obtained")
  documentationQuality DocumentationQuality @description("Quality of supporting documentation")
  evidenceGaps string[] @description("Identified gaps in evidence")
}

class SupportingEvidence {
  evidenceType string @description("Type of evidence (document, witness, expert opinion, etc.)")
  evidenceDescription string @description("Description of the evidence")
  evidenceSource string @description("Source of the evidence")
  evidenceStrength float @description("Strength of this evidence (0.0-1.0)")
  evidenceReliability float @description("Reliability of this evidence (0.0-1.0)")
  evidenceImpact string @description("Impact of this evidence on coverage decision")
}

class ContradictoryEvidence {
  evidenceType string @description("Type of contradictory evidence")
  evidenceDescription string @description("Description of contradictory evidence")
  contradictionReason string @description("Why this evidence contradicts coverage")
  responseToContradiction string @description("How this contradiction is addressed")
  contradictionWeight float @description("Weight of this contradictory evidence (0.0-1.0)")
  mitigationStrategy string @description("Strategy for addressing this contradiction")
}

class EvidenceWeighting {
  documentaryEvidence float @description("Weight given to documentary evidence")
  witnessTestimony float @description("Weight given to witness testimony")
  expertOpinion float @description("Weight given to expert opinions")
  physicalEvidence float @description("Weight given to physical evidence")
  circumstantialEvidence float @description("Weight given to circumstantial evidence")
  weightingRationale string @description("Rationale for evidence weighting approach")
}

class ExpertOpinion {
  expertType string @description("Type of expert (legal, technical, medical, etc.)")
  expertCredentials string @description("Expert's credentials and qualifications")
  opinionSummary string @description("Summary of expert's opinion")
  opinionBasis string @description("Basis for expert's opinion")
  opinionReliability float @description("Reliability of expert opinion (0.0-1.0)")
  opinionImpact string @description("Impact of opinion on coverage decision")
  conflictingOpinions string[] @description("Any conflicting expert opinions")
}

class DocumentationQuality {
  completeness float @description("Completeness of documentation (0.0-1.0)")
  reliability float @description("Reliability of documentation (0.0-1.0)")
  consistency float @description("Consistency across documents (0.0-1.0)")
  timeliness float @description("Timeliness of documentation (0.0-1.0)")
  authentication float @description("Authentication level of documents (0.0-1.0)")
  qualityNotes string @description("Notes on documentation quality issues")
}

// ================================================================================================
// EXIT PATH ANALYSIS SCHEMAS
// ================================================================================================

class Level02ExitAnalysis {
  exitPath CoverageDecision @description("Chosen exit path")
  exitReason string @description("Detailed reason for choosing this exit path")
  coverageAnalysisProvided CoverageAnalysisDetails @description("Coverage analysis completed")
  nextStepsRequired Level02NextSteps @description("What needs to happen next")
  riskAssessment Level02RiskAssessment @description("Risk assessment for this decision")
  humanReviewRequired bool @description("Whether human expert review is required")
  legalCounselRequired bool @description("Whether legal counsel review is required")
  timelineForResolution string @description("Expected timeline for final resolution")
  priorityLevel PriorityLevel @description("Priority level for handling this claim")
  escalationTriggers string[] @description("Factors that would require escalation")
}

class CoverageAnalysisDetails {
  analysisCompleteness float @description("Completeness of coverage analysis (0.0-1.0)")
  analysisConfidence float @description("Confidence in analysis conclusions (0.0-1.0)")
  keyFindings string[] @description("Key findings from coverage analysis")
  analysisLimitations string[] @description("Limitations in the analysis")
  assumptionsMade string[] @description("Assumptions made during analysis")
  uncertaintyAreas string[] @description("Areas of uncertainty in analysis")
  additionalAnalysisNeeded string[] @description("Additional analysis that may be needed")
}

class Level02NextSteps {
  immediateActions string[] @description("Actions that must be taken immediately")
  shortTermActions string[] @description("Actions needed within 1-7 days")
  longTermActions string[] @description("Actions needed within 30 days")
  documentationRequired DocumentationAction[] @description("Documentation that must be created")
  communicationRequired CommunicationAction[] @description("Communications that must be sent")
  investigationRequired InvestigationAction[] @description("Investigations that must be conducted")
  legalActionRequired LegalAction[] @description("Legal actions that may be required")
}

class DocumentationAction {
  documentType string @description("Type of document to create")
  documentPurpose string @description("Purpose of the document")
  documentRecipient string @description("Who will receive the document")
  documentTimeline string @description("When document must be completed")
  documentImportance "CRITICAL" | "HIGH" | "NORMAL" | "LOW" @description("Importance level")
}

class CommunicationAction {
  communicationType string @description("Type of communication (email, letter, phone, meeting)")
  communicationRecipient string @description("Who to communicate with")
  communicationPurpose string @description("Purpose of the communication")
  communicationTimeline string @description("When communication must occur")
  communicationMethod string @description("Preferred method of communication")
  followUpRequired bool @description("Whether follow-up is required")
}

class InvestigationAction {
  investigationType string @description("Type of investigation required")
  investigationScope string @description("Scope of the investigation")
  investigationTimeline string @description("Timeline for investigation")
  investigationResources string @description("Resources required for investigation")
  investigationCost string @description("Estimated cost of investigation")
  investigationExpectedOutcome string @description("Expected outcome of investigation")
}

class LegalAction {
  legalActionType string @description("Type of legal action")
  legalBasis string @description("Legal basis for the action")
  legalTimeline string @description("Timeline for legal action")
  legalCost string @description("Estimated cost of legal action")
  legalRisk string @description("Legal risks associated with action")
  alternativesToLegalAction string[] @description("Alternatives to legal action")
}

class Level02RiskAssessment {
  coverageRisk RiskLevel @description("Risk level of coverage decision")
  legalRisk RiskLevel @description("Legal risk level")
  financialRisk RiskLevel @description("Financial risk level")
  reputationalRisk RiskLevel @description("Reputational risk level")
  regulatoryRisk RiskLevel @description("Regulatory compliance risk level")
  overallRisk RiskLevel @description("Overall risk level")
  riskMitigationStrategies string[] @description("Strategies to mitigate identified risks")
  riskMonitoringRequired bool @description("Whether ongoing risk monitoring is required")
}

// ================================================================================================
// MAIN LEVEL 02 COVERAGE ANALYSIS RESULT SCHEMA
// ================================================================================================

class Level02CoverageAnalysis {
  // Primary Coverage Decision
  coverageDecision CoverageDecision @description("Final coverage determination")
  confidenceScore float @description("Overall analysis confidence (0.0-1.0)")
  
  // Core Analysis Components
  policyAnalysis PolicyCoverageAnalysis @description("Detailed policy coverage analysis")
  exclusionAnalysis ExclusionAnalysis @description("Policy exclusions assessment")
  coverageMapping CoverageMapping @description("Cause-to-coverage mapping analysis")
  causeMapping CauseOfLossMapping @description("Legal causation analysis")
  
  // Decision Support and Justification
  coverageJustification CoverageJustification @description("Detailed coverage decision justification")
  decisionSupport CoverageDecisionSupport @description("Evidence and support for decision")
  
  // Canadian Legal Context
  canadianLegalAnalysis CanadianLegalAnalysis @description("Canadian legal and regulatory analysis")
  
  // Medical Impact Assessment (for injury claims)
  medicalImpactAssessment MedicalImpactAssessment? @description("Medical impact assessment for injury claims")
  
  // Information Requirements (for INFORMATION_REQUIRED exit)
  claimantInformation ClaimantInformationNeeded? @description("Information needed from claimant")
  thirdPartyInformation ThirdPartyInformationNeeded[] @description("Information needed from third parties")
  agentInformation AgentObtainableInformation[] @description("Information agent can obtain")
  allInformationRequests InformationRequest[] @description("All information requests categorized")
  
  // Exit Path Analysis
  exitAnalysis Level02ExitAnalysis @description("Detailed analysis for chosen exit path")
  
  // Quality and Risk Metrics
  analysisQuality AnalysisQuality @description("Quality assessment of the analysis")
  riskAssessment Level02RiskAssessment @description("Comprehensive risk assessment")
  uncertaintyAreas UncertaintyArea[] @description("Areas requiring further clarification")
  
  // UI METADATA AND EXPLAINABILITY (NEW)
  uiMetadata UIMetadata @description("UI metadata for frontend entity mapping and highlighting")
  explainabilityInsights ExplainabilityInsight[] @description("LIME/SHAP explainability analysis for coverage decisions")
  
  // Processing Metadata
  level01Data Level01Summary @description("Summary of Level 01 analysis used")
  analysisTimestamp string @description("When Level 02 analysis was performed")
  processingTimeMs int @description("Time taken for analysis in milliseconds")
  modelVersion string @description("Analysis model version used")
  analystId string @description("ID of analyst (human or AI) performing analysis")
}

// ================================================================================================
// SUPPORTING SCHEMAS
// ================================================================================================

class AnalysisQuality {
  dataCompleteness float @description("Completeness of input data (0.0-1.0)")
  dataReliability float @description("Reliability of input data (0.0-1.0)")
  analysisDepth float @description("Depth of analysis performed (0.0-1.0)")
  analysisConsistency float @description("Consistency of analysis (0.0-1.0)")
  expertValidation float @description("Level of expert validation (0.0-1.0)")
  overallQuality float @description("Overall analysis quality score (0.0-1.0)")
  qualityNotes string[] @description("Notes on quality issues or strengths")
}

class UncertaintyArea {
  uncertaintyType string @description("Type of uncertainty")
  uncertaintyDescription string @description("Description of the uncertainty")
  uncertaintyImpact string @description("Impact of uncertainty on coverage decision")
  resolutionApproach string @description("Approach to resolve uncertainty")
  resolutionTimeline string @description("Timeline for resolving uncertainty")
  uncertaintyRisk RiskLevel @description("Risk level associated with this uncertainty")
}

class Level01Summary {
  claimId string @description("Claim ID from Level 01")
  claimType string @description("Type of claim from Level 01")
  policyNumber string @description("Policy number identified in Level 01")
  incidentDate string @description("Incident date from Level 01")
  primaryCause string @description("Primary cause of loss from Level 01")
  level01Confidence float @description("Confidence score from Level 01")
  level01ExitPath string @description("Exit path from Level 01 analysis")
  keyFindings string[] @description("Key findings from Level 01 that affect coverage")
}

// ================================================================================================
// INPUT SCHEMA FOR LEVEL 02 ANALYSIS
// ================================================================================================

class Level02AnalysisInput {
  claimId string @description("Unique claim identifier")
  level01Analysis Level01Summary @description("Complete Level 01 analysis results")
  policyDocuments string[] @description("Policy document texts (if available)")
  additionalEvidence string[] @description("Additional evidence texts")
  humanInputs string[] @description("Manual clarifications or inputs")
  processingNotes string[] @description("Processing metadata and notes")
  urgencyLevel "IMMEDIATE" | "HIGH" | "NORMAL" | "LOW" @description("Processing urgency")
  specialInstructions string[] @description("Special handling instructions")
}

// ================================================================================================
// MAIN LEVEL 02 ANALYSIS FUNCTION
// ================================================================================================

function AnalyzeCoverageLevel02(
  claimId: string,
  claimType: string,
  policyNumber: string,
  incidentDate: string,
  primaryCause: string,
  level01Confidence: float,
  level01ExitPath: string,
  keyFindings: string[],
  policyDocuments: string,
  additionalEvidence: string,
  canadianJurisdiction: string
) -> Level02CoverageAnalysis {
  client CustomGPT4o
  prompt #"
system: You are Zurich Insurance's expert Level 02 Coverage Analyst specializing in Canadian insurance law and policy interpretation.

MISSION: Determine coverage for the claim using comprehensive policy analysis, Canadian legal precedents, and detailed justification.

⚠️ ACCURACY REQUIREMENTS:

1. POLICY EXCLUSIONS:
   - Only cite exclusions that are explicitly provided in the policy documents
   - If no policy document is provided, state "Policy document not available for exclusion review"
   - Mark any exclusion analysis as "REQUIRES_POLICY_REVIEW" if no policy text provided

2. LEGAL PRECEDENTS:
   - Focus on well-established Canadian legal principles
   - If you don't have access to specific precedents, state "Legal research required - specific precedent database not available"
   - Use general legal principles rather than specific case citations unless you're certain of accuracy

3. POLICY LANGUAGE:
   - Base analysis on policy language explicitly provided in the input
   - If policy terms are missing, clearly indicate "Policy language not available"

4. FACTUAL CLAIMS:
   - Base analysis on facts provided in the Level 01 analysis and evidence
   - Clearly distinguish between "facts provided" and "assumptions made"

5. CONFIDENCE SCORING:
   - Adjust confidence scores based on completeness of available information
   - Factor in missing information when calculating confidence

LEVEL 01 ANALYSIS RESULTS:
Claim ID: {{ claimId }}
Claim Type: {{ claimType }}
Policy Number: {{ policyNumber }}
Primary Cause: {{ primaryCause }}
Incident Date: {{ incidentDate }}
Level 01 Confidence: {{ level01Confidence }}
Key Level 01 Findings: {{ keyFindings }}

ADDITIONAL EVIDENCE:
{{ additionalEvidence }}

POLICY DOCUMENTS:
{{ policyDocuments }}

CANADIAN JURISDICTION: {{ canadianJurisdiction }}

⚠️ MEDICAL ANALYSIS REQUIREMENTS (if injuries reported):

If Level 01 indicates injuries, perform comprehensive medical impact assessment:

1. INJURY SEVERITY EVALUATION:
   - Assess injury severity based on Level 01 medical information
   - Consider treatment complexity and duration
   - Evaluate functional impairment and disability potential

2. MEDICAL CAUSATION ANALYSIS:
   - Assess clarity of medical causation to incident
   - Identify any pre-existing conditions that may complicate claims
   - Evaluate quality of medical documentation

3. COVERAGE IMPLICATIONS:
   - Consider impact on policy limits adequacy
   - Assess need for expert medical opinions
   - Factor medical complexity into coverage confidence
   - Evaluate return-to-work likelihood and financial impact

4. FUTURE CARE ASSESSMENT:
   - Estimate future care costs based on injury severity
   - Consider long-term disability implications
   - Assess permanent impairment potential

Base medical analysis on injury details, treatment records, diagnostic results, and medical timeline from Level 01 analysis.

🎯 CRITICAL: UI METADATA & EXPLAINABILITY REQUIREMENTS

For EVERY coverage decision and policy analysis, provide complete UI metadata:

1. ENTITY MAPPING FOR COVERAGE ANALYSIS:
   - Field name (coverage_decision, policy_limits, exclusions_applied, etc.)
   - Extracted/analyzed values with source attribution
   - Original policy language or document text
   - Source document name and location
   - Decision reasoning with confidence scores
   - Appropriate UI highlight colors based on decision type

2. COVERAGE DECISION EXPLAINABILITY (LIME/SHAP STYLE):
   - Feature importance analysis: What factors most influenced coverage decision?
   - Policy language impact: How did specific policy terms affect the decision?
   - Legal precedent influence: Weight of legal factors in decision
   - Medical factors: If injury claim, how medical info influenced coverage
   - Risk assessment factors: What increased/decreased confidence
   - Alternative scenarios: What would change the coverage decision

3. DOCUMENT HIGHLIGHTS FOR POLICY ANALYSIS:
   - Create highlighting regions for policy terms used
   - Map exclusions to their exact policy language
   - Link coverage provisions to decision reasoning
   - Highlight legal precedents or regulatory factors

4. CONFIDENCE BREAKDOWN:
   - Per-field confidence with detailed reasoning
   - Policy completeness impact on confidence
   - Legal clarity impact on confidence
   - Medical information quality impact (if applicable)
   - Overall decision confidence factors

5. VISUALIZATION DATA:
   - Coverage flow charts showing decision logic
   - Feature importance charts for coverage factors
   - Policy provision impact analysis
   - Risk assessment visualizations
   - Medical severity impact charts (if applicable)

UI COLOR SCHEME FOR LEVEL 02:
Coverage Decision Colors:
- COVERED: #4CAF50 (green)
- NOT_COVERED: #F44336 (red)
- INFORMATION_REQUIRED: #FF9800 (orange)
- POLICY_LANGUAGE: #2196F3 (blue)
- EXCLUSIONS: #E91E63 (pink)
- LEGAL_PRECEDENT: #9C27B0 (purple)
- MEDICAL_FACTORS: #795548 (brown)

EXPLAINABILITY DEPTH FOR COVERAGE DECISIONS:

For each coverage determination, analyze:
- Which policy provisions were most important?
- How did legal precedents influence the decision?
- What medical factors affected coverage (if applicable)?
- How does evidence quality impact confidence?
- What additional information would improve confidence?
- What alternative interpretations were considered?

ALTERNATIVE SCENARIO ANALYSIS:
- Different policy interpretations
- Impact of additional evidence
- Regulatory changes that could affect decision
- Medical prognosis variations (if applicable)
- Legal precedent changes

{{ ctx.output_format }}
"#
}

// ================================================================================================
// CANLII LEGAL RESEARCH FUNCTION (FUTURE INTEGRATION)
// ================================================================================================

function ResearchCanadianLegalPrecedents(
  claimType: string,
  causeOfLoss: string,
  policyLanguage: string,
  jurisdiction: string
) -> CanadianLegalAnalysis {
  client "openai/gpt-4o-mini"
  prompt #"
    You are simulating CanLII legal research for insurance coverage analysis.
    This function will be enhanced with actual CanLII API integration.
    
    RESEARCH PARAMETERS:
    Claim Type: {{ claimType }}
    Cause of Loss: {{ causeOfLoss }}
    Policy Language: {{ policyLanguage }}
    Jurisdiction: {{ jurisdiction }}
    
    Research Canadian legal precedents relevant to this insurance coverage question.
    Focus on coverage interpretation, exclusion application, and policy construction principles.
    
    Provide realistic legal analysis based on Canadian insurance law principles.
    
    {{ ctx.output_format }}
  "#
}

// ================================================================================================
// MEDICAL IMPACT ANALYSIS FOR COVERAGE (NEW)
// ================================================================================================

enum MedicalComplexity {
  LOW @description("Minor injuries, straightforward treatment")
  MODERATE @description("Moderate injuries requiring ongoing care")
  HIGH @description("Severe injuries with complex treatment needs")
  CATASTROPHIC @description("Life-altering injuries requiring long-term care")
}

class MedicalImpactAssessment {
  injurySeverityLevel MedicalComplexity @description("Overall injury severity level")
  treatmentComplexity MedicalComplexity @description("Complexity of required treatment")
  functionalImpairment string @description("Level of functional impairment")
  returnToWorkLikelihood "full_return" | "modified_duties" | "partial_return" | "unlikely" @description("Likelihood of return to work")
  futureCareCosts string @description("Estimated future care costs")
  permanentDisability bool @description("Whether permanent disability is likely")
  medicalCausationClear bool @description("Whether medical causation is clear")
  preExistingConditions string[] @description("Any identified pre-existing conditions")
  medicalDocumentationQuality "excellent" | "good" | "adequate" | "poor" @description("Quality of medical documentation")
  expertMedicalOpinionNeeded bool @description("Whether expert medical opinion is required")
}
