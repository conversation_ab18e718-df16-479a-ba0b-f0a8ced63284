// ================================================================================================
// SHARED UI METADATA AND EXPLAINABILITY SCHEMAS
// ================================================================================================
// Purpose: Shared schemas for UI metadata and explainability across all analysis levels
// Used by: Level01, Level02, Level03, Level04 analysis schemas
// ================================================================================================

// ================================================================================================
// CORE UI METADATA SCHEMAS
// ================================================================================================

class UIMetadata {
  entityMappings EntityMapping[] @description("UI metadata for entity-to-source mapping")
  explainabilityData ExplainabilityInsight[] @description("LIME/SHAP analysis for model decisions")
  documentHighlights DocumentHighlight[] @description("Precise highlighting coordinates")
  confidenceBreakdown FieldConfidenceBreakdown @description("Per-field confidence analysis")
  colorScheme ColorSchemeMapping @description("UI color scheme for different entity types")
  analysisFlow AnalysisFlow @description("Visual flow of analysis logic")
}

class EntityMapping {
  fieldName string @description("Entity field name")
  extractedValue string @description("The extracted/normalized value")
  originalText string @description("Original text from document")
  sourceDocument string @description("Source document filename")
  pageNumber int @description("Page number where found")
  lineNumber int? @description("Line number if available")
  startPosition int? @description("Character start position")
  endPosition int? @description("Character end position")
  boundingBox float[]? @description("OCR bounding box [x1, y1, x2, y2]")
  extractionMethod string @description("How entity was extracted")
  confidence float @description("Extraction confidence (0.0-1.0)")
  highlightColor string @description("UI highlight color for this entity")
  entityType string @description("Type of entity")
  relevanceScore float @description("Relevance score for analysis (0.0-1.0)")
}

class DocumentHighlight {
  documentId string @description("Document identifier")
  documentName string @description("Document filename")
  pageNumber int @description("Page number")
  highlights HighlightRegion[] @description("Highlight regions on this page")
}

class HighlightRegion {
  fieldName string @description("Associated entity field")
  text string @description("Highlighted text")
  startPos int @description("Character start position")
  endPos int @description("Character end position")
  boundingBox float[]? @description("Visual bounding box")
  highlightColor string @description("UI color for highlight")
  confidence float @description("Confidence in this extraction")
  entityType string @description("Type of entity being highlighted")
  analysisRelevance float @description("Relevance to analysis (0.0-1.0)")
}

class FieldConfidenceBreakdown {
  overallConfidence float @description("Overall analysis confidence")
  fieldConfidences FieldConfidence[] @description("Per-field confidence scores")
  uncertaintyFactors string[] @description("Factors contributing to uncertainty")
  confidenceDistribution ConfidenceDistribution @description("Confidence distribution analysis")
  analysisCertainty float @description("Certainty in analysis results")
}

class FieldConfidence {
  fieldName string @description("Field name")
  confidence float @description("Confidence score")
  contributingFactors string[] @description("What contributed to this confidence")
  uncertaintyReasons string[] @description("Reasons for any uncertainty")
  sourceQuality float @description("Quality of source information (0.0-1.0)")
  validationStrength float @description("Strength of validation (0.0-1.0)")
}

class ConfidenceDistribution {
  highConfidenceFields string[] @description("Fields with >90% confidence")
  mediumConfidenceFields string[] @description("Fields with 70-90% confidence")
  lowConfidenceFields string[] @description("Fields with <70% confidence")
  averageConfidence float @description("Average confidence across all fields")
}

class ColorSchemeMapping {
  entityTypeColors EntityTypeColor[] @description("Color mapping for different entity types")
  confidenceColorScheme ConfidenceColorScheme @description("Color scheme based on confidence levels")
  analysisColorScheme AnalysisColorScheme @description("Color scheme for analysis-specific elements")
}

class EntityTypeColor {
  entityType string @description("Entity type")
  primaryColor string @description("Primary highlight color")
  secondaryColor string @description("Secondary/border color")
  textColor string @description("Text color for good contrast")
  weight float @description("Weight/importance of this entity type (0.0-1.0)")
}

class ConfidenceColorScheme {
  highConfidenceColor string @description("Color for high confidence (>90%)")
  mediumConfidenceColor string @description("Color for medium confidence (70-90%)")
  lowConfidenceColor string @description("Color for low confidence (<70%)")
}

class AnalysisColorScheme {
  criticalColor string @description("Color for critical elements")
  highPriorityColor string @description("Color for high priority elements")
  mediumPriorityColor string @description("Color for medium priority elements")
  lowPriorityColor string @description("Color for low priority elements")
}

class AnalysisFlow {
  flowSteps AnalysisFlowStep[] @description("Steps in analysis logic")
  decisionPoints DecisionPoint[] @description("Key decision points in analysis")
  dataFlow DataFlowStep[] @description("Flow of data through analysis")
  alternativeScenarios AlternativeScenario[] @description("Alternative scenarios considered")
}

class AnalysisFlowStep {
  stepNumber int @description("Step number in analysis process")
  stepName string @description("Name of this analysis step")
  stepDescription string @description("Description of what happens in this step")
  inputData string[] @description("Input data considered in this step")
  outcome string @description("Outcome of this step")
  nextSteps string[] @description("Possible next steps")
  confidence float @description("Confidence in this step's outcome")
}

class DecisionPoint {
  decisionName string @description("Name of the decision")
  decisionDescription string @description("Description of the decision made")
  inputFactors string[] @description("Factors that influenced this decision")
  outcome string @description("Outcome of the decision")
  confidence float @description("Confidence in this decision")
  alternativeOptions string[] @description("Alternative options considered")
}

class DataFlowStep {
  stepName string @description("Name of data flow step")
  sourceData string[] @description("Source data for this step")
  processing string @description("Processing performed on data")
  outputData string[] @description("Output data from this step")
  dataQuality float @description("Quality of data after processing (0.0-1.0)")
}

// ================================================================================================
// EXPLAINABILITY SCHEMAS
// ================================================================================================

class ExplainabilityInsight {
  analysisType "LIME" | "SHAP" | "INTEGRATED_GRADIENTS" | "ATTENTION" @description("Type of explainability analysis")
  decisionContext string @description("What decision this explains")
  featureImportances FeatureImportance[] @description("Feature importance scores")
  topPositiveFactors ExplanationFactor[] @description("Top factors supporting decision")
  topNegativeFactors ExplanationFactor[] @description("Top factors against decision")
  confidenceContributors ConfidenceContributor[] @description("What contributed to confidence")
  alternativeScenarios AlternativeScenario[] @description("Alternative scenarios and their impact")
  visualizationData VisualizationData @description("Data for UI visualization")
  methodologyNotes string[] @description("Notes about the explainability methodology used")
  contextualFactors ContextualFactors @description("Contextual factors affecting analysis")
}

class FeatureImportance {
  featureName string @description("Name of the feature/field")
  importance float @description("Importance score (-1.0 to 1.0)")
  direction "POSITIVE" | "NEGATIVE" | "NEUTRAL" @description("Impact direction")
  explanation string @description("Human-readable explanation")
  sourceText string? @description("Source text that contributed")
  confidence float @description("Confidence in this importance score")
  relatedFields string[] @description("Other fields this feature influences")
  analysisContext string @description("Context for this feature's importance")
}

class ExplanationFactor {
  factor string @description("The factor/feature")
  impact float @description("Impact score")
  explanation string @description("Why this factor matters")
  sourceLocation string? @description("Where this factor was found")
  evidenceStrength "STRONG" | "MODERATE" | "WEAK" @description("Strength of evidence")
  supportingEvidence string[] @description("Supporting evidence for this factor")
}

class ConfidenceContributor {
  contributor string @description("What contributed to confidence")
  contribution float @description("How much it contributed (0.0-1.0)")
  reason string @description("Why this contributed to confidence")
  sourceType "DOCUMENT_QUALITY" | "TEXT_CLARITY" | "PATTERN_MATCH" | "CROSS_VALIDATION" | "EXPERT_VALIDATION" @description("Type of contributor")
}

class AlternativeScenario {
  scenario string @description("Alternative scenario description")
  probabilityChange float @description("How decision probability would change")
  requiredChanges string[] @description("What would need to change")
  likelihood "HIGH" | "MEDIUM" | "LOW" @description("Likelihood of this scenario")
  implications string[] @description("Implications of this scenario")
}

class VisualizationData {
  chartType "BAR" | "WATERFALL" | "HEATMAP" | "SANKEY" | "FEATURE_IMPORTANCE" | "FLOW_DIAGRAM" @description("Recommended chart type")
  chartData ChartDataPoint[] @description("Data points for visualization")
  colorScheme string[] @description("Color scheme for visualization")
  annotations ChartAnnotation[] @description("Chart annotations")
  interactiveElements InteractiveElement[] @description("Interactive elements for the visualization")
}

class ChartDataPoint {
  label string @description("Data point label")
  value float @description("Data point value")
  color string? @description("Specific color for this point")
  metadata string? @description("Additional metadata")
  tooltip string? @description("Tooltip text for this point")
  analysisImpact float @description("Impact on overall analysis")
}

class ChartAnnotation {
  text string @description("Annotation text")
  position string @description("Where to place annotation")
  style string @description("Annotation style")
  importance "HIGH" | "MEDIUM" | "LOW" @description("Importance of this annotation")
  analysisRelevance float @description("Relevance to analysis")
}

class InteractiveElement {
  elementType "HOVER_DETAIL" | "CLICK_DRILL_DOWN" | "FILTER" | "ZOOM" | "COMPARE" @description("Type of interactive element")
  targetField string @description("Field this element targets")
  action string @description("What action it performs")
  description string @description("Description of the interaction")
  context string @description("Context for this interaction")
}

class ContextualFactors {
  analysisLevel string @description("Analysis level (L1, L2, L3, L4)")
  analysisContext string @description("Context of the analysis")
  domainSpecificFactors string[] @description("Domain-specific factors affecting analysis")
  regulatoryFactors string[] @description("Regulatory factors considered")
  businessRules string[] @description("Business rules applied")
  qualityFactors string[] @description("Factors affecting data quality")
}