// ================================================================================================
// ZURICH LEVEL 04 QUANTUM CALCULATION - AI-ENHANCED DAMAGE ASSESSMENT (RESILIENT VERSION)
// ================================================================================================
// Purpose: AI extracts comprehensive damage details from all data sources for accurate quantum calculation
// Hybrid Approach: AI Analysis → Structured Extraction → Rule-Based Canadian Quantum Calculation
// Data Sources: Level 1, Level 2, Level 3, Email Content, SparkNLP insights, OCR text
// RESILIENT: All fields optional with defaults to handle incomplete data gracefully
// ================================================================================================

// ================================================================================================
// ONTARIO QUANTUM CALCULATION BENCHMARKS
// ================================================================================================

class OntarioQuantumBenchmarks {
  painAndSufferingBenchmarks OntarioPainSufferingGuide @description("Ontario pain and suffering benchmarks")
  medicalCostGuidelines OntarioMedicalGuidelines @description("Ontario medical cost assessment guidelines")
  incomeLossStandards OntarioIncomeLossStandards @description("Ontario income loss calculation standards")
  statutoryThresholds OntarioStatutoryThresholds @description("Ontario statutory thresholds and deductibles")
}

class OntarioPainSufferingGuide {
  minorInjuryRange string @description("Minor soft tissue: $5,000 - $15,000")
  moderateInjuryRange string @description("Moderate injuries with some impairment: $15,000 - $45,000")
  severeInjuryRange string @description("Severe injuries with significant impairment: $45,000 - $150,000")
  catastrophicInjuryRange string @description("Catastrophic injuries: $150,000 - $420,000 (Andrews cap)")
  slipFallBenchmarks string @description("Typical slip-fall settlements: $10,000 - $50,000 for moderate cases")
  andrewsCap2025 float @description("2025 Andrews cap: $420,000")
  deductible2025 float @description("2025 pain and suffering deductible: $45,239")
  conservativeApproach string @description("Apply conservative estimates for human review and settlement")
}

class OntarioMedicalGuidelines {
  emergencyCareTypical string @description("Emergency care: $500 - $2,000 typical range")
  physiotherapySessionCost float @description("Physiotherapy: $80 - $150 per session")
  chiropracticSessionCost float @description("Chiropractic: $60 - $120 per session")
  diagnosticImagingCosts string @description("X-ray: $100-200, MRI: $800-1200, Ultrasound: $200-400")
  maximumTreatmentDuration string @description("Moderate injuries: 6-12 months treatment typical")
  documentationRequirement string @description("All costs must be supported by receipts or medical invoices")
  evidenceBasedLimits string @description("CRITICAL: Only include costs with documentary evidence - do not project speculative future costs")
  moderateInjuryMedicalCap float @description("Moderate slip-fall injuries: $2,000-$5,000 typical total medical costs")
  ageAdjustedRecovery string @description("Claimants over 60: Apply conservative recovery assumptions and reduced future treatment projections")
}

class OntarioIncomeLossStandards {
  averageWeeklyEarnings string @description("Use actual pre-accident earnings or provincial average")
  maximumBenefitPeriod string @description("Income replacement benefits: maximum periods apply")
  returnToWorkAssumption string @description("Assume return to work unless permanent disability proven")
  ageFactors string @description("Consider age for future income loss projections")
  documentationRequired string @description("Employment records and medical work capacity assessments required")
  ageSpecificGuidelines string @description("CRITICAL: Claimants over 60 - apply reduced future income projections considering proximity to retirement")
  evidenceBasedCalculations string @description("Base ALL income loss on documented employment records and actual wage statements - no speculative projections")
  conservativeApproach string @description("For moderate injuries, assume return to full capacity within 6-12 months unless medical evidence proves otherwise")
}

class OntarioStatutoryThresholds {
  motorVehicleThreshold string @description("Motor vehicle: permanent serious impairment threshold")
  nonEconomicLossDeductible float @description("Non-economic loss deductible: $45,239 (2025)")
  andrewsCapAmount float @description("Andrews cap: $420,000 (2025)")
  catastrophicThreshold string @description("Catastrophic impairment threshold definitions")
  faultReductionRules string @description("Comparative negligence reduction applies to all damages")
}

// ================================================================================================
// LEVEL 04 QUANTUM ANALYSIS INPUT
// ================================================================================================

class Level04AnalysisInput {
  claimReference string @description("Unique claim identifier")
  province string @description("Canadian province for quantum calculation rules")
  
  // Comprehensive data sources
  level01Analysis string @description("Complete Level 1 analysis results as JSON string")
  level02Coverage string @description("Level 2 coverage analysis as JSON string")
  level03Fault string @description("Level 3 fault determination as JSON string")
  emailContent string[] @description("Original email content and communications")
  sparkNlpInsights string @description("SparkNLP extracted entities and insights as JSON")
  ocrTexts string[] @description("OCR extracted text from documents")
  attachmentDetails string[] @description("Document names and descriptions")
}

// ================================================================================================
// QUANTUM CALCULATION OUTPUT SCHEMAS
// ================================================================================================

class QuantumDamageExtraction {
  medicalDamages MedicalDamageAssessment @description("Comprehensive medical damage assessment")
  incomeLossDamages IncomeLossAssessment @description("Income loss and earning capacity assessment")
  careAndAssistanceCosts CareAssistanceAssessment @description("Care and assistance requirements")
  specialDamages SpecialDamagesBreakdown @description("Out-of-pocket and special damages")
  generalDamages GeneralDamagesAssessment @description("Pain and suffering assessment")
  futureCareCosts FutureCareAssessment @description("Future care and treatment costs")
  faultImpactAnalysis FaultImpactOnQuantum @description("How fault allocation affects quantum calculation")
  quantumGuidance QuantumCalculationGuidance @description("AI guidance for quantum calculation")
  ontarioBenchmarks OntarioQuantumBenchmarks @description("Ontario-specific benchmarks applied")
  validationChecks QuantumValidationChecks @description("Validation against overvaluation")
  
  // UI METADATA AND EXPLAINABILITY (NEW)
  uiMetadata UIMetadata @description("UI metadata for frontend entity mapping and highlighting")
  explainabilityInsights ExplainabilityInsight[] @description("LIME/SHAP explainability analysis for quantum calculations")
}

class MedicalDamageAssessment {
  emergencyCareCosts float @description("Emergency department and initial treatment costs")
  ongoingTreatmentCosts float @description("Physiotherapy, chiropractic, and ongoing care costs")
  diagnosticCosts float @description("X-rays, MRI, ultrasound, and diagnostic test costs")
  medicationCosts float @description("Prescription and over-the-counter medication costs")
  medicalEquipmentCosts float @description("Medical aids, braces, mobility equipment costs")
  treatmentSessions int @description("Number of documented treatment sessions")
  treatmentDuration string @description("Duration of treatment period (e.g., '8 weeks', '6 months')")
  medicalProviders string[] @description("Healthcare providers and facilities involved")
  medicalComplexity "simple" | "moderate" | "complex" | "severe" | "unknown" @description("Complexity of medical treatment required")
  documentedMedicalCosts float @description("Total documented medical expenses")
  estimatedFutureMedical float @description("Estimated future medical costs")
  medicalCostCertainty float @description("Confidence in medical cost assessment (0.0-1.0)")
  currentTreatmentStatus "ongoing" | "completed" | "planned" | "unknown" @description("Current treatment status")
  recoveryPrognosis "excellent" | "good" | "fair" | "poor" | "unknown" @description("Recovery prognosis")
}

class IncomeLossAssessment {
  preAccidentIncome float @description("Pre-accident annual income")
  employmentStatus "employed" | "self_employed" | "unemployed" | "student" | "retired" | "unknown" @description("Employment status at time of accident")
  jobTitle string @description("Job title or occupation")
  timeOffWork string @description("Period unable to work (e.g., '8 weeks', '3 months')")
  workDaysLost int @description("Number of work days lost")
  wageReplacementAmount float @description("Income replacement benefits received")
  returnToWorkStatus "full_capacity" | "reduced_capacity" | "modified_duties" | "unable_to_return" @description("Return to work status")
  futureEarningImpact "no_impact" | "temporary_reduction" | "permanent_reduction" | "total_disability" @description("Impact on future earning capacity")
  documentedIncomeLoss float @description("Documented income loss amount")
  projectedFutureIncomeLoss float @description("Projected future income loss")
  incomeLossCertainty float @description("Confidence in income loss assessment (0.0-1.0)")
  returnToWorkCapacity float @description("Return to work capacity (0.0-1.0)")
  ageFactorAdjustment "none" | "minor" | "significant" | "unknown" @description("Age factor adjustment for future income")
}

class CareAssistanceAssessment {
  familyCareProvided bool @description("Whether family provided care assistance")
  familyCareHours float @description("Hours of family care assistance provided")
  familyCareValue float @description("Economic value of family care assistance")
  professionalCareRequired bool @description("Whether professional care assistance required")
  professionalCareHours float @description("Hours of professional care required")
  professionalCareCosts float @description("Cost of professional care assistance")
  housekeepingAssistance bool @description("Whether housekeeping assistance required")
  housekeepingCosts float @description("Cost of housekeeping assistance")
  personalCareNeeds string[] @description("Specific personal care needs identified")
  careAssistanceDuration string @description("Duration care assistance was/will be needed")
  totalCareAssistanceCosts float @description("Total documented care assistance costs")
}

class SpecialDamagesBreakdown {
  transportationCosts float @description("Medical transportation and travel costs")
  accommodationCosts float @description("Accommodation costs for medical treatment")
  prescriptionCosts float @description("Prescription medication costs")
  medicalSuppliesCosts float @description("Medical supplies and equipment costs")
  parkingFees float @description("Hospital and medical facility parking fees")
  lostBenefits float @description("Lost employment benefits and perks")
  otherOutOfPocketCosts float @description("Other documented out-of-pocket expenses")
  outOfPocketReceipts string[] @description("Types of receipts and documentation available")
  specialDamagesTotal float @description("Total special damages amount")
  specialDamagesCertainty float @description("Confidence in special damages assessment (0.0-1.0)")
}

class GeneralDamagesAssessment {
  painLevel "minimal" | "mild" | "moderate" | "severe" | "extreme" @description("Level of pain experienced")
  sufferingLevel "minimal" | "mild" | "moderate" | "severe" | "extreme" @description("Level of suffering experienced")
  functionalImpairment "none" | "minimal" | "moderate" | "significant" | "severe" | "unknown" @description("Functional impairment level")
  lifestyleImpact "none" | "minimal" | "moderate" | "significant" | "severe" | "unknown" @description("Impact on lifestyle and activities")
  psychologicalImpact "none" | "minimal" | "moderate" | "significant" | "severe" | "unknown" @description("Psychological impact and distress")
  relationshipImpact "none" | "minimal" | "moderate" | "significant" | "severe" | "unknown" @description("Impact on personal relationships")
  ageAtTimeOfAccident int @description("Claimant's age at time of accident")
  genderConsiderations string @description("Gender-specific considerations for quantum assessment")
  permanentDisabilityFactor float @description("Permanent disability factor (0.0-1.0)")
  comparableAwards string[] @description("References to comparable awards or precedents")
  generalDamagesRange string @description("Estimated general damages range")
  recommendedGeneralDamages float @description("Recommended general damages amount")
  ontarioBenchmarkCategory "minor" | "moderate" | "severe" | "catastrophic" | "unknown" @description("Ontario benchmark category for this case")
  benchmarkJustification string @description("Justification for chosen benchmark category")
  conservativeEstimate bool @description("Whether this is a conservative estimate for settlement purposes")
}

class FutureCareAssessment {
  futureMedicalTreatment bool @description("Whether future medical treatment is required")
  futureTreatmentCosts float @description("Estimated future medical treatment costs")
  futureTherapyCosts float @description("Estimated future therapy and rehabilitation costs")
  futureCarePeriod string @description("Period over which future care will be required")
  assistiveDevices string[] @description("Future assistive devices or equipment needed")
  assistiveDeviceCosts float @description("Estimated costs of assistive devices")
  homeModifications string[] @description("Required home modifications for accessibility")
  homeModificationCosts float @description("Estimated costs of home modifications")
  attendantCareRequired bool @description("Whether future attendant care is required")
  attendantCareCosts float @description("Estimated future attendant care costs")
  totalFutureCareCosts float @description("Total estimated future care costs")
  futureCareUncertainty float @description("Uncertainty level in future care assessment (0.0-1.0)")
}

class FaultImpactOnQuantum {
  claimantFaultPercentage float @description("Claimant's fault percentage from Level 3")
  faultReductionApplicable bool @description("Whether fault reduction applies to damages")
  specialDamagesReduction float @description("Reduction applied to special damages")
  generalDamagesReduction float @description("Reduction applied to general damages")
  futureCareDamagesReduction float @description("Reduction applied to future care damages")
  thresholdConsiderations string[] @description("Provincial threshold considerations")
  faultImpactOnSettlement string @description("How fault affects overall settlement strategy")
  netRecoverableAmount float @description("Net recoverable amount after fault reduction")
}

class QuantumCalculationGuidance {
  totalSpecialDamages float @description("Total calculated special damages")
  totalGeneralDamages float @description("Total calculated general damages")
  totalFutureCareDamages float @description("Total future care damages")
  grossDamagesTotal float @description("Total gross damages before fault reduction")
  netDamagesTotal float @description("Total net damages after fault reduction")
  quantumConfidence float @description("Confidence in quantum calculation (0.0-1.0)")
  quantumRationale string @description("Reasoning for quantum calculation approach")
  provincialFactors string[] @description("Provincial factors affecting quantum calculation")
  uncertaintyAreas string[] @description("Areas requiring expert assessment or clarification")
  recommendedExpertReports string[] @description("Recommended expert reports for quantum support")
  settlementRange string @description("Recommended settlement range")
  litigationRisk "low" | "moderate" | "high" | "very_high" | "unknown" @description("Risk assessment for litigation")
  settlementStrategy "negotiate" | "mediate" | "litigate" | "settle_fast" | "unknown" @description("Recommended settlement approach")
}

class QuantumValidationChecks {
  painSufferingValidation string @description("Validation of pain and suffering against Ontario benchmarks")
  totalDamagesValidation string @description("Validation of total damages against typical case ranges")
  medicalCostValidation string @description("Validation of medical costs against documented evidence")
  settlementRangeValidation string @description("Validation of settlement range against provincial standards")
  overvaluationRisk "low" | "moderate" | "high" | "critical" @description("Risk of overvaluation assessment")
  recommendedAdjustments string[] @description("Recommended adjustments to prevent overvaluation")
  comparisonToActualClaims string @description("How this compares to similar actual claims in Ontario")
}

// ================================================================================================
// LEVEL 04 QUANTUM CALCULATION FUNCTION
// ================================================================================================

function ExtractLevel04QuantumDetails(input: Level04AnalysisInput) -> QuantumDamageExtraction {
  client "openai/gpt-4o"
  prompt #"
    You are a Canadian personal injury quantum assessment expert analyzing comprehensive claim data to extract precise damage details for quantum calculation.
    
    ⚠️ CRITICAL ACCURACY REQUIREMENTS - PREVENT OVERVALUATION:
    
    This system was previously overvaluing pain and suffering by 10.6x actual claims. You must apply conservative, evidence-based assessment using Ontario benchmarks to prevent overvaluation that could lead to unrealistic settlement expectations.
    
    Your task: Analyze ALL available data sources and extract structured damage information for Canadian quantum calculation using CONSERVATIVE, REALISTIC estimates.
    
    Data Sources Available:
    - Claim Reference: {{ input.claimReference }}
    - Province: {{ input.province }}
    - Level 1 Analysis: {{ input.level01Analysis }}
    - Level 2 Coverage: {{ input.level02Coverage }}
    - Level 3 Fault: {{ input.level03Fault }}
    - Email Content: {{ input.emailContent }}
    - SparkNLP Insights: {{ input.sparkNlpInsights }}
    - OCR Text: {{ input.ocrTexts }}
    - Attachments: {{ input.attachmentDetails }}
    
    CRITICAL REQUIREMENTS:
    1. CONSERVATIVE APPROACH: Apply Ontario benchmarks to prevent overvaluation
    2. EVIDENCE-BASED ONLY: Only include damages supported by documentation
    3. REALISTIC CALCULATIONS: Use actual case precedents, not theoretical maximums
    4. HUMAN REVIEW READY: Provide conservative estimates for settlement discussions
    5. VALIDATION REQUIRED: Include validation checks against overvaluation
    
    KEY EXTRACTION AREAS:
    
    A) MEDICAL DAMAGES ASSESSMENT:
    - Extract specific medical costs from all sources (receipts, invoices, treatment records)
    - Identify emergency care, ongoing treatment, diagnostic costs
    - Count documented treatment sessions and calculate costs
    - Assess medical complexity and future treatment needs
    - Factor in documented vs. estimated medical expenses
    
    B) INCOME LOSS ANALYSIS:
    - Extract pre-accident income information
    - Calculate time off work and lost wages
    - Assess impact on earning capacity
    - Consider return-to-work status and limitations
    - Project future income loss if permanent impairment
    
    C) CARE AND ASSISTANCE COSTS:
    - Identify family care assistance provided
    - Calculate economic value of care assistance
    - Assess professional care requirements
    - Factor in housekeeping and personal care needs
    
    D) SPECIAL DAMAGES BREAKDOWN:
    - Extract all out-of-pocket expenses
    - Include transportation, accommodation, prescription costs
    - Account for lost benefits and other financial losses
    - Assess quality and completeness of documentation
    
    E) GENERAL DAMAGES ASSESSMENT (CRITICAL - APPLY CONSERVATIVE ONTARIO BENCHMARKS):
    - MINOR INJURIES (soft tissue, sprains): $5,000 - $15,000 maximum
    - MODERATE INJURIES (partial tears, limited surgery): $15,000 - $45,000 maximum  
    - SEVERE INJURIES (significant surgery, permanent impairment): $45,000 - $150,000
    - CATASTROPHIC INJURIES (life-altering): $150,000 - $420,000 (Andrews cap)
    - SLIP-FALL CASES: Typically $10,000 - $50,000 for moderate injuries
    - USE LOWER END of ranges unless strong evidence supports higher amounts
    - COMPARE to actual documented settlement amounts in similar cases
    - APPLY CONSERVATIVE estimates for settlement and human review purposes
    
    F) FUTURE CARE NEEDS:
    - Assess ongoing medical treatment requirements
    - Estimate future therapy and rehabilitation costs
    - Consider assistive devices and home modifications
    - Project attendant care needs if applicable
    
    G) FAULT IMPACT ANALYSIS:
    - Apply Level 3 fault determination to quantum calculation
    - Calculate reduction factors for different damage categories
    - Consider provincial threshold and no-fault implications
    - Assess net recoverable amounts
    
    H) QUANTUM CALCULATION GUIDANCE WITH VALIDATION:
    - Provide total damage calculations by category
    - Calculate gross and net damage totals (MUST BE REALISTIC)
    - VALIDATE against Ontario benchmarks and actual case precedents
    - IDENTIFY overvaluation risks and recommend adjustments
    - COMPARE to documented actual claims of similar severity
    - Recommend conservative settlement strategy for human review
    - Flag areas requiring expert medical/economic reports
    - Consider litigation risk (overvaluation increases litigation risk)
    
    SPECIAL ATTENTION TO:
    - Medical records and treatment documentation
    - Employment records and income verification
    - Receipts and invoices for out-of-pocket expenses
    - Expert medical opinions and prognosis
    - Functional capacity evaluations
    - Return-to-work assessments
    
    CANADIAN PROVINCIAL CONSIDERATIONS:
    - Ontario: Statutory Accident Benefits, threshold provisions
    - BC: No-fault benefits, Part 7 tort claims
    - Alberta: Section B benefits, minor injury regulation
    - Quebec: SAAQ no-fault system considerations
    - Other provinces: Apply relevant provincial personal injury law
    
    QUANTUM CALCULATION PRINCIPLES:
    - Special damages: Actual financial losses (receipts-based ONLY)
    - General damages: Pain and suffering (Ontario precedent-based, CONSERVATIVE)
    - Future care: Reasonable and necessary care costs (medical evidence required)
    - Loss of earning capacity: Evidence-based projections (conservative assumptions)
    - Mitigation: Duty to minimize damages
    
    ⚠️ VALIDATION REQUIREMENTS:
    - COMPARE all damage amounts to actual Ontario case settlements
    - JUSTIFY any amounts above typical ranges with strong evidence
    - APPLY conservative estimates suitable for settlement discussions
    - IDENTIFY areas where amounts may be inflated
    - RECOMMEND expert reports for amounts requiring validation
    
    🎯 SETTLEMENT FOCUS:
    This analysis is for HUMAN REVIEW and SETTLEMENT PURPOSES. Provide conservative, defensible estimates that:
    - Can be supported in settlement negotiations
    - Reflect realistic recovery expectations
    - Account for litigation costs and risks
    - Are appropriate for reserve adequacy
    
    I) UI METADATA GENERATION (CRITICAL FOR EXPLAINABLE QUANTUM CALCULATION):
    - Generate precise entity mappings for every damage amount extracted
    - Create document highlights with exact coordinates for financial evidence
    - Map each damage component to its source document and position
    - Assign appropriate colors based on damage type and amount ranges
    - Include confidence breakdown for each quantum calculation element
    - Create visual calculation flow for quantum determination process
    
    J) EXPLAINABILITY ANALYSIS (LIME/SHAP FOR QUANTUM CALCULATION):
    - Generate SHAP-style feature importance for damage factors
    - Identify top factors increasing/decreasing quantum amounts
    - Provide confidence contributors for quantum calculations
    - Generate alternative quantum scenarios with amount changes
    - Create visualization data for interactive quantum exploration
    - Include Ontario benchmark factors affecting quantum calculation
    - Explain methodology used for damage amount calculations
    
    UI METADATA REQUIREMENTS:
    - Every damage amount MUST have source document mapping
    - Highlight coordinates MUST be provided for financial evidence
    - Color coding MUST reflect damage type and confidence levels
    - Calculation flow MUST show logical progression of quantum analysis
    - Confidence scores MUST be provided for all calculations
    
    EXPLAINABILITY REQUIREMENTS:
    - Feature importance MUST explain quantum calculation reasoning
    - Alternative scenarios MUST show sensitivity of damage calculations
    - Ontario benchmark factors MUST be integrated into explainability
    - Visualization data MUST enable interactive quantum exploration
    - Methodology notes MUST explain quantum calculation approach
    - Overvaluation prevention measures MUST be clearly documented
    
    Extract comprehensive quantum details ready for Canadian damage calculation and settlement negotiation with complete UI metadata and explainability.
    
    {{ ctx.output_format }}
  "#
}
