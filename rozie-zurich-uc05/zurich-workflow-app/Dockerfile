FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    build-essential \
    git \
    pkg-config \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Install Node.js for BAML CLI
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
    && apt-get install -y nodejs

# Copy common requirements
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Install BAML CLI globally
RUN npm install -g @boundaryml/baml

# Copy BAML models and generate client
COPY baml_models/ ./baml_models/
RUN cd baml_models && baml-cli generate

# Copy all backend source code
COPY backend/ ./backend/

# Set Python path to include the app directory and baml_models
ENV PYTHONPATH=/app:/app/baml_models

# Create logs directory
RUN mkdir -p /app/logs

# Expose default port (can be overridden)
EXPOSE 8000

# Health check (can be customized per service)
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:${PORT:-8000}/api/health || exit 1

# Default command (can be overridden in docker-compose)
CMD ["uvicorn", "backend.src.api.main:app", "--host", "0.0.0.0", "--port", "8000"]