import json
import logging
import time
from datetime import datetime
from typing import Dict, Any, Optional
from functools import wraps

class StructuredLogger:
    """
    Dozzle-compatible structured logger for Zurich Canadian Liability Claims System
    Tracks: AI vs Rule-based decisions, Data validation, Processing metrics
    """
    
    def __init__(self, service_name: str):
        self.service_name = service_name
        self.logger = logging.getLogger(f"zurich.{service_name}")
        
        # Configure for Dozzle (JSON format)
        handler = logging.StreamHandler()
        handler.setFormatter(logging.Formatter('%(message)s'))
        self.logger.addHandler(handler)
        self.logger.setLevel(logging.INFO)
    
    def log_structured(self, 
                      level: str,
                      event_type: str,
                      message: str,
                      **kwargs):
        """Log structured JSON for Dozzle compatibility"""
        log_entry = {
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "service": self.service_name,
            "level": level.upper(),
            "event_type": event_type,
            "message": message,
            **kwargs
        }
        
        if level.upper() == "INFO":
            self.logger.info(json.dumps(log_entry))
        elif level.upper() == "ERROR":
            self.logger.error(json.dumps(log_entry))
        elif level.upper() == "WARNING":
            self.logger.warning(json.dumps(log_entry))
        else:
            self.logger.debug(json.dumps(log_entry))
    
    def log_request_start(self, request_id: str, endpoint: str, data_size: int):
        """Log request initiation"""
        self.log_structured(
            "info", "request_start", 
            f"Processing request {request_id}",
            request_id=request_id,
            endpoint=endpoint,
            data_size_bytes=data_size
        )
    
    def log_ai_decision(self, request_id: str, model: str, prompt_type: str, response_time_ms: float, confidence: Optional[float] = None):
        """Log AI-based decision"""
        self.log_structured(
            "info", "ai_decision",
            f"AI model {model} completed {prompt_type}",
            request_id=request_id,
            decision_type="AI",
            model=model,
            prompt_type=prompt_type,
            response_time_ms=response_time_ms,
            confidence=confidence
        )
    
    def log_rule_decision(self, request_id: str, rule_type: str, rule_name: str, result: Any, processing_time_ms: float):
        """Log rule-based decision"""
        self.log_structured(
            "info", "rule_decision",
            f"Rule-based {rule_type} applied: {rule_name}",
            request_id=request_id,
            decision_type="RULE_BASED",
            rule_type=rule_type,
            rule_name=rule_name,
            result=str(result),
            processing_time_ms=processing_time_ms
        )
    
    def log_data_validation(self, request_id: str, field: str, status: str, value: Any = None, error: str = None):
        """Log data validation results"""
        self.log_structured(
            "info" if status == "valid" else "warning", "data_validation",
            f"Data validation for {field}: {status}",
            request_id=request_id,
            field=field,
            validation_status=status,
            value=str(value) if value is not None else None,
            error=error
        )
    
    def log_processing_metrics(self, request_id: str, stage: str, processing_time_ms: float, success: bool, details: Dict[str, Any] = None):
        """Log processing stage metrics"""
        self.log_structured(
            "info", "processing_metrics",
            f"Stage {stage} completed: {'SUCCESS' if success else 'FAILED'}",
            request_id=request_id,
            stage=stage,
            processing_time_ms=processing_time_ms,
            success=success,
            details=details or {}
        )
    
    def log_request_complete(self, request_id: str, total_time_ms: float, success: bool, final_result: str):
        """Log request completion"""
        self.log_structured(
            "info", "request_complete",
            f"Request {request_id} completed: {'SUCCESS' if success else 'FAILED'}",
            request_id=request_id,
            total_processing_time_ms=total_time_ms,
            success=success,
            final_result=final_result
        )
    
    def log_error(self, request_id: str, error_type: str, error_message: str, stage: str = None):
        """Log errors"""
        self.log_structured(
            "error", "error",
            f"Error in {stage or 'unknown stage'}: {error_message}",
            request_id=request_id,
            error_type=error_type,
            error_message=error_message,
            stage=stage
        )

def track_processing_time(logger: StructuredLogger, request_id: str, stage: str):
    """Decorator to track processing time for functions"""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                end_time = time.time()
                processing_time = (end_time - start_time) * 1000
                logger.log_processing_metrics(request_id, stage, processing_time, True)
                return result
            except Exception as e:
                end_time = time.time()
                processing_time = (end_time - start_time) * 1000
                logger.log_processing_metrics(request_id, stage, processing_time, False, {"error": str(e)})
                raise
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                end_time = time.time()
                processing_time = (end_time - start_time) * 1000
                logger.log_processing_metrics(request_id, stage, processing_time, True)
                return result
            except Exception as e:
                end_time = time.time()
                processing_time = (end_time - start_time) * 1000
                logger.log_processing_metrics(request_id, stage, processing_time, False, {"error": str(e)})
                raise
        
        return async_wrapper if hasattr(func, '__code__') and func.__code__.co_flags & 0x80 else sync_wrapper
    return decorator 