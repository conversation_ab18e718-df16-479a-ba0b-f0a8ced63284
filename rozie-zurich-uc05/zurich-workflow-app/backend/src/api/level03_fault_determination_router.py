"""
ZURICH LEVEL 03 FAULT DETERMINATION - FASTAPI ROUTER
Complete Canadian fault determination using rule-based engine
Integrates with existing Level 1 & 2 analyses stored in Supabase
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Any
import logging
import json
import time
import uuid
from datetime import datetime
import os
import sys

# Import structured logging  
from .structured_logger import StructuredLogger

# Canadian liability engine import  
from .canadian_liability_engine import (
    CanadianFaultDetermination, 
    Province,
    OccupiersLiabilityAssessment
)
from supabase import create_client, Client

# Initialize router
router = APIRouter(prefix="/api/level03-fault", tags=["Level 03 - Fault Determination"])
logger = logging.getLogger(__name__)
structured_logger = StructuredLogger("level03_fault_determination")

# Supabase client
supabase_url = os.getenv("SUPABASE_URL")
supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY") or os.getenv("SUPABASE_ANON_KEY")
supabase: Optional[Client] = None

if supabase_url and supabase_key:
    supabase = create_client(supabase_url, supabase_key)
    logger.info("Supabase connected for Level 03")


# ==================== REQUEST/RESPONSE MODELS ====================

class Level03Request(BaseModel):
    """Level 03 Fault Determination Request"""
    claim_reference: str = Field(..., description="Unique claim identifier")
    province: Optional[str] = Field("Ontario", description="Province where incident occurred")
    accident_type: Optional[str] = Field(None, description="Type of accident (auto/slip_fall/general)")
    
    # Optional override data if not in Supabase
    level01_analysis: Optional[Dict[str, Any]] = None
    level02_coverage: Optional[Dict[str, Any]] = None
    
    # Additional context
    circumstances: Optional[Dict[str, Any]] = Field(
        default_factory=dict,
        description="Additional circumstances (weather, witnesses, etc.)"
    )

class FaultParty(BaseModel):
    """Party involved in the incident"""
    party_id: str
    party_type: str  # driver, pedestrian, property_owner, etc.
    fault_percentage: int = Field(..., ge=0, le=100)
    fault_factors: List[str] = Field(default_factory=list)

class Level03Response(BaseModel):
    """Level 03 Fault Determination Response"""
    claim_reference: str
    processing_timestamp: datetime
    
    # Fault determination
    fault_type: str  # auto_collision, occupiers_liability, general_negligence
    fault_parties: List[FaultParty]
    total_fault_allocated: int  # Should equal 100
    
    # Legal basis
    applicable_law: str
    regulation_reference: str
    precedent_cases: Optional[List[str]] = None
    
    # Analysis details
    accident_classification: str
    contributory_factors: List[str]
    mitigating_factors: List[str]
    
    # Confidence and recommendations
    determination_confidence: float = Field(..., ge=0, le=1)
    requires_investigation: bool
    investigation_points: Optional[List[str]] = None
    
    # Next steps
    proceed_to_quantum: bool
    exit_path: str  # PROCEED_TO_LEVEL04, INVESTIGATION_REQUIRED, DISPUTE_LIKELY
    
    # UI METADATA AND EXPLAINABILITY (NEW)
    uiMetadata: Dict[str, Any]
    explainabilityInsights: List[Dict[str, Any]]


# ==================== HELPER FUNCTIONS ====================

async def fetch_comprehensive_claim_data(claim_reference: str) -> Dict[str, Any]:
    """Fetch comprehensive claim data from Supabase like Level 2 does"""
    if not supabase:
        logger.warning(f"Supabase not available for claim {claim_reference}")
        return _create_minimal_fallback(claim_reference)
    
    try:
        # Fetch claim details including Level 1 & 2 analyses
        # Try different possible column names for the claim identifier
        claims_response = None
        try:
            # First try with claim_id
            claims_response = supabase.table('claims')\
                .select('*, 01_level_analysis, 02_level_analysis, 03_level_analysis')\
                .eq('claim_id', claim_reference)\
                .execute()
        except Exception as e1:
            try:
                # Try with claim_reference
                claims_response = supabase.table('claims')\
                    .select('*, 01_level_analysis, 02_level_analysis, 03_level_analysis')\
                    .eq('claim_reference', claim_reference)\
                    .execute()
            except Exception as e2:
                try:
                    # Try with id
                    claims_response = supabase.table('claims')\
                        .select('*, 01_level_analysis, 02_level_analysis, 03_level_analysis')\
                        .eq('id', claim_reference)\
                        .execute()
                except Exception as e3:
                    # If all attempts fail, use minimal fallback
                    logger.warning(f"Database access not available. Using minimal fallback for claim {claim_reference}")
                    logger.debug(f"Errors: claim_id={e1}, claim_reference={e2}, id={e3}")
                    return _create_minimal_fallback(claim_reference)
        
        # Fetch attachments and OCR text using claim_reference
        attachments_response = None
        try:
            attachments_response = supabase.table('attachments')\
                .select('*')\
                .eq('claim_reference', claim_reference)\
                .execute()
        except Exception as e:
            logger.warning(f"Could not fetch attachments for claim {claim_reference}: {str(e)}")
            attachments_response = None
        
        claim_data = claims_response.data[0] if claims_response and claims_response.data else {}
        attachments_data = attachments_response.data if attachments_response and attachments_response.data else []
        
        # Extract Level 1 analysis from claim data
        level01_analysis = None
        if claim_data.get('01_level_analysis'):
            try:
                # Parse JSON if it's a string, otherwise use directly
                if isinstance(claim_data['01_level_analysis'], str):
                    level01_analysis = json.loads(claim_data['01_level_analysis'])
                else:
                    level01_analysis = claim_data['01_level_analysis']
                logger.info(f"Found Level 1 analysis for claim {claim_reference}")
            except (json.JSONDecodeError, TypeError) as e:
                logger.warning(f"Failed to parse Level 1 analysis for claim {claim_reference}: {str(e)}")
                level01_analysis = None
        
        # Extract Level 2 analysis from claim data
        level02_analysis = None
        if claim_data.get('02_level_analysis'):
            try:
                # Parse JSON if it's a string, otherwise use directly
                if isinstance(claim_data['02_level_analysis'], str):
                    level02_analysis = json.loads(claim_data['02_level_analysis'])
                else:
                    level02_analysis = claim_data['02_level_analysis']
                logger.info(f"Found Level 2 analysis for claim {claim_reference}")
            except (json.JSONDecodeError, TypeError) as e:
                logger.warning(f"Failed to parse Level 2 analysis for claim {claim_reference}: {str(e)}")
                level02_analysis = None
        
        return {
            "claim_details": claim_data,
            "level01_analysis": level01_analysis,
            "level02_analysis": level02_analysis,
            "attachments": attachments_data,
            "ocr_texts": [att.get('ocr_text', '') for att in attachments_data if att.get('ocr_text')],
            "documents": [att.get('file_name', '') for att in attachments_data],
            "fetched_from_supabase": True
        }
        
    except Exception as e:
        logger.error(f"Failed to fetch from Supabase: {str(e)}")
        return _create_minimal_fallback(claim_reference)

def _create_minimal_fallback(claim_reference: str) -> Dict[str, Any]:
    """Create minimal fallback when Supabase is unavailable - returns empty structure"""
    logger.warning(f"Supabase unavailable for claim {claim_reference} - returning empty structure")
    
    return {
        "claim_details": {"claim_reference": claim_reference},
        "level01_analysis": None,
        "level02_analysis": None,
        "attachments": [],
        "ocr_texts": [],
        "documents": [],
        "fetched_from_supabase": False,
        "fallback_data": True
    }


def extract_email_content(supabase_data: Dict[str, Any], level01: Dict[str, Any] = None) -> List[str]:
    """Extract email content from various sources in priority order"""
    email_content = []
    
    try:
        # Priority 1: Direct email_body from claim details
        claim_details = supabase_data.get("claim_details", {})
        if claim_details.get("email_body"):
            email_content.append(claim_details["email_body"])
            logger.info("Email content extracted from claim_details.email_body")
            return email_content
        
        # Priority 2: Email content from Level 1 analysis (if it contains the original email)
        if level01:
            # Check various possible field names where email content might be stored
            for field_name in ["emailContent", "emailBody", "email_body", "body", "originalEmail"]:
                if level01.get(field_name):
                    email_content.append(level01[field_name])
                    logger.info(f"Email content extracted from level01.{field_name}")
                    return email_content
            
            # Check if it's nested in claimDetails
            claim_details_l1 = level01.get("claimDetails", {})
            if claim_details_l1.get("emailContent"):
                email_content.append(claim_details_l1["emailContent"])
                logger.info("Email content extracted from level01.claimDetails.emailContent")
                return email_content
        
        # Priority 3: Attempt to reconstruct from claim description or other narrative fields
        if level01:
            incident_description = level01.get("incidentDescription", "")
            damage_description = level01.get("damageDescription", "")
            if incident_description or damage_description:
                reconstructed = f"{incident_description}\n{damage_description}".strip()
                email_content.append(reconstructed)
                logger.info("Email content reconstructed from incident/damage descriptions")
                return email_content
        
        # Priority 4: Check claim details for any narrative fields
        if claim_details:
            for field_name in ["description", "incident_description", "claim_description", "notes"]:
                if claim_details.get(field_name):
                    email_content.append(claim_details[field_name])
                    logger.info(f"Email content extracted from claim_details.{field_name}")
                    return email_content
        
        logger.warning("No email content found in any source")
        return []
        
    except Exception as e:
        logger.error(f"Error extracting email content: {str(e)}")
        return []


def extract_accident_details(level01: Dict[str, Any], level02: Dict[str, Any] = None, ocr_texts: List[str] = None) -> Dict[str, Any]:
    """Extract accident details from comprehensive Level 1 analysis, Level 2 coverage, and OCR data"""
    
    # Extract from multiple data sources like Level 2 does
    accident_details = {
        "accident_type": "general_liability",
        "specific_type": "general_negligence",
        "location": {},
        "weather": "unknown",
        "witnesses": 0,
        "circumstances": "",
        "incident_type": "unknown"
    }
    
    # Process Level 1 data comprehensively
    if level01:
        # Extract from claimDetails section
        claim_details = level01.get("claimDetails", {})
        if claim_details:
            accident_details["location"] = {
                "incident_location": claim_details.get("incidentLocation", ""),
                "incident_date": claim_details.get("incidentDate", ""),
                "incident_time": claim_details.get("incidentTime", "")
            }
            accident_details["witnesses"] = 1 if claim_details.get("witnessesPresent", False) else 0
            
            # Check for vehicle involvement
            vehicle_involved = claim_details.get("vehicleInvolved")
            if vehicle_involved or claim_details.get("claimType") == "AUTO":
                accident_details["accident_type"] = "auto"
                accident_details["specific_type"] = "general_collision"
            
            # Get damage description for context
            damage_desc = claim_details.get("damageDescription", "").lower()
            accident_details["circumstances"] = damage_desc
        
        # Extract from causeOfLoss section (primary source)
        cause_of_loss = level01.get("causeOfLoss", {})
        if cause_of_loss:
            incident_type = cause_of_loss.get("incidentType", "").upper()
            primary_cause = cause_of_loss.get("primaryCause", "").lower()
            circumstances = cause_of_loss.get("circumstances", "")
            
            # Determine accident type based on incident type
            if incident_type == "AUTO_COLLISION" or "vehicle" in primary_cause or "collision" in primary_cause:
                accident_details["accident_type"] = "auto"
                
                # Determine specific auto type
                if "rear" in primary_cause and "end" in primary_cause:
                    accident_details["specific_type"] = "rear_end_standard"
                elif "left turn" in primary_cause:
                    accident_details["specific_type"] = "intersection_left_turn"
                elif "lane change" in primary_cause:
                    accident_details["specific_type"] = "lane_change_standard"
                elif "parking" in primary_cause:
                    accident_details["specific_type"] = "parking_lot_backing"
                else:
                    accident_details["specific_type"] = "general_collision"
                    
            elif incident_type == "SLIP_FALL" or "slip" in primary_cause or "fall" in primary_cause:
                accident_details["accident_type"] = "slip_fall"
                
                # Determine specific slip/fall type
                if "ice" in primary_cause or "snow" in primary_cause:
                    accident_details["specific_type"] = "ice_snow"
                elif "wet" in primary_cause or "water" in primary_cause:
                    accident_details["specific_type"] = "wet_floor"
                elif "uneven" in primary_cause or "hole" in primary_cause:
                    accident_details["specific_type"] = "uneven_surface"
                else:
                    accident_details["specific_type"] = "premises_liability"
            
            else:
                accident_details["accident_type"] = "general_liability"
                accident_details["specific_type"] = "general_negligence"
            
            accident_details["circumstances"] = circumstances
            accident_details["incident_type"] = incident_type
        
        # Extract weather conditions
        if level01.get("weatherConditions"):
            accident_details["weather"] = level01["weatherConditions"]
        elif cause_of_loss.get("weatherConditions"):
            accident_details["weather"] = cause_of_loss["weatherConditions"]
    
    # Enhance with Level 2 coverage analysis if available
    if level02:
        # Level 2 might have additional insights about the incident
        coverage_analysis = level02.get("analysis_results", {})
        if coverage_analysis and coverage_analysis.get("causeMapping"):
            cause_mapping = coverage_analysis["causeMapping"]
            if hasattr(cause_mapping, 'incidentClassification'):
                accident_details["incident_classification"] = cause_mapping.incidentClassification
    
    # Enhance with OCR text analysis if available
    if ocr_texts:
        combined_ocr = " ".join(ocr_texts).lower()
        
        # Look for additional accident details in OCR text
        if any(word in combined_ocr for word in ["vehicle", "car", "auto", "collision", "rear-end", "intersection"]):
            if accident_details["accident_type"] == "general_liability":
                accident_details["accident_type"] = "auto"
                accident_details["specific_type"] = "general_collision"
        
        elif any(word in combined_ocr for word in ["slip", "fall", "wet floor", "ice", "premises"]):
            if accident_details["accident_type"] == "general_liability":
                accident_details["accident_type"] = "slip_fall"
                accident_details["specific_type"] = "premises_liability"
        
        # Extract weather information from OCR
        if "weather" in combined_ocr:
            if "rain" in combined_ocr:
                accident_details["weather"] = "rainy"
            elif "snow" in combined_ocr or "ice" in combined_ocr:
                accident_details["weather"] = "snow/ice"
            elif "clear" in combined_ocr or "sunny" in combined_ocr:
                accident_details["weather"] = "clear"
    
    logger.info(f"Accident classification result: {accident_details['accident_type']} -> {accident_details['specific_type']}")
    
    return accident_details


def apply_fault_rules(
    province: Province,
    accident_type: str,
    specific_type: str,
    circumstances: Dict[str, Any],
    level01_data: Dict[str, Any] = None,
    level02_data: Dict[str, Any] = None
) -> Dict[str, Any]:
    """Apply provincial fault determination rules with enhanced accuracy using Level 1 & 2 insights"""
    
    if accident_type == "auto":
        # Use auto fault determination with enhanced data
        enhanced_circumstances = {**circumstances}
        
        # Enhance with Level 1 data
        if level01_data and level01_data.get("causeOfLoss"):
            cause_data = level01_data["causeOfLoss"]
            enhanced_circumstances.update({
                "at_fault_parties": cause_data.get("atFaultParties", []),
                "negligence_factors": cause_data.get("negligenceFactors", []),
                "contributing_factors": cause_data.get("contributingFactors", []),
                "traffic_violations": cause_data.get("trafficViolations", [])
            })
        
        # Enhance with Level 2 coverage analysis if available
        if level02_data and level02_data.get("analysis_results"):
            analysis = level02_data["analysis_results"]
            if analysis.get("riskAssessment"):
                risk_data = analysis["riskAssessment"]
                enhanced_circumstances.update({
                    "risk_factors": risk_data.get("riskFactors", []),
                    "liability_indicators": risk_data.get("liabilityIndicators", [])
                })
        
        fault_result = CanadianFaultDetermination.determine_fault(
            specific_type,
            province,
            enhanced_circumstances
        )
        
        # Convert to party format with enhanced factors
        parties = []
        for party_type, fault_pct in fault_result.items():
            fault_factors = enhanced_circumstances.get("negligence_factors", [])
            if party_type in enhanced_circumstances.get("at_fault_parties", []):
                fault_factors.extend(enhanced_circumstances.get("contributory_factors", []))
            
            parties.append({
                "party_id": f"party_{party_type}",
                "party_type": party_type,
                "fault_percentage": fault_pct,
                "fault_factors": fault_factors
            })
            
        return {
            "fault_type": "auto_collision",
            "parties": parties,
            "regulation": f"{province.value} Fault Determination Rules"
        }
        
    elif accident_type == "slip_fall":
        # Use occupiers liability assessment with enhanced data
        enhanced_circumstances = {**circumstances}
        
        # Enhance with Level 1 data for slip/fall cases
        if level01_data:
            # Extract premises details
            if level01_data.get("claimDetails"):
                claim_details = level01_data["claimDetails"]
                enhanced_circumstances.update({
                    "incident_location": claim_details.get("incidentLocation", ""),
                    "property_damage": claim_details.get("propertyDamage", False),
                    "emergency_services": claim_details.get("emergencyServicesInvolved", False),
                    "injuries_reported": claim_details.get("injuriesReported", False)
                })
            
            # Extract cause of loss details for slip/fall
            if level01_data.get("causeOfLoss"):
                cause_data = level01_data["causeOfLoss"]
                enhanced_circumstances.update({
                    "primary_cause": cause_data.get("primaryCause", ""),
                    "negligence_factors": cause_data.get("negligenceFactors", []),
                    "weather_conditions": cause_data.get("weatherConditions"),
                    "causation_chain": cause_data.get("causationChain", [])
                })
                
                # Determine visitor status from circumstances
                if "customer" in cause_data.get("circumstances", "").lower():
                    enhanced_circumstances["visitor_status"] = "invitee"
                elif "employee" in cause_data.get("circumstances", "").lower():
                    enhanced_circumstances["visitor_status"] = "employee"
                else:
                    enhanced_circumstances["visitor_status"] = "invitee"  # Default for commercial premises
        
        # Enhance with Level 2 risk assessment
        if level02_data and level02_data.get("analysis_results"):
            analysis = level02_data["analysis_results"]
            if analysis.get("riskAssessment"):
                risk_data = analysis["riskAssessment"]
                enhanced_circumstances.update({
                    "coverage_risk_factors": risk_data.get("riskFactors", []),
                    "liability_indicators": risk_data.get("liabilityIndicators", [])
                })
        
        # Determine location type and visitor status
        location_type = enhanced_circumstances.get("location_type", "commercial")
        visitor_status = enhanced_circumstances.get("visitor_status", "invitee")
        
        # If we can determine it's a commercial location from Level 1 data
        if level01_data and level01_data.get("policyDetails"):
            policy_details = level01_data["policyDetails"]
            coverage_types = policy_details.get("coverageTypes", [])
            if "Commercial General Liability" in coverage_types:
                location_type = "commercial"
        
        liability_result = OccupiersLiabilityAssessment.assess_liability(
            province,
            location_type,
            specific_type,  # hazard_type
            visitor_status,
            enhanced_circumstances
        )
        
        owner_liability = liability_result["liability_percentage"]
        claimant_contributory = 100 - owner_liability
        
        # Enhanced fault factors from comprehensive analysis
        owner_factors = liability_result["breach_factors"]
        owner_factors.extend(enhanced_circumstances.get("negligence_factors", []))
        
        claimant_factors = enhanced_circumstances.get("claimant_factors", [])
        if enhanced_circumstances.get("weather_conditions"):
            claimant_factors.append(f"Weather conditions: {enhanced_circumstances['weather_conditions']}")
        
        parties = [
            {
                "party_id": "property_owner",
                "party_type": "property_owner", 
                "fault_percentage": owner_liability,
                "fault_factors": list(set(owner_factors))  # Remove duplicates
            },
            {
                "party_id": "claimant",
                "party_type": "visitor",
                "fault_percentage": claimant_contributory,
                "fault_factors": claimant_factors
            }
        ]
        
        return {
            "fault_type": "occupiers_liability",
            "parties": parties,
            "regulation": liability_result["applicable_law"]
        }
        
    else:
        # General negligence - requires investigation
        return {
            "fault_type": "general_negligence",
            "parties": [
                {
                    "party_id": "defendant",
                    "party_type": "defendant",
                    "fault_percentage": 50,
                    "fault_factors": ["Requires investigation"]
                },
                {
                    "party_id": "plaintiff",
                    "party_type": "plaintiff", 
                    "fault_percentage": 50,
                    "fault_factors": ["Requires investigation"]
                }
            ],
            "regulation": "Common law negligence principles"
        }


# ==================== API ENDPOINTS ====================

@router.post("/analyze", response_model=List[Level03Response])
async def analyze_fault(request: Level03Request):
    """
    Perform Level 03 Fault Determination using Canadian rules
    
    This endpoint:
    1. Fetches Level 1 & 2 analyses from Supabase
    2. Applies provincial fault determination rules
    3. Calculates fault percentages for all parties
    4. Provides legal basis and next steps
    """
    request_id = str(uuid.uuid4())
    start_time = time.time()
    
    # Log request start
    structured_logger.log_request_start(
        request_id, 
        "/api/level03-fault/analyze", 
        len(str(request.dict()).encode('utf-8'))
    )
    
    try:
        logger.info(f"Processing Level 03 fault determination for claim {request.claim_reference}")
        
        # Log data validation
        structured_logger.log_data_validation(request_id, "claim_reference", "valid", request.claim_reference)
        structured_logger.log_data_validation(request_id, "province", "valid", request.province)
        
        # Fetch comprehensive data like Level 2 does - including Level 1, Level 2, OCR, attachments
        supabase_data = await fetch_comprehensive_claim_data(request.claim_reference)
        
        # Use override data if provided, otherwise use Supabase data
        level01 = request.level01_analysis or supabase_data.get("level01_analysis", {})
        level02 = request.level02_coverage or supabase_data.get("level02_analysis", {})
        
        # Extract comprehensive data sources
        ocr_texts = supabase_data.get("ocr_texts", [])
        attachments = supabase_data.get("attachments", [])
        documents = supabase_data.get("documents", [])
        
        logger.info(f"Comprehensive data loaded - L1: {'✓' if level01 else '✗'}, L2: {'✓' if level02 else '✗'}, OCR: {len(ocr_texts)}, Docs: {len(documents)}")
        
        # Log data completeness for validation
        data_completeness_score = 0.0
        if level01:
            data_completeness_score += 0.4
        if level02:
            data_completeness_score += 0.3
        if ocr_texts:
            data_completeness_score += 0.2
        if attachments:
            data_completeness_score += 0.1
        
        structured_logger.log_data_validation(request_id, "data_completeness", "valid", f"{data_completeness_score:.2f}")
        
        # Phase 1: AI-Enhanced Fault Factor Extraction
        ai_extraction_start_time = time.time()
        ai_extraction = None
        
        try:
            # Prepare comprehensive data for AI analysis
            ai_input_data = {
                "claimReference": request.claim_reference,
                "province": request.province,
                "level01Analysis": json.dumps(level01) if level01 else "{}",
                "level02Coverage": json.dumps(level02) if level02 else "{}",
                "emailContent": extract_email_content(supabase_data, level01),
                "sparkNlpInsights": json.dumps(level01.get("sparkNlpEnhancedData", {})) if level01 else "{}",
                "ocrTexts": ocr_texts,
                "attachmentDetails": documents
            }
            
            # Call AI extraction using BAML
            from baml_models.baml_client import b
            ai_extraction = b.ExtractLevel03FaultFactors(ai_input_data)
            
            ai_extraction_end_time = time.time()
            
            # Log AI decision
            structured_logger.log_ai_decision(
                request_id,
                "level03_ai_fault_extraction",
                {
                    "model": "gpt-4o",
                    "confidence": ai_extraction.faultGuidance.confidenceInGuidance,
                    "accident_type": ai_extraction.accidentClassification.primaryType,
                    "extraction_approach": "comprehensive_multi_source"
                },
                {
                    "accident_classification": ai_extraction.accidentClassification.primaryType,
                    "specific_subtype": ai_extraction.accidentClassification.specificSubtype,
                    "applicable_law": ai_extraction.accidentClassification.applicableLaw,
                    "suggested_fault_distribution": {
                        "primary": ai_extraction.faultGuidance.suggestedPrimaryFault,
                        "secondary": ai_extraction.faultGuidance.suggestedSecondaryFault
                    },
                    "evidence_quality": ai_extraction.evidenceQuality.overallReliability,
                    "negligence_factors_count": len(ai_extraction.negligenceFactors.propertyOwnerFactors + 
                                                   ai_extraction.negligenceFactors.vehicleOperatorFactors +
                                                   ai_extraction.negligenceFactors.thirdPartyFactors),
                    "uncertainty_areas": ai_extraction.faultGuidance.uncertaintyAreas
                },
                (ai_extraction_end_time - ai_extraction_start_time) * 1000
            )
            
            logger.info(f"AI extraction successful: {ai_extraction.accidentClassification.primaryType} -> {ai_extraction.accidentClassification.specificSubtype} (confidence: {ai_extraction.faultGuidance.confidenceInGuidance:.2f})")
            
        except Exception as e:
            ai_extraction_end_time = time.time()
            logger.error(f"AI extraction failed, falling back to rule-based: {str(e)}")
            ai_extraction = None
            
            # Log AI failure but continue with rule-based approach
            structured_logger.log_error(
                request_id, "ai_extraction_failure", str(e),
                {"fallback_approach": "rule_based_extraction"}
            )
        
        # Phase 2: Enhanced Accident Details Extraction (AI-guided or rule-based fallback)
        rule_start_time = time.time()
        
        if ai_extraction:
            # Use AI-extracted data to enhance accident details
            accident_details = {
                "accident_type": ai_extraction.accidentClassification.primaryType,
                "specific_type": ai_extraction.accidentClassification.specificSubtype,
                "applicable_law": ai_extraction.accidentClassification.applicableLaw,
                "ai_enhanced": True,
                "ai_confidence": ai_extraction.accidentClassification.certaintyLevel,
                "circumstances": ai_extraction.structuredForRules.causationChain,
                "weather": next((f for f in ai_extraction.circumstanceDetails.environmentalFactors.weatherConditions if f), "unknown"),
                "witnesses": 1 if ai_extraction.evidenceQuality.witnessStatements else 0,
                "evidence_quality": ai_extraction.evidenceQuality.overallReliability,
                "fault_guidance": {
                    "primary_fault": ai_extraction.faultGuidance.suggestedPrimaryFault,
                    "secondary_fault": ai_extraction.faultGuidance.suggestedSecondaryFault,
                    "rationale": ai_extraction.faultGuidance.faultRationale,
                    "recommended_rules": ai_extraction.faultGuidance.recommendedRuleSet
                }
            }
            logger.info(f"Using AI-enhanced accident details: {accident_details['accident_type']} -> {accident_details['specific_type']}")
        else:
            # Fallback to traditional rule-based extraction
            accident_details = extract_accident_details(level01, level02, ocr_texts)
            accident_details["ai_enhanced"] = False
            logger.info(f"Using rule-based accident details: {accident_details['accident_type']} -> {accident_details['specific_type']}")
        
        rule_end_time = time.time()
        
        # Log rule-based accident classification
        structured_logger.log_rule_decision(
            request_id, "accident_classification", "extract_accident_details",
            accident_details["accident_type"], (rule_end_time - rule_start_time) * 1000
        )
        
        # Override with request data if provided
        if request.accident_type:
            accident_details["accident_type"] = request.accident_type
            structured_logger.log_data_validation(request_id, "accident_type_override", "valid", request.accident_type)
        
        # Determine province
        province = Province(request.province)
        structured_logger.log_rule_decision(
            request_id, "jurisdiction", "province_determination",
            province.value, 1.0
        )
        
        # Merge circumstances
        circumstances = {
            **accident_details,
            **request.circumstances,
            "weather_conditions": accident_details.get("weather"),
            "witness_count": accident_details.get("witnesses", 0)
        }
        
        # Phase 3: Apply fault determination rules with AI guidance and comprehensive data
        fault_rule_start_time = time.time()
        
        # Enhanced circumstances with AI insights if available
        enhanced_circumstances = {**circumstances}
        if ai_extraction:
            # Add AI-extracted fault factors to circumstances
            enhanced_circumstances.update({
                "ai_guidance": accident_details.get("fault_guidance", {}),
                "negligence_factors": (
                    ai_extraction.negligenceFactors.propertyOwnerFactors +
                    ai_extraction.negligenceFactors.vehicleOperatorFactors +
                    ai_extraction.negligenceFactors.thirdPartyFactors +
                    ai_extraction.negligenceFactors.institutionalFactors
                ),
                "contributory_factors": ai_extraction.contributoryFactors.claimantFactors,
                "mitigating_factors": ai_extraction.contributoryFactors.mitigatingCircumstances,
                "aggravating_factors": ai_extraction.contributoryFactors.aggravatingCircumstances,
                "evidence_quality": ai_extraction.evidenceQuality.overallReliability,
                "visitor_status": ai_extraction.structuredForRules.visitorStatus,
                "location_type": ai_extraction.structuredForRules.locationType,
                "hazard_type": ai_extraction.structuredForRules.hazardType,
                "warnings_posted": ai_extraction.structuredForRules.warningsPosted,
                "traffic_violations": ai_extraction.structuredForRules.trafficViolations,
                "statutory_violations": ai_extraction.negligenceFactors.statutoryViolations,
                "duty_care_breaches": ai_extraction.negligenceFactors.dutyOfCareBreaches
            })
            
            logger.info(f"Enhanced circumstances with AI insights: {len(enhanced_circumstances.get('negligence_factors', []))} negligence factors, evidence quality: {enhanced_circumstances.get('evidence_quality', 0.0):.2f}")
        
        fault_result = apply_fault_rules(
            province,
            accident_details["accident_type"],
            accident_details["specific_type"],
            enhanced_circumstances,
            level01,  # Pass Level 1 data for enhanced analysis
            level02   # Pass Level 2 data for enhanced analysis
        )
        fault_rule_end_time = time.time()
        
        # Log rule-based fault determination
        structured_logger.log_rule_decision(
            request_id, "fault_determination", 
            f"{province.value}_{accident_details['accident_type']}_rules",
            f"fault_type:{fault_result['fault_type']}_parties:{len(fault_result['parties'])}",
            (fault_rule_end_time - fault_rule_start_time) * 1000
        )
        
        # Build fault parties
        fault_parties = []
        for party_data in fault_result["parties"]:
            fault_parties.append(FaultParty(**party_data))
        
        # Calculate total fault (should be 100%)
        total_fault = sum(p.fault_percentage for p in fault_parties)
        
        # Determine confidence and next steps (enhanced with AI insights)
        if ai_extraction:
            # Use AI confidence plus traditional factors
            ai_confidence = ai_extraction.faultGuidance.confidenceInGuidance
            accident_confidence = ai_extraction.accidentClassification.certaintyLevel
            evidence_confidence = ai_extraction.evidenceQuality.overallReliability
            
            # Weighted confidence calculation
            confidence = (ai_confidence * 0.4 + accident_confidence * 0.3 + evidence_confidence * 0.3)
            logger.info(f"AI-enhanced confidence: AI={ai_confidence:.2f}, Accident={accident_confidence:.2f}, Evidence={evidence_confidence:.2f} -> Combined={confidence:.2f}")
        else:
            # Traditional confidence calculation
            confidence = 0.9 if accident_details["accident_type"] in ["auto", "slip_fall"] else 0.7
            logger.info(f"Traditional confidence: {confidence:.2f}")
        
        # Initialize requires_investigation with proper boolean value
        requires_investigation = False
        
        # Check investigation requirements
        if ai_extraction and hasattr(ai_extraction.faultGuidance, 'uncertaintyAreas'):
            uncertainty_count = len(ai_extraction.faultGuidance.uncertaintyAreas) if ai_extraction.faultGuidance.uncertaintyAreas else 0
        else:
            uncertainty_count = 0
            
        requires_investigation = (
            confidence < 0.8 or 
            accident_details["accident_type"] == "general_liability" or
            enhanced_circumstances.get("dispute_potential", False) or
            uncertainty_count > 0
        )
        
        investigation_points = []
        if requires_investigation:
            # Traditional investigation points
            if enhanced_circumstances.get("witness_count", 0) == 0:
                investigation_points.append("No witness statements available")
            if accident_details["weather"] == "unknown":
                investigation_points.append("Weather conditions need verification")
            if accident_details["accident_type"] == "general_liability":
                investigation_points.append("Complex liability - legal review recommended")
            
            # AI-identified uncertainty areas
            if ai_extraction and ai_extraction.faultGuidance.uncertaintyAreas:
                for uncertainty in ai_extraction.faultGuidance.uncertaintyAreas:
                    investigation_points.append(f"AI identified uncertainty: {uncertainty}")
            
            # Evidence quality concerns
            if ai_extraction and ai_extraction.evidenceQuality.overallReliability < 0.6:
                investigation_points.append("Evidence quality below threshold - additional documentation needed")
                if ai_extraction.evidenceQuality.evidenceGaps:
                    for gap in ai_extraction.evidenceQuality.evidenceGaps:
                        investigation_points.append(f"Missing evidence: {gap}")
        
        # Determine exit path
        exit_rule_start_time = time.time()
        if requires_investigation and confidence < 0.7:
            exit_path = "INVESTIGATION_REQUIRED"
            proceed_to_quantum = False
        elif any(p.fault_percentage > 75 for p in fault_parties):
            exit_path = "DISPUTE_LIKELY"
            proceed_to_quantum = True
        else:
            exit_path = "PROCEED_TO_LEVEL04"
            proceed_to_quantum = True
        exit_rule_end_time = time.time()
        
        # Log rule-based exit path decision
        structured_logger.log_rule_decision(
            request_id, "exit_path_determination", "confidence_and_fault_threshold_rules",
            f"exit:{exit_path}_proceed:{proceed_to_quantum}_confidence:{confidence:.2f}",
            (exit_rule_end_time - exit_rule_start_time) * 1000
        )
        
        # Store result in Supabase
        if supabase:
            try:
                result_data = {
                    "claim_reference": request.claim_reference,
                    "03_level_analysis": {
                        "fault_type": fault_result["fault_type"],
                        "fault_allocation": {p.party_id: p.fault_percentage for p in fault_parties},
                        "confidence": confidence,
                        "timestamp": datetime.utcnow().isoformat()
                    }
                }
                
                supabase.table('claims').update(result_data).eq('claim_reference', request.claim_reference).execute()
                logger.info(f"Level 03 analysis stored for claim {request.claim_reference}")
                
            except Exception as e:
                logger.error(f"Failed to store Level 03 analysis: {str(e)}")
        
        # Ensure all required fields have proper values
        contributory_factors = enhanced_circumstances.get("contributory_factors", [])
        if not isinstance(contributory_factors, list):
            contributory_factors = []
            
        mitigating_factors = enhanced_circumstances.get("mitigating_factors", [])
        if not isinstance(mitigating_factors, list):
            mitigating_factors = []
        
        # Ensure requires_investigation is a boolean
        if requires_investigation is None:
            requires_investigation = False
            logger.warning("requires_investigation was None, defaulting to False")
        
        # Build response
        response = Level03Response(
            claim_reference=request.claim_reference,
            processing_timestamp=datetime.utcnow(),
            fault_type=fault_result["fault_type"],
            fault_parties=fault_parties,
            total_fault_allocated=total_fault,
            applicable_law=f"{province.value} {fault_result['fault_type'].replace('_', ' ').title()} Law",
            regulation_reference=fault_result["regulation"],
            precedent_cases=None,  # Would need legal database integration
            accident_classification=accident_details["specific_type"],
            contributory_factors=contributory_factors,
            mitigating_factors=mitigating_factors,
            determination_confidence=confidence,
            requires_investigation=bool(requires_investigation),  # Ensure boolean type
            investigation_points=investigation_points if investigation_points else None,
            proceed_to_quantum=proceed_to_quantum,
            exit_path=exit_path,
            # UI METADATA AND EXPLAINABILITY (NEW)
            uiMetadata={},  # TODO: Add actual UI metadata from BAML analysis
            explainabilityInsights=[]  # TODO: Add actual explainability insights
        )
        
        logger.info(f"Level 03 fault determination completed for claim {request.claim_reference}")
        
        # Log request completion
        total_time = (time.time() - start_time) * 1000
        structured_logger.log_request_complete(
            request_id, total_time, True,
            f"fault_type:{fault_result['fault_type']}_exit:{exit_path}_confidence:{confidence:.2f}"
        )
        
        return [response]
        
    except Exception as e:
        logger.error(f"Error in Level 03 analysis: {str(e)}")
        
        # Log error
        structured_logger.log_error(
            request_id, "fault_determination_error", str(e), "level03_fault_analysis"
        )
        
        # Log failed completion
        total_time = (time.time() - start_time) * 1000
        structured_logger.log_request_complete(
            request_id, total_time, False, f"ERROR: {str(e)[:100]}"
        )
        
        # Return error response in consistent format
        return [Level03Response(
            claim_reference=request.claim_reference,
            processing_timestamp=datetime.utcnow(),
            fault_type="ERROR",
            fault_parties=[],
            total_fault_allocated=0,
            applicable_law="Error",
            regulation_reference="Error", 
            accident_classification="Error",
            contributory_factors=[],
            mitigating_factors=[],
            determination_confidence=0.0,
            requires_investigation=True,
            proceed_to_quantum=False,
            exit_path="ERROR",
            uiMetadata={},
            explainabilityInsights=[]
        )]


@router.get("/fault-rules/{province}")
async def get_provincial_fault_rules(province: str):
    """
    Get fault determination rules for a specific province
    
    Returns the complete set of fault rules and regulations for the specified province.
    """
    try:
        province_enum = Province(province)
        
        # Get the appropriate rules based on province
        if province_enum == Province.ON:
            rules = CanadianFaultDetermination.ONTARIO_FAULT_RULES
        elif province_enum == Province.BC:
            rules = CanadianFaultDetermination.BC_FAULT_RULES
        elif province_enum == Province.AB:
            rules = CanadianFaultDetermination.ALBERTA_FAULT_RULES
        elif province_enum == Province.QC:
            rules = CanadianFaultDetermination.QUEBEC_FAULT_RULES
        else:
            rules = CanadianFaultDetermination.ONTARIO_FAULT_RULES  # Default
        
        return {
            "province": province,
            "fault_rules": rules,
            "regulation": CanadianFaultDetermination._get_fault_regulation(province_enum),
            "special_considerations": {
                "no_fault_benefits": province in ["Ontario", "Quebec"],
                "modified_comparative": province == "Alberta",
                "public_insurance": province in ["Manitoba", "Saskatchewan", "British Columbia"]
            }
        }
        
    except ValueError:
        raise HTTPException(status_code=400, detail=f"Invalid province: {province}")


@router.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "Level 03 - Fault Determination",
        "timestamp": datetime.utcnow().isoformat(),
        "supabase_connected": supabase is not None
    } 