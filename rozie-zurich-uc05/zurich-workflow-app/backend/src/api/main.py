"""
ZURICH BACKEND MAIN APPLICATION
==============================

Purpose: Main FastAPI application combining all Zurich backend services
Services: Email Classification + OCR Wrapper + Future APIs
Architecture: Single FastAPI app with APIRouters for clean separation

Endpoints:
- Email Classification: /api/classify-email, /api/classify-email/simple, /api/health, /api/info
- OCR Processing: /ocr/batch-process, /ocr/health, /ocr/info
- Main App: /, /health (combined health check)

Usage:
- Email Classification: POST http://localhost:8000/api/classify-email
- OCR Processing: POST http://localhost:8000/ocr/batch-process
- Combined Documentation: GET http://localhost:8000/docs
"""

import logging
from datetime import datetime
from typing import Dict, Any
import os

from fastapi import FastAPI, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

# Import the routers
from .email_classification_router import router as email_router
from .ocr_wrapper_router import router as ocr_router
from .supabase_ocr_router import router as supabase_ocr_router
from .level01_analysis_router import router as level01_router
from .level02_coverage_router import router as level02_router
from .level03_fault_determination_router import router as level03_router
from .level04_quantum_calculation_router import router as level04_router
from .ai_explainability_router import router as explainability_router
from .support_router import router as support_router

# ================================================================================================
# LOGGING CONFIGURATION
# ================================================================================================

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/zurich_backend_main.log')
    ]
)
logger = logging.getLogger("ZurichBackendMain")

# ================================================================================================
# MAIN FASTAPI APPLICATION
# ================================================================================================

app = FastAPI(
    title="Zurich Backend Services",
    description="Combined API for Zurich Insurance workflow automation - Email Classification & OCR Processing",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_tags=[
        {
            "name": "email-classification",
            "description": "AI-powered email classification for Canadian insurance claims processing"
        },
        {
            "name": "level01-analysis",
            "description": "Level 01 comprehensive claim analysis with intelligent exit path routing"
        },
        {
            "name": "level02-coverage",
            "description": "Level 02 coverage determination with Canadian legal analysis and intelligent routing"
        },
        {
            "name": "ocr-processing", 
            "description": "Document OCR processing through Zurich OCR service"
        },
        {
            "name": "supabase-ocr-processing",
            "description": "Stream claim files from Supabase storage directly to Zurich OCR API"
        },
        {
            "name": "AI Explainability",
            "description": "AI-driven document highlight generation and explainability for claims"
        },
        {
            "name": "system",
            "description": "System health checks and information"
        }
    ]
)

# ================================================================================================
# MIDDLEWARE CONFIGURATION
# ================================================================================================

# Add CORS middleware for frontend and n8n integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",  # Frontend development server
        "http://localhost:8080",  # Alternative frontend port
        "http://localhost",       # Local frontend
        "*"                       # Allow all for development - restrict in production
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# ================================================================================================
# INCLUDE ROUTERS
# ================================================================================================

# Include email classification router
app.include_router(
    email_router,
    # prefix="/api" is already defined in the router
    tags=["email-classification"]
)

# Include Level 01 analysis router
app.include_router(
    level01_router,
    # prefix="/api" is already defined in the router
    tags=["level01-analysis"]
)

# Include Level 02 coverage router
app.include_router(
    level02_router
    # prefix="/api" is already defined in the router
    # tags are already defined in the router itself
)

# Include Level 03 fault determination router
app.include_router(
    level03_router
    # prefix="/api" is already defined in the router
    # tags are already defined in the router itself
)

# Include Level 04 quantum calculation router
app.include_router(
    level04_router
    # prefix="/api" is already defined in the router
    # tags are already defined in the router itself
)

# Include OCR wrapper router  
app.include_router(
    ocr_router,
    # prefix="/ocr" is already defined in the router
    tags=["ocr-processing"]
)

# Include Supabase OCR router
app.include_router(
    supabase_ocr_router,
    # prefix="/supabase-ocr" is already defined in the router
    tags=["supabase-ocr-processing"]
)

# Include AI Explainability router
app.include_router(
    explainability_router,
    # prefix="/api/ai-explainability" is already defined in the router
    tags=["AI Explainability"]
)

# Include Support System Integration router
app.include_router(
    support_router,
    prefix="/api",
    tags=["support-integration"]
)

# ================================================================================================
# MAIN APPLICATION ENDPOINTS
# ================================================================================================

@app.get("/", tags=["system"])
async def root():
    """Root endpoint with service information and available endpoints"""
    return {
        "service": "Zurich Backend Services",
        "version": "1.0.0",
        "description": "Combined API for Zurich Insurance workflow automation",
        "status": "operational",
        "available_services": {
            "email_classification": {
                "description": "AI-powered email classification for Canadian insurance claims",
                "endpoints": [
                    "/api/classify-email",
                    "/api/classify-email/simple"
                ],
                "health_check": "/api/health",
                "info": "/api/info"
            },
            "level01_analysis": {
                "description": "Comprehensive Level 01 claim analysis with intelligent exit path routing",
                "endpoints": [
                    "/api/level01-analysis",
                    "/api/level01-analysis/simple"
                ],
                "health_check": "/api/level01-analysis/health",
                "info": "/api/level01-analysis/info"
            },
            "level02_coverage": {
                "description": "Level 02 coverage determination with Canadian legal analysis and intelligent routing",
                "endpoints": [
                    "/api/level02-coverage",
                    "/api/level02-coverage/simple"
                ],
                "health_check": "/api/level02-coverage/health",
                "info": "/api/level02-coverage/info"
            },
            "level03_fault": {
                "description": "Level 03 fault determination using Canadian provincial rules",
                "endpoints": [
                    "/api/level03-fault/analyze",
                    "/api/level03-fault/fault-rules/{province}"
                ],
                "health_check": "/api/level03-fault/health",
                "info": "/api/level03-fault/info"
            },
            "level04_quantum": {
                "description": "Level 04 quantum calculation for damages and settlement recommendations",
                "endpoints": [
                    "/api/level04-quantum/calculate"
                ],
                "health_check": "/api/level04-quantum/health", 
                "info": "/api/level04-quantum/info"
            },
            "ocr_processing": {
                "description": "Document OCR processing through Zurich OCR service",
                "endpoints": [
                    "/ocr/batch-process"
                ],
                "health_check": "/ocr/health", 
                "info": "/ocr/info"
            },
            "supabase_ocr_processing": {
                "description": "Stream claim files from Supabase storage directly to Zurich OCR API",
                "endpoints": [
                    "/supabase-ocr/process-claim-files"
                ],
                "health_check": "/supabase-ocr/health",
                "info": "/supabase-ocr/info"
            },
            "ai_explainability": {
                "description": "AI-driven document highlight generation and explainability for claims",
                "endpoints": [
                    "/api/ai-explainability/highlights"
                ],
                "health_check": "/api/ai-explainability/health",
                "info": "/api/ai-explainability/info"
            }
        },
        "documentation": {
            "interactive_docs": "/docs",
            "redoc": "/redoc",
            "openapi_spec": "/openapi.json"
        },
        "system": {
            "combined_health": "/health",
            "timestamp": datetime.now().isoformat()
        }
    }

@app.get("/health", tags=["system"])
async def combined_health_check():
    """Combined health check for all services"""
    health_results = {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "services": {},
        "overall": True
    }
    
    try:
        # Check email classification service
        try:
            # Import here to avoid circular imports
            from baml_models.baml_client import b
            health_results["services"]["email_classification"] = {
                "status": "healthy",
                "baml_client": "available",
                "description": "Email classification service operational"
            }
        except Exception as e:
            health_results["services"]["email_classification"] = {
                "status": "unhealthy", 
                "error": str(e),
                "description": "Email classification service unavailable"
            }
            health_results["overall"] = False
        
        # Check Level 01 analysis service
        try:
            # Same BAML client, but test specific to Level 01 analysis
            health_results["services"]["level01_analysis"] = {
                "status": "healthy",
                "baml_client": "available",
                "description": "Level 01 analysis service operational"
            }
        except Exception as e:
            health_results["services"]["level01_analysis"] = {
                "status": "unhealthy",
                "error": str(e),
                "description": "Level 01 analysis service unavailable"
            }
            health_results["overall"] = False
        
        # Check Level 02 coverage service
        try:
            # Same BAML client, but test specific to Level 02 coverage analysis
            health_results["services"]["level02_coverage"] = {
                "status": "healthy",
                "baml_client": "available",
                "description": "Level 02 coverage analysis service operational"
            }
        except Exception as e:
            health_results["services"]["level02_coverage"] = {
                "status": "unhealthy",
                "error": str(e),
                "description": "Level 02 coverage analysis service unavailable"
            }
            health_results["overall"] = False
        
        # Check Level 03 fault determination service
        try:
            # Same BAML client, but test specific to Level 03 fault determination
            health_results["services"]["level03_fault"] = {
                "status": "healthy",
                "baml_client": "available",
                "description": "Level 03 fault determination service operational"
            }
        except Exception as e:
            health_results["services"]["level03_fault"] = {
                "status": "unhealthy",
                "error": str(e),
                "description": "Level 03 fault determination service unavailable"
            }
            health_results["overall"] = False
        
        # Check Level 04 quantum calculation service
        try:
            # Same BAML client, but test specific to Level 04 quantum calculation
            health_results["services"]["level04_quantum"] = {
                "status": "healthy",
                "baml_client": "available",
                "description": "Level 04 quantum calculation service operational"
            }
        except Exception as e:
            health_results["services"]["level04_quantum"] = {
                "status": "unhealthy",
                "error": str(e),
                "description": "Level 04 quantum calculation service unavailable"
            }
            health_results["overall"] = False
        
        # Check OCR wrapper service  
        try:
            # Basic OCR wrapper health check
            health_results["services"]["ocr_processing"] = {
                "status": "healthy",
                "wrapper_service": "running",
                "description": "OCR wrapper service operational"
            }
        except Exception as e:
            health_results["services"]["ocr_processing"] = {
                "status": "unhealthy",
                "error": str(e), 
                "description": "OCR wrapper service unavailable"
            }
            health_results["overall"] = False
            
        # Check Supabase OCR service
        try:
            # Import here to avoid circular imports
            from .supabase_ocr_router import get_supabase_service
            import httpx
            
            # Test Supabase connection using direct API call (same as the service uses)
            supabase_service = get_supabase_service()
            async with httpx.AsyncClient() as client:
                # Test Supabase API connectivity
                response = await client.post(
                    supabase_service.list_endpoint,
                    headers=supabase_service.headers,
                    json={"prefix": "", "limit": 1},
                    timeout=5.0
                )
                supabase_connected = response.status_code == 200
                
            health_results["services"]["supabase_ocr_processing"] = {
                "status": "healthy" if supabase_connected else "degraded",
                "supabase_connected": supabase_connected,
                "bucket_name": supabase_service.bucket_name,
                "api_endpoint": supabase_service.list_endpoint,
                "description": "Supabase OCR service operational"
            }
            
            if not supabase_connected:
                health_results["overall"] = False
                
        except Exception as e:
            health_results["services"]["supabase_ocr_processing"] = {
                "status": "unhealthy",
                "error": str(e),
                "description": "Supabase OCR service unavailable"
            }
            health_results["overall"] = False
            
        # Update overall status
        health_results["status"] = "healthy" if health_results["overall"] else "degraded"
        
        # Log health check result
        if health_results["overall"]:
            logger.info("✅ Combined health check: All services healthy")
        else:
            logger.warning("⚠️ Combined health check: Some services unhealthy")
            
        # Return appropriate status code
        status_code = status.HTTP_200_OK if health_results["overall"] else status.HTTP_503_SERVICE_UNAVAILABLE
        
        return JSONResponse(
            status_code=status_code,
            content=health_results
        )
        
    except Exception as e:
        logger.error(f"❌ Combined health check failed: {e}")
        
        error_response = {
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "error": str(e),
            "description": "Health check system failure",
            "overall": False
        }
        
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content=error_response
        )

@app.get("/info", tags=["system"])
async def system_info():
    """Get comprehensive system information"""
    return {
        "system": {
            "name": "Zurich Backend Services",
            "version": "1.0.0",
            "description": "Combined API for Zurich Insurance workflow automation",
            "environment": os.getenv("FASTAPI_ENV", "development"),
            "python_version": "3.11+",
            "fastapi_version": "0.100+",
            "deployment": "Docker container"
        },
        "services": {
            "email_classification": {
                "name": "Zurich Email Classification API",
                "version": "1.0.0",
                "description": "AI-powered email classification for Canadian insurance claims processing",
                "models_used": ["gpt-4o", "gpt-4o-mini"],
                "supported_actions": [
                    "PROCEED_TO_ZENDESK",
                    "IGNORE_EMAIL",
                    "REQUEST_ATTACHMENTS", 
                    "HUMAN_REVIEW_REQUIRED"
                ],
                "supported_claim_types": [
                    "LIABILITY", "PROPERTY", "AUTO", "WORKERS_COMPENSATION",
                    "GENERAL_LIABILITY", "PROFESSIONAL_LIABILITY", "NOT_CLAIM_RELATED"
                ],
                "canadian_jurisdictions": [
                    "ALBERTA", "BRITISH_COLUMBIA", "MANITOBA", "NEW_BRUNSWICK",
                    "NEWFOUNDLAND_AND_LABRADOR", "NORTHWEST_TERRITORIES", "NOVA_SCOTIA",
                    "NUNAVUT", "ONTARIO", "PRINCE_EDWARD_ISLAND", "QUEBEC",
                    "SASKATCHEWAN", "YUKON"
                ]
            },
            "level02_coverage": {
                "name": "Zurich Level 02 Coverage Analysis API",
                "version": "1.0.0",
                "description": "Comprehensive coverage determination with Canadian legal analysis",
                "models_used": ["gpt-4o", "gpt-4o-mini"],
                "supported_decisions": [
                    "NOT_COVERED",
                    "COVERED", 
                    "INFORMATION_REQUIRED"
                ],
                "canadian_legal_integration": "CanLII precedent research",
                "information_sources": [
                    "CLAIMANT", "THIRD_PARTY", "INSURANCE_AGENT",
                    "EXPERT_ASSESSMENT", "LEGAL_COUNSEL", "REGULATORY_AUTHORITY"
                ],
                "risk_levels": ["LOW", "MEDIUM", "HIGH", "CRITICAL"]
            },
            "ocr_processing": {
                "name": "Zurich OCR Wrapper API",
                "version": "1.0.0", 
                "description": "n8n-compatible wrapper for Zurich OCR batch processing service",
                "upstream_service": "https://zurich-ocr.dev-scc-demo.rozie.ai",
                "supported_engines": ["google", "azure", "aws"],
                "supported_file_types": [
                    "application/pdf", "image/jpeg", "image/png", "image/tiff",
                    "image/webp", "application/msword",
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                ],
                "max_files_per_request": 10,
                "max_file_size": "50MB",
                "timeout": "300 seconds"
            }
        },
        "integration": {
            "n8n_compatibility": True,
            "cors_enabled": True,
            "documentation": {
                "swagger_ui": "/docs",
                "redoc": "/redoc",
                "openapi_spec": "/openapi.json"
            },
            "health_monitoring": {
                "combined_health": "/health",
                "service_specific": ["/api/health", "/ocr/health"]
            }
        },
        "usage_examples": {
            "email_classification": {
                "endpoint": "/api/classify-email/simple",
                "method": "POST",
                "content_type": "application/json",
                "example_payload": {
                    "subject": "Car accident claim",
                    "body": "I was involved in a car accident yesterday...",
                    "senderEmail": "<EMAIL>",
                    "attachments": []
                }
            },
            "level02_coverage": {
                "endpoint": "/api/level02-coverage/simple",
                "method": "POST",
                "content_type": "application/json",
                "example_payload": {
                    "claim_reference": "ZUR-2024-001",
                    "level01_analysis": {
                        "claimId": "ZUR-2024-001",
                        "claimType": "AUTO",
                        "policyNumber": "POL-123456",
                        "primaryCause": "Vehicle collision",
                        "level01ExitPath": "PROCEED_TO_LEVEL02",
                        "keyFindings": ["Policy verified", "Incident within coverage period"]
                    },
                    "policy_documents": ["Policy terms and conditions..."],
                    "urgency_level": "HIGH"
                }
            },
            "ocr_processing": {
                "endpoint": "/ocr/batch-process",
                "method": "POST", 
                "content_type": "multipart/form-data",
                "parameters": {
                    "files": "List of files to process",
                    "config": '{"ocr_engine": "google", "post_processing": "v1"}'
                }
            }
        },
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/info", tags=["System"])
async def get_api_info():
    """Get API information and capabilities"""
    return {
        "name": "Zurich Insurance Claims Processing API",
        "version": "3.0.0",
        "description": "Complete Canadian liability claims processing with fault determination and quantum calculation",
        "capabilities": {
            "email_classification": {
                "description": "AI-powered email classification using BAML",
                "models": ["GPT-4o", "GPT-4o-mini"],
                "features": ["Multi-language", "Priority detection", "Document analysis"]
            },
            "level01_analysis": {
                "description": "Initial claim intake and document extraction",
                "features": ["Form field extraction", "Document requirements", "Initial risk assessment"]
            },
            "level02_coverage": {
                "description": "Policy coverage determination",
                "features": ["Coverage validation", "Exclusion checking", "Limit analysis"]
            },
            "level03_fault": {
                "description": "Canadian fault determination using provincial rules",
                "features": [
                    "All 13 provinces/territories supported",
                    "Ontario Regulation 668/90 compliance",
                    "Occupiers liability assessment",
                    "Rule-based fault allocation"
                ]
            },
            "level04_quantum": {
                "description": "Damage calculation using Canadian standards",
                "features": [
                    "Provincial medical cost tables",
                    "Income replacement calculations",
                    "Pain & suffering (2024 cap: $400,000)",
                    "Settlement recommendations"
                ]
            },
            "ocr_processing": {
                "description": "Document text extraction and analysis",
                "features": ["Multi-format support", "Supabase integration"]
            }
        },
        "provincial_support": [
            "Ontario", "British Columbia", "Alberta", "Quebec",
            "Manitoba", "Saskatchewan", "Nova Scotia", "New Brunswick",
            "Newfoundland and Labrador", "Prince Edward Island",
            "Northwest Territories", "Yukon", "Nunavut"
        ],
        "compliance": {
            "fault_determination": "Provincial regulations and case law",
            "medical_costs": "Provincial healthcare and insurance standards",
            "privacy": "PIPEDA compliant"
        },
        "api_documentation": "/docs",
        "health_check": "/api/health",
        "environment": os.getenv("ENVIRONMENT", "production"),
        "timestamp": datetime.utcnow().isoformat()
    }

# ================================================================================================
# EXCEPTION HANDLERS
# ================================================================================================

@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc: HTTPException):
    """Handle HTTP exceptions with consistent error format"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": "http_exception",
            "message": exc.detail,
            "status_code": exc.status_code,
            "timestamp": datetime.now().isoformat(),
            "path": str(request.url)
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc: Exception):
    """Handle general exceptions with logging"""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "error": "internal_server_error",
            "message": "An internal server error occurred",
            "timestamp": datetime.now().isoformat(),
            "path": str(request.url)
        }
    )

# ================================================================================================
# APPLICATION STARTUP/SHUTDOWN EVENTS
# ================================================================================================

@app.on_event("startup")
async def startup_event():
    """Application startup event"""
    logger.info("🚀 Zurich Backend Services starting up...")
    logger.info("📧 Email Classification API: Available at /api/classify-email*")
    logger.info("📋 Level 01 Analysis API: Available at /api/level01-analysis*")
    logger.info("🔍 Level 02 Coverage API: Available at /api/level02-coverage*")
    logger.info("⚖️ Level 03 Fault Determination API: Available at /api/level03-fault*")
    logger.info("💰 Level 04 Quantum Calculation API: Available at /api/level04-quantum*")
    logger.info("🔍 OCR Processing API: Available at /ocr/*")
    logger.info("📚 Combined Documentation: Available at /docs")
    logger.info("💚 Combined Health Check: Available at /health")
    logger.info("ℹ️ System Information: Available at /info")
    logger.info("✅ All services ready!")

@app.on_event("shutdown")
async def shutdown_event():
    """Application shutdown event"""
    logger.info("🔄 Zurich Backend Services shutting down...")
    logger.info("👋 Goodbye!")

# ================================================================================================
# MAIN ENTRY POINT
# ================================================================================================

if __name__ == "__main__":
    # This allows running the app directly with: python -m backend.src.api.main
    uvicorn.run(
        "backend.src.api.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    ) 