"""
ZURICH LEVEL 01 ANALYSIS ROUTER
===============================

Purpose: FastAPI router for comprehensive Level 01 claim analysis with intelligent exit path routing
Integration: Receives claim data from n8n, processes using BAML, returns analysis with next workflow step
Models: Uses OpenAI GPT-4o for comprehensive analysis
Flow: Email Classification -> Level 01 Analysis -> Level 02 Coverage Analysis

Endpoints: 
- POST /level01-analysis
- POST /level01-analysis/simple  
- GET /level01-analysis/health
- GET /level01-analysis/info

Usage in n8n:
- HTTP Request node
- POST to {{base_url}}/api/level01-analysis
- Include claim ID, email content, attachments text
- Response determines workflow routing decision based on exit path
"""

import asyncio
import logging
import time
from datetime import datetime
from typing import List, Optional, Dict, Any
import traceback
import re

from fastapi import APIRouter, HTTPException, Request, status
from fastapi.responses import JSONResponse
from pydantic import BaseModel, validator, Field

# Import BAML client (auto-generated)
try:
    from baml_models.baml_client import b
    from baml_models.baml_client.types import ClaimDocumentInput, Level01Analysis
except ImportError as e:
    logging.error(f"BAML client import failed: {e}")
    logging.error("Make sure to run 'baml-cli generate' in the baml_models directory")
    raise

# ================================================================================================
# LOGGING CONFIGURATION
# ================================================================================================

logger = logging.getLogger("ZurichLevel01Analysis")

# ================================================================================================
# ROUTER INITIALIZATION 
# ================================================================================================

router = APIRouter(
    prefix="/api",
    tags=["level01-analysis"],
    responses={
        404: {"description": "Not found"},
        500: {"description": "Internal server error"}
    }
)

# ================================================================================================
# REQUEST/RESPONSE MODELS FOR N8N INTEGRATION
# ================================================================================================

class Level01AnalysisRequest(BaseModel):
    """Model for Level 01 analysis request from n8n"""
    claimId: str = Field(..., description="Unique claim identifier")
    emailContent: str = Field(..., description="Email body content")
    emailSubject: str = Field(..., description="Email subject line")
    attachmentNames: List[str] = Field(default_factory=list, description="Names of all attachments")
    attachmentsText: List[str] = Field(default_factory=list, description="Extracted text from all attachments")
    preprocessingNotes: List[str] = Field(default_factory=list, description="Notes from document preprocessing")
    
    @validator('claimId')
    def validate_claim_id(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError("Claim ID cannot be empty")
        return v.strip()
    
    @validator('emailSubject')
    def validate_subject(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError("Email subject cannot be empty")
        return v.strip()
    
    @validator('emailContent')
    def validate_content(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError("Email content cannot be empty")
        return v.strip()
    
    @validator('attachmentNames', 'attachmentsText')
    def validate_lists_match(cls, v, values):
        # Ensure attachment names and text lists have the same length
        return v

class Level01WorkflowResponse(BaseModel):
    """Simplified response model for n8n workflow routing"""
    exitPath: str = Field(..., description="Next workflow step: DOCUMENTS_INSUFFICIENT, POLICY_LOOKUP_NEEDED, or PROCEED_TO_LEVEL02")
    claimId: str = Field(..., description="Claim identifier")
    policyNumberFound: bool = Field(..., description="Whether policy number was identified")
    documentsSufficient: bool = Field(..., description="Whether documents are sufficient")
    confidenceScore: float = Field(..., description="Overall analysis confidence 0.0-1.0")
    priorityLevel: str = Field(..., description="Processing priority level")
    claimType: str = Field(..., description="Type of claim identified")
    
    # Exit-specific routing information
    nextStepsRequired: List[str] = Field(..., description="Specific next steps required")
    documentsNeeded: List[str] = Field(..., description="Documents needed if insufficient")
    estimatedTimeToResolution: str = Field(..., description="Estimated resolution timeline")
    humanReviewRequired: bool = Field(..., description="Whether human review is needed")
    
    # Brief analysis summary
    analysisReasoning: str = Field(..., description="Brief explanation of routing decision")
    riskIndicators: List[str] = Field(..., description="Key risk factors identified")

class DetailedLevel01Response(BaseModel):
    """Detailed response model with complete Level 01 analysis results"""
    # Primary routing decision
    exitPath: str
    claimId: str
    
    # Core extractions
    claimDetails: Dict[str, Any]
    policyDetails: Dict[str, Any]
    causeOfLoss: Dict[str, Any]
    contactDetails: List[Dict[str, Any]]
    
    # Decision logic results
    documentsSufficient: bool
    policyNumberAvailable: bool
    policyDetailsComplete: bool
    
    # Exit analysis
    exitAnalysis: Dict[str, Any]
    
    # Quality metrics
    confidenceScore: float
    dataQualityScore: float
    processingNotes: List[str]
    
    # Canadian legal context
    canadianJurisdiction: Optional[str]
    legalConsiderations: List[str]
    regulatoryNotes: List[str]
    
    # Enhanced analysis (Spark NLP - future)
    sparkNlpInsights: Optional[Dict[str, Any]]
    
    # UI METADATA AND EXPLAINABILITY (NEW)
    uiMetadata: Dict[str, Any]
    explainabilityInsights: List[Dict[str, Any]]
    
    # Processing metadata
    processingTimeMs: int
    analysisTimestamp: str
    modelVersion: str

class ErrorResponse(BaseModel):
    """Error response model"""
    error: str
    message: str
    timestamp: str
    claimId: Optional[str] = None
    requestId: Optional[str] = None

# ================================================================================================
# UTILITY FUNCTIONS
# ================================================================================================

def sanitize_text_content(content: str) -> str:
    """Clean and sanitize text content for analysis"""
    if not content:
        return ""
    
    # Remove excessive whitespace
    content = re.sub(r'\s+', ' ', content.strip())
    
    # Remove common email artifacts
    artifacts = [
        r'--+\s*\n.*',  # Email signatures
        r'from:\s*.*?sent:\s*.*?to:.*?subject:.*?\n',  # Email headers
        r'this\s+email\s+is\s+confidential.*',
        r'please\s+consider\s+the\s+environment.*',
        r'sent\s+from\s+my\s+(iphone|android|mobile).*'
    ]
    
    for pattern in artifacts:
        content = re.sub(pattern, '', content, flags=re.IGNORECASE | re.DOTALL)
    
    return content.strip()

def validate_attachments_consistency(names: List[str], texts: List[str]) -> bool:
    """Validate that attachment names and texts are consistent"""
    if len(names) != len(texts):
        logger.warning(f"Attachment count mismatch: {len(names)} names vs {len(texts)} texts")
        return False
    return True

def create_baml_input(request: Level01AnalysisRequest) -> ClaimDocumentInput:
    """Convert API request to BAML input object"""
    return ClaimDocumentInput(
        claimId=request.claimId,
        emailContent=sanitize_text_content(request.emailContent),
        emailSubject=sanitize_text_content(request.emailSubject),
        attachmentNames=request.attachmentNames,
        attachmentsText=[sanitize_text_content(text) for text in request.attachmentsText],
        preprocessingNotes=request.preprocessingNotes
    )

def extract_workflow_response(analysis: Level01Analysis) -> Level01WorkflowResponse:
    """Extract simplified workflow response from full analysis"""
    return Level01WorkflowResponse(
        exitPath=analysis.exitPath.value,
        claimId=analysis.claimDetails.claimantName or "Unknown",  # Use appropriate field
        policyNumberFound=analysis.policyNumberAvailable,
        documentsSufficient=analysis.documentsSufficient,
        confidenceScore=analysis.confidenceScore,
        priorityLevel=analysis.exitAnalysis.priorityLevel.value,
        claimType=analysis.claimDetails.claimType.value,
        nextStepsRequired=analysis.exitAnalysis.nextStepsRequired.manualActions + analysis.exitAnalysis.nextStepsRequired.automatedActions,
        documentsNeeded=analysis.exitAnalysis.nextStepsRequired.documentsNeeded,
        estimatedTimeToResolution=analysis.exitAnalysis.estimatedTimeToResolution,
        humanReviewRequired=len(analysis.exitAnalysis.humanReviewItems) > 0,
        analysisReasoning=analysis.exitAnalysis.exitReason,
        riskIndicators=analysis.exitAnalysis.analysisProvided.riskIndicators
    )

# ================================================================================================
# API ENDPOINTS
# ================================================================================================

@router.post("/level01-analysis", 
             response_model=DetailedLevel01Response,
             summary="Comprehensive Level 01 claim analysis",
             description="Perform detailed claim analysis and determine workflow routing")
async def analyze_claim_level01_detailed(request: Level01AnalysisRequest):
    """
    Comprehensive Level 01 claim analysis with intelligent exit path routing.
    
    This endpoint performs a thorough analysis of claim documents and determines
    the appropriate next step in the workflow:
    - DOCUMENTS_INSUFFICIENT: Missing critical documents
    - POLICY_LOOKUP_NEEDED: Policy number found, need complete policy details
    - PROCEED_TO_LEVEL02: Ready for coverage analysis
    """
    start_time = time.time()
    request_timestamp = datetime.now().isoformat()
    
    try:
        logger.info(f"Starting Level 01 analysis for claim {request.claimId}")
        
        # Validate attachment consistency
        if not validate_attachments_consistency(request.attachmentNames, request.attachmentsText):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Attachment names and text lists must have the same length"
            )
        
        # Create BAML input
        baml_input = create_baml_input(request)
        
        # Perform Level 01 analysis using BAML
        logger.info(f"Calling BAML AnalyzeClaimLevel01 for claim {request.claimId}")
        analysis_result = b.AnalyzeClaimLevel01(baml_input)
        
        processing_time_ms = int((time.time() - start_time) * 1000)
        
        logger.info(f"Level 01 analysis completed for claim {request.claimId} in {processing_time_ms}ms")
        logger.info(f"Exit path: {analysis_result.exitPath}, Confidence: {analysis_result.confidenceScore:.2f}")
        
        # Create detailed response
        response = DetailedLevel01Response(
            exitPath=analysis_result.exitPath.value,
            claimId=request.claimId,
            claimDetails=analysis_result.claimDetails.__dict__,
            policyDetails=analysis_result.policyDetails.__dict__,
            causeOfLoss=analysis_result.causeOfLoss.__dict__,
            contactDetails=[contact.__dict__ for contact in analysis_result.contactDetails],
            documentsSufficient=analysis_result.documentsSufficient,
            policyNumberAvailable=analysis_result.policyNumberAvailable,
            policyDetailsComplete=analysis_result.policyDetailsComplete,
            exitAnalysis=analysis_result.exitAnalysis.__dict__,
            confidenceScore=analysis_result.confidenceScore,
            dataQualityScore=analysis_result.dataQualityScore,
            processingNotes=analysis_result.processingNotes,
            canadianJurisdiction=analysis_result.canadianJurisdiction,
            legalConsiderations=analysis_result.legalConsiderations,
            regulatoryNotes=analysis_result.regulatoryNotes,
            sparkNlpInsights=analysis_result.sparkNlpInsights.__dict__ if analysis_result.sparkNlpInsights else None,
            # UI METADATA AND EXPLAINABILITY (NEW)
            uiMetadata=analysis_result.uiMetadata.__dict__,
            explainabilityInsights=[insight.__dict__ for insight in analysis_result.explainabilityInsights],
            processingTimeMs=processing_time_ms,
            analysisTimestamp=request_timestamp,
            modelVersion=analysis_result.modelVersion
        )
        
        return response
        
    except Exception as e:
        processing_time_ms = int((time.time() - start_time) * 1000)
        logger.error(f"Level 01 analysis failed for claim {request.claimId}: {str(e)}")
        logger.error(f"Full traceback: {traceback.format_exc()}")
        
        # Return structured error response
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "level01_analysis_failed",
                "message": f"Failed to analyze claim {request.claimId}: {str(e)}",
                "timestamp": request_timestamp,
                "claimId": request.claimId,
                "processingTimeMs": processing_time_ms
            }
        )

@router.post("/level01-analysis/simple",
             response_model=Level01WorkflowResponse,
             summary="Simple Level 01 analysis for n8n routing",
             description="Simplified endpoint returning only essential workflow routing information")
async def analyze_claim_level01_simple(request: Level01AnalysisRequest):
    """
    Simplified Level 01 analysis optimized for n8n workflow routing.
    
    Returns only the essential information needed for workflow decisions:
    - Exit path routing
    - Key extracted information
    - Next steps required
    - Priority and timeline
    """
    start_time = time.time()
    request_timestamp = datetime.now().isoformat()
    
    try:
        logger.info(f"Starting simple Level 01 analysis for claim {request.claimId}")
        
        # Validate attachment consistency
        if not validate_attachments_consistency(request.attachmentNames, request.attachmentsText):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Attachment names and text lists must have the same length"
            )
        
        # Create BAML input
        baml_input = create_baml_input(request)
        
        # Perform Level 01 analysis using BAML
        analysis_result = b.AnalyzeClaimLevel01(baml_input)
        
        processing_time_ms = int((time.time() - start_time) * 1000)
        
        logger.info(f"Simple Level 01 analysis completed for claim {request.claimId} in {processing_time_ms}ms")
        
        # Extract simplified response
        response = extract_workflow_response(analysis_result)
        
        logger.info(f"Routing decision: {response.exitPath} for claim {request.claimId}")
        
        return response
        
    except Exception as e:
        processing_time_ms = int((time.time() - start_time) * 1000)
        logger.error(f"Simple Level 01 analysis failed for claim {request.claimId}: {str(e)}")
        logger.error(f"Full traceback: {traceback.format_exc()}")
        
        # Return structured error response
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "error": "level01_analysis_failed",
                "message": f"Failed to analyze claim {request.claimId}: {str(e)}",
                "timestamp": request_timestamp,
                "claimId": request.claimId,
                "processingTimeMs": processing_time_ms
            }
        )

@router.get("/level01-analysis/health",
            summary="Level 01 analysis health check",
            description="Check if the Level 01 analysis API and BAML client are working properly")
async def level01_health_check():
    """Health check endpoint for Level 01 analysis service"""
    try:
        # Test BAML client availability
        test_input = ClaimDocumentInput(
            claimId="HEALTH_CHECK",
            emailContent="Test email content",
            emailSubject="Test subject",
            attachmentNames=[],
            attachmentsText=[],
            preprocessingNotes=[]
        )
        
        # Quick test call (with timeout)
        test_start = time.time()
        # Note: For production, you might want to use a lighter test
        # For now, we'll just check if the client is importable
        
        return {
            "status": "healthy",
            "service": "level01-analysis",
            "timestamp": datetime.now().isoformat(),
            "baml_client": "available",
            "test_response_time_ms": int((time.time() - test_start) * 1000),
            "supported_exit_paths": [
                "DOCUMENTS_INSUFFICIENT",
                "POLICY_LOOKUP_NEEDED", 
                "PROCEED_TO_LEVEL02"
            ],
            "analysis_capabilities": [
                "claim_details_extraction",
                "policy_details_extraction",
                "cause_of_loss_analysis",
                "contact_details_extraction",
                "canadian_legal_context",
                "intelligent_routing"
            ]
        }
        
    except Exception as e:
        logger.error(f"Level 01 analysis health check failed: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "status": "unhealthy",
                "service": "level01-analysis",
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
                "baml_client": "unavailable"
            }
        )

@router.get("/level01-analysis/info",
            summary="Level 01 analysis API information",
            description="Get information about Level 01 analysis capabilities and workflow integration")
async def level01_api_info():
    """Information endpoint for Level 01 analysis service"""
    return {
        "service": "Level 01 Claim Analysis",
        "version": "1.0.0",
        "description": "Comprehensive claim analysis with intelligent exit path routing",
        "capabilities": {
            "claim_analysis": {
                "claim_details_extraction": "Extract incident date, location, parties, damages",
                "policy_details_extraction": "Identify policy numbers, coverage, limits, deductibles", 
                "cause_of_loss_analysis": "Determine primary cause, contributing factors, fault",
                "contact_details_extraction": "Extract all relevant party contact information"
            },
            "intelligent_routing": {
                "documents_insufficient": "Route when critical documents are missing",
                "policy_lookup_needed": "Route when policy number found but details needed",
                "proceed_to_level02": "Route when ready for coverage analysis"
            },
            "canadian_context": {
                "provincial_regulations": "Consider provincial insurance differences",
                "legal_factors": "Identify relevant Canadian legal considerations",
                "no_fault_implications": "Factor in no-fault insurance rules"
            },
            "quality_metrics": {
                "confidence_scoring": "Confidence levels for each analysis section",
                "data_quality_assessment": "Quality evaluation of input documents",
                "risk_indicators": "Identification of potential risk factors"
            }
        },
        "workflow_integration": {
            "input_requirements": [
                "claimId (required)",
                "emailContent (required)",
                "emailSubject (required)",
                "attachmentNames (optional)",
                "attachmentsText (optional)",
                "preprocessingNotes (optional)"
            ],
            "exit_paths": {
                "DOCUMENTS_INSUFFICIENT": {
                    "description": "Missing critical documents",
                    "next_action": "Request additional documents from claimant",
                    "automation": "Generate document request email"
                },
                "POLICY_LOOKUP_NEEDED": {
                    "description": "Policy number found, need complete details",
                    "next_action": "Lookup policy in system",
                    "automation": "API call to policy management system"
                },
                "PROCEED_TO_LEVEL02": {
                    "description": "Ready for coverage analysis",
                    "next_action": "Proceed to Level 02 coverage analysis",
                    "automation": "Queue for Level 02 processing"
                }
            }
        },
        "endpoints": {
            "detailed_analysis": "/api/level01-analysis",
            "simple_routing": "/api/level01-analysis/simple",
            "health_check": "/api/level01-analysis/health",
            "info": "/api/level01-analysis/info"
        },
        "models": {
            "primary": "OpenAI GPT-4o",
            "fallback": "OpenAI GPT-4o-mini",
            "enhancement": "Spark NLP (future integration)"
        },
        "performance": {
            "target_response_time": "< 45 seconds",
            "confidence_threshold": "> 0.8 for high-confidence routing",
            "supported_languages": ["English", "French (Quebec)"]
        },
        "timestamp": datetime.now().isoformat()
    } 