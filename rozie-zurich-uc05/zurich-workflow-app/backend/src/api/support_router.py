"""
Support System API Integration Router
Provides secure proxy endpoints for Support System API access
"""

import os
import base64
import httpx
from typing import Optional, Dict, Any, List
from fastapi import APIRouter, HTTPException, Query, Path, Depends
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import logging

# Configure logging
logger = logging.getLogger(__name__)

# Support System Configuration
SUPPORT_SUBDOMAIN = os.getenv("SUPPORT_SUBDOMAIN", "d3v-rozieai5417")
SUPPORT_EMAIL = os.getenv("SUPPORT_EMAIL", "<EMAIL>")
SUPPORT_TOKEN = os.getenv("SUPPORT_TOKEN")
SUPPORT_BASE_URL = f"https://{SUPPORT_SUBDOMAIN}.zendesk.com/api/v2"

# Create router
router = APIRouter(prefix="/support", tags=["support"])

# Pydantic Models
class SupportConnectionStatus(BaseModel):
    success: bool
    connected: bool
    subdomain: Optional[str] = None
    user: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

class SupportTicketsResponse(BaseModel):
    success: bool
    tickets: List[Dict[str, Any]]
    count: Optional[int] = None
    next_page: Optional[str] = None
    previous_page: Optional[str] = None
    error: Optional[str] = None

class SupportUsersResponse(BaseModel):
    success: bool
    users: List[Dict[str, Any]]
    count: Optional[int] = None
    error: Optional[str] = None

# Helper Functions
def get_support_headers() -> Dict[str, str]:
    """Generate Support System API headers with authentication"""
    if not SUPPORT_TOKEN:
        raise HTTPException(status_code=500, detail="Support System API token not configured")

    # Create Basic Auth header
    auth_string = f"{SUPPORT_EMAIL}/token:{SUPPORT_TOKEN}"
    auth_bytes = auth_string.encode('ascii')
    auth_b64 = base64.b64encode(auth_bytes).decode('ascii')

    return {
        'Authorization': f'Basic {auth_b64}',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }

async def make_support_request(endpoint: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """Make authenticated request to Support System API"""
    url = f"{SUPPORT_BASE_URL}{endpoint}"
    headers = get_support_headers()
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(url, headers=headers, params=params or {})
            response.raise_for_status()
            return response.json()
            
    except httpx.HTTPStatusError as e:
        logger.error(f"Support System API HTTP error: {e.response.status_code} - {e.response.text}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"Support System API error: {e.response.text}"
        )
    except httpx.RequestError as e:
        logger.error(f"Support System API request error: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to connect to Support System API: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Unexpected error in Support System request: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Unexpected error: {str(e)}"
        )

# API Endpoints

@router.get("/test", response_model=SupportConnectionStatus)
async def test_support_connection():
    """Test Support System API connection and return user info"""
    try:
        logger.info("Testing Support System API connection...")

        data = await make_support_request("/users/me.json")

        return SupportConnectionStatus(
            success=True,
            connected=True,
            subdomain=SUPPORT_SUBDOMAIN,
            user=data.get("user", {})
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Connection test failed: {str(e)}")
        return SupportConnectionStatus(
            success=False,
            connected=False,
            error=str(e)
        )

@router.get("/tickets", response_model=SupportTicketsResponse)
async def get_tickets(
    per_page: int = Query(default=100, le=100, ge=1),
    sort: Optional[str] = Query(default=None),
    page: Optional[int] = Query(default=None)
):
    """Get all tickets from Support System"""
    try:
        logger.info(f"Fetching tickets with per_page={per_page}")

        params = {"per_page": per_page}
        if sort:
            params["sort"] = sort
        if page:
            params["page"] = page

        data = await make_support_request("/tickets.json", params)

        return SupportTicketsResponse(
            success=True,
            tickets=data.get("tickets", []),
            count=data.get("count"),
            next_page=data.get("next_page"),
            previous_page=data.get("previous_page")
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching tickets: {str(e)}")
        return SupportTicketsResponse(
            success=False,
            tickets=[],
            error=str(e)
        )

@router.get("/tickets/{ticket_id}")
async def get_ticket_by_id(ticket_id: int = Path(..., description="Ticket ID")):
    """Get specific ticket by ID"""
    try:
        logger.info(f"Fetching ticket {ticket_id}")
        
        data = await make_support_request(f"/tickets/{ticket_id}.json")
        
        return {
            "success": True,
            "ticket": data.get("ticket", {}),
            "error": None
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching ticket {ticket_id}: {str(e)}")
        return {
            "success": False,
            "ticket": None,
            "error": str(e)
        }

@router.get("/tickets/{ticket_id}/comments")
async def get_ticket_comments(ticket_id: int = Path(..., description="Ticket ID")):
    """Get comments for a specific ticket"""
    try:
        logger.info(f"Fetching comments for ticket {ticket_id}")
        
        data = await make_support_request(f"/tickets/{ticket_id}/comments.json")
        
        return {
            "success": True,
            "comments": data.get("comments", []),
            "error": None
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching comments for ticket {ticket_id}: {str(e)}")
        return {
            "success": False,
            "comments": [],
            "error": str(e)
        }

@router.get("/users", response_model=SupportUsersResponse)
async def get_users(
    per_page: int = Query(default=100, le=100, ge=1),
    role: Optional[str] = Query(default=None)
):
    """Get all users from Support System"""
    try:
        logger.info(f"Fetching users with per_page={per_page}")

        params = {"per_page": per_page}
        if role:
            params["role"] = role

        data = await make_support_request("/users.json", params)

        return SupportUsersResponse(
            success=True,
            users=data.get("users", []),
            count=data.get("count")
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching users: {str(e)}")
        return SupportUsersResponse(
            success=False,
            users=[],
            error=str(e)
        )

@router.get("/search")
async def search_tickets(
    query: str = Query(..., description="Search query"),
    sort_by: Optional[str] = Query(default=None),
    sort_order: Optional[str] = Query(default=None)
):
    """Search tickets in Support System"""
    try:
        logger.info(f"Searching tickets with query: {query}")

        params = {"query": query}
        if sort_by:
            params["sort_by"] = sort_by
        if sort_order:
            params["sort_order"] = sort_order

        data = await make_support_request("/search.json", params)
        
        # Filter only tickets from search results
        tickets = [
            result for result in data.get("results", [])
            if result.get("result_type") == "ticket"
        ]
        
        return {
            "success": True,
            "tickets": tickets,
            "count": len(tickets),
            "total_count": data.get("count", 0),
            "error": None
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error searching tickets: {str(e)}")
        return {
            "success": False,
            "tickets": [],
            "error": str(e)
        }

@router.get("/health")
async def support_health_check():
    """Health check endpoint for Support System integration"""
    try:
        # Quick connection test
        await make_support_request("/users/me.json")

        return {
            "status": "healthy",
            "service": "support-api",
            "connected": True,
            "subdomain": SUPPORT_SUBDOMAIN,
            "timestamp": "2024-01-20T12:00:00Z"
        }

    except Exception as e:
        return {
            "status": "unhealthy",
            "service": "support-api",
            "connected": False,
            "error": str(e),
            "timestamp": "2024-01-20T12:00:00Z"
        }
