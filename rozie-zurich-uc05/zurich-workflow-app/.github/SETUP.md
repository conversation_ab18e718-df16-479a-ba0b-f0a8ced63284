# 🚀 GitHub Actions Setup Guide

This guide helps you configure GitHub Actions for automated deployment to AWS ECS.

## 📋 **Required GitHub Secrets**

You need to add these secrets to your GitHub repository:

### **1. AWS Credentials**
Go to **Settings → Secrets and variables → Actions** and add:

| Secret Name | Description | How to Get |
|-------------|-------------|------------|
| `AWS_ACCESS_KEY_ID` | AWS Access Key | AWS IAM Console → Create User → Programmatic Access |
| `AWS_SECRET_ACCESS_KEY` | AWS Secret Key | Same as above |
| `AWS_ACCOUNT_ID` | Your AWS Account ID | AWS Console → Top right → Account ID |

### **2. Application Secrets**
These will be managed through the "Update Secrets" workflow, but you can also set them manually:

| Secret Name | Description | Required |
|-------------|-------------|----------|
| `OPENAI_API_KEY` | OpenAI API Key | ✅ Yes |
| `SUPPORT_TOKEN` | Support System API Token | ✅ Yes |
| `SUPABASE_SERVICE_ROLE_KEY` | Supabase Service Role Key | ✅ Yes |
| `SUPABASE_DB_PASSWORD` | Supabase Database Password | ✅ Yes |

## 🔧 **AWS IAM Setup**

### **Step 1: Create IAM User**
1. Go to **AWS IAM Console**
2. Click **Users → Add User**
3. User name: `github-actions-zurich-workflow`
4. Access type: **Programmatic access**
5. Click **Next**

### **Step 2: Attach Policies**
Attach these AWS managed policies:
- `AmazonECS_FullAccess`
- `AmazonEC2ContainerRegistryFullAccess`
- `AmazonVPCFullAccess`
- `IAMFullAccess`
- `AmazonS3FullAccess`
- `SecretsManagerReadWrite`
- `CloudWatchLogsFullAccess`
- `ElasticFileSystemClientFullAccess`

### **Step 3: Custom Policy for Terraform**
Create a custom policy with these permissions:
```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "ec2:*",
                "elasticloadbalancing:*",
                "efs:*",
                "logs:*",
                "secretsmanager:*",
                "iam:*",
                "ecs:*",
                "ecr:*"
            ],
            "Resource": "*"
        }
    ]
}
```

### **Step 4: Save Credentials**
1. Download the **Access Key ID** and **Secret Access Key**
2. Add them to GitHub Secrets
3. Get your **AWS Account ID** from the AWS Console

## 🚀 **Deployment Workflows**

### **1. Main Deployment (`deploy-aws.yml`)**
**Triggers:**
- Push to `main` or `production` branch
- Manual trigger via GitHub Actions

**What it does:**
1. ✅ Builds and tests code
2. 🏗️ Deploys infrastructure with Terraform
3. 🐳 Builds and pushes Docker images to ECR
4. 🚀 Deploys services to ECS

### **2. Update Secrets (`update-secrets.yml`)**
**Triggers:**
- Manual trigger only

**What it does:**
1. 🔐 Updates AWS Secrets Manager with your API keys
2. 🔄 Forces ECS services to restart with new secrets

**Usage:**
1. Go to **Actions → Update AWS Secrets**
2. Click **Run workflow**
3. Enter your API keys and credentials
4. Click **Run workflow**

### **3. Cleanup (`cleanup-aws.yml`)**
**Triggers:**
- Manual trigger only

**What it does:**
1. 🛑 Stops all ECS services
2. 🗑️ Destroys all AWS infrastructure
3. 💰 Stops all AWS charges

**Usage:**
1. Go to **Actions → Cleanup AWS Resources**
2. Type `DESTROY` in the confirmation field
3. Choose whether to keep ECR images
4. Click **Run workflow**

## 📝 **Step-by-Step Deployment**

### **Step 1: Setup Repository**
```bash
# 1. Push your code to GitHub
git add .
git commit -m "Initial commit with GitHub Actions"
git push origin main
```

### **Step 2: Configure Secrets**
1. Go to **GitHub → Settings → Secrets and variables → Actions**
2. Add AWS credentials:
   - `AWS_ACCESS_KEY_ID`
   - `AWS_SECRET_ACCESS_KEY`
   - `AWS_ACCOUNT_ID`

### **Step 3: Deploy Infrastructure**
1. Go to **Actions → Deploy Zurich Workflow to AWS ECS**
2. Click **Run workflow**
3. Select `production` environment
4. Click **Run workflow**
5. Wait ~15-20 minutes for completion

### **Step 4: Update Application Secrets**
1. Go to **Actions → Update AWS Secrets**
2. Click **Run workflow**
3. Enter your API keys:
   - OpenAI API Key
   - Support System Token
   - Supabase Service Role Key
   - Supabase Database Password
4. Click **Run workflow**

### **Step 5: Verify Deployment**
1. Check the workflow logs for the ALB DNS name
2. Visit your application:
   - Frontend: `http://your-alb-dns/`
   - Backend: `http://your-alb-dns/api/`
   - Dashboard: `http://your-alb-dns/dashboard/`
   - n8n: `http://your-alb-dns/n8n/`

## 🔍 **Monitoring & Troubleshooting**

### **Check Deployment Status**
```bash
# View ECS services
aws ecs describe-services --cluster zurich-workflow-cluster --services zurich-workflow-backend zurich-workflow-n8n

# View logs
aws logs tail /ecs/zurich-workflow --follow
```

### **Common Issues**

1. **Secrets not found:**
   - Run the "Update Secrets" workflow
   - Check AWS Secrets Manager console

2. **Services not starting:**
   - Check CloudWatch logs
   - Verify ECR images were pushed

3. **Infrastructure creation failed:**
   - Check Terraform logs in GitHub Actions
   - Verify AWS permissions

## 💰 **Cost Management**

### **Monitor Costs**
- Check AWS Cost Explorer daily
- Set up billing alerts
- Expected cost: ~$136-161/month

### **Stop Charges**
Run the **Cleanup AWS Resources** workflow to destroy all infrastructure and stop charges.

## 🆘 **Support**

If you encounter issues:
1. Check GitHub Actions logs
2. Check AWS CloudWatch logs
3. Verify all secrets are configured
4. Ensure AWS permissions are correct

---

**🎉 Your automated deployment pipeline is ready!**
