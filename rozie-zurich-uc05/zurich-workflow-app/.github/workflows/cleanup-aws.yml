name: 🧹 Cleanup AWS Resources

on:
  workflow_dispatch:
    inputs:
      confirm_destroy:
        description: 'Type "DESTROY" to confirm resource deletion'
        required: true
        type: string
      keep_ecr_images:
        description: 'Keep ECR images (recommended for rollback)'
        required: true
        default: true
        type: boolean

env:
  AWS_REGION: us-east-1
  PROJECT_NAME: zurich-workflow
  DOMAIN_NAME: rozieai-zurich-uc05.com

jobs:
  cleanup-resources:
    name: 🧹 Cleanup AWS Resources
    runs-on: ubuntu-latest
    
    steps:
    - name: ✋ Validate confirmation
      run: |
        if [ "${{ github.event.inputs.confirm_destroy }}" != "DESTROY" ]; then
          echo "❌ Confirmation failed. You must type 'DESTROY' to proceed."
          exit 1
        fi
        echo "✅ Confirmation validated. Proceeding with cleanup..."

    - name: 📥 Checkout code
      uses: actions/checkout@v4

    - name: 🔧 Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: 🏗️ Setup Terraform
      uses: hashicorp/setup-terraform@v3
      with:
        terraform_version: 1.6.0
        terraform_wrapper: false

    - name: 🛑 Stop ECS Services
      run: |
        # Get cluster name
        CLUSTER_NAME=$(aws ecs list-clusters --query 'clusterArns[?contains(@, `'$PROJECT_NAME'`)]' --output text | cut -d'/' -f2)
        
        if [ -n "$CLUSTER_NAME" ]; then
          echo "Stopping ECS services in cluster: $CLUSTER_NAME"
          
          # Scale down services to 0
          for service in backend frontend zendesk-dashboard n8n; do
            SERVICE_NAME="$PROJECT_NAME-$service"
            if aws ecs describe-services --cluster $CLUSTER_NAME --services $SERVICE_NAME --query 'services[0].serviceName' --output text 2>/dev/null | grep -q $SERVICE_NAME; then
              echo "Scaling down service: $SERVICE_NAME"
              aws ecs update-service --cluster $CLUSTER_NAME --service $SERVICE_NAME --desired-count 0
            fi
          done
          
          # Wait for services to scale down
          echo "Waiting for services to scale down..."
          sleep 60
          
          # Delete services
          for service in backend frontend zendesk-dashboard n8n; do
            SERVICE_NAME="$PROJECT_NAME-$service"
            if aws ecs describe-services --cluster $CLUSTER_NAME --services $SERVICE_NAME --query 'services[0].serviceName' --output text 2>/dev/null | grep -q $SERVICE_NAME; then
              echo "Deleting service: $SERVICE_NAME"
              aws ecs delete-service --cluster $CLUSTER_NAME --service $SERVICE_NAME --force
            fi
          done
        else
          echo "No ECS cluster found."
        fi

    - name: 🗑️ Clean ECR Images (Optional)
      if: github.event.inputs.keep_ecr_images == 'false'
      run: |
        echo "Cleaning ECR images..."
        for repo in backend frontend zendesk-dashboard; do
          REPO_NAME="$PROJECT_NAME/$repo"
          if aws ecr describe-repositories --repository-names $REPO_NAME >/dev/null 2>&1; then
            echo "Deleting images in repository: $REPO_NAME"
            aws ecr list-images --repository-name $REPO_NAME --query 'imageIds[*]' --output json | \
            jq '.[] | select(.imageTag != null) | {imageTag: .imageTag}' | \
            jq -s '.' | \
            aws ecr batch-delete-image --repository-name $REPO_NAME --image-ids file:///dev/stdin || true
          fi
        done

    - name: 🏗️ Terraform Destroy
      working-directory: ./aws-deployment/terraform
      run: |
        terraform init
        terraform destroy -auto-approve \
          -var="aws_region=${{ env.AWS_REGION }}" \
          -var="project_name=${{ env.PROJECT_NAME }}" \
          -var="domain_name=${{ env.DOMAIN_NAME }}" \
          -var="environment=production"

    - name: 🧹 Manual Cleanup (Fallback)
      run: |
        echo "Performing manual cleanup of any remaining resources..."
        
        # Clean up any remaining ECS task definitions
        aws ecs list-task-definitions --family-prefix $PROJECT_NAME --query 'taskDefinitionArns[]' --output text | \
        while read arn; do
          if [ -n "$arn" ]; then
            echo "Deregistering task definition: $arn"
            aws ecs deregister-task-definition --task-definition $arn >/dev/null 2>&1 || true
          fi
        done
        
        # Clean up CloudWatch log groups
        aws logs describe-log-groups --log-group-name-prefix "/ecs/$PROJECT_NAME" --query 'logGroups[].logGroupName' --output text | \
        while read log_group; do
          if [ -n "$log_group" ]; then
            echo "Deleting log group: $log_group"
            aws logs delete-log-group --log-group-name "$log_group" || true
          fi
        done

    - name: ✅ Cleanup Summary
      run: |
        echo "🎉 Cleanup completed!"
        echo ""
        echo "🗑️ Resources cleaned up:"
        echo "   ✅ ECS Services stopped and deleted"
        echo "   ✅ ECS Task definitions deregistered"
        echo "   ✅ Infrastructure destroyed via Terraform"
        echo "   ✅ CloudWatch log groups deleted"
        if [ "${{ github.event.inputs.keep_ecr_images }}" == "false" ]; then
          echo "   ✅ ECR images deleted"
        else
          echo "   ⚠️ ECR images preserved (manual cleanup required if needed)"
        fi
        echo ""
        echo "💰 This should stop all AWS charges for this project."
        echo "⚠️ Note: Some resources may take a few minutes to fully terminate."
