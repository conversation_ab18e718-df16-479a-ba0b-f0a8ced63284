name: Deploy Frontend Only

on:
  push:
    branches: [ main, zurich/dinesh/zurich_workflow_uc05 ]
    paths:
      - 'frontend/**'
      - 'nginx/nginx.conf'
      - '.github/workflows/deploy-frontend.yml'
  workflow_dispatch:
    inputs:
      force_deploy:
        description: 'Force deploy frontend'
        required: false
        default: 'false'

env:
  AWS_REGION: us-east-1
  ECR_REPOSITORY_FRONTEND: rozieai-zurich-uc05-frontend
  ECR_REPOSITORY_NGINX: rozieai-zurich-uc05-nginx
  ECS_SERVICE: rozieai-zurich-uc05
  ECS_CLUSTER: dev-scc-demo
  ECS_TASK_DEFINITION: rozieai-zurich-uc05

jobs:
  deploy-frontend:
    name: Deploy Frontend & Nginx
    runs-on: ubuntu-latest
    environment: development

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2

    - name: Build and push Frontend image
      id: build-frontend
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        IMAGE_TAG: ${{ github.sha }}
      run: |
        echo "🏗️ Building Frontend image..."
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY_FRONTEND:$IMAGE_TAG ./frontend
        docker push $ECR_REGISTRY/$ECR_REPOSITORY_FRONTEND:$IMAGE_TAG
        echo "frontend-image=$ECR_REGISTRY/$ECR_REPOSITORY_FRONTEND:$IMAGE_TAG" >> $GITHUB_OUTPUT

    - name: Build and push Nginx image
      id: build-nginx
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        IMAGE_TAG: ${{ github.sha }}
      run: |
        echo "🏗️ Building Nginx image..."
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY_NGINX:$IMAGE_TAG ./nginx
        docker push $ECR_REGISTRY/$ECR_REPOSITORY_NGINX:$IMAGE_TAG
        echo "nginx-image=$ECR_REGISTRY/$ECR_REPOSITORY_NGINX:$IMAGE_TAG" >> $GITHUB_OUTPUT

    - name: Download task definition
      run: |
        aws ecs describe-task-definition --task-definition $ECS_TASK_DEFINITION --query taskDefinition > task-definition.json

    - name: Update Frontend container in task definition
      id: task-def-frontend
      uses: aws-actions/amazon-ecs-render-task-definition@v1
      with:
        task-definition: task-definition.json
        container-name: frontend
        image: ${{ steps.build-frontend.outputs.frontend-image }}

    - name: Update Nginx container in task definition
      id: task-def-nginx
      uses: aws-actions/amazon-ecs-render-task-definition@v1
      with:
        task-definition: ${{ steps.task-def-frontend.outputs.task-definition }}
        container-name: nginx
        image: ${{ steps.build-nginx.outputs.nginx-image }}

    - name: Deploy to Amazon ECS
      uses: aws-actions/amazon-ecs-deploy-task-definition@v1
      with:
        task-definition: ${{ steps.task-def-nginx.outputs.task-definition }}
        service: ${{ env.ECS_SERVICE }}
        cluster: ${{ env.ECS_CLUSTER }}
        wait-for-service-stability: true

    - name: Deployment Success
      run: |
        echo "✅ Frontend deployment completed successfully!"
        echo "🌐 Frontend URL: https://rozieai-zrh-uc05.dev-scc-demo.rozie.ai/"
