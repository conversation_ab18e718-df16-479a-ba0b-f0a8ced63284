name: Deploy Zurich Workflow to AWS (Development)

on:
  push:
    branches: [ main, dev, deployment ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'dev'
        type: choice
        options:
        - dev

env:
  AWS_REGION: us-east-1
  ECR_REGISTRY: ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.us-east-1.amazonaws.com

jobs:
  build-and-deploy:
    name: Build Images and Deploy Infrastructure
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2

    - name: Build and push backend Docker image
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        ECR_REPOSITORY: zurich-workflow/backend
        IMAGE_TAG: latest
      run: |
        echo "🏗️ Building backend Docker image..."
        docker build -f Dockerfile -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
        echo "📤 Pushing backend image to ECR..."
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
        echo "✅ Backend image pushed successfully!"

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'

    - name: Install Serverless Framework
      run: |
        npm install -g serverless@3
        npm install -g serverless-plugin-existing-s3

    - name: Deploy infrastructure with Serverless
      env:
        AWS_ACCOUNT_ID: ${{ secrets.AWS_ACCOUNT_ID }}
        AWS_REGION: ${{ env.AWS_REGION }}
        DOMAIN_NAME: rozieai-zurich-uc05.com
        HOSTED_ZONE_ID: ${{ secrets.HOSTED_ZONE_ID }}
        ACM_CERT_ARN: ${{ secrets.ACM_CERT_ARN }}
      run: |
        cd aws-deployment
        echo "🚀 Deploying Zurich Workflow to AWS..."
        echo "📋 Configuration:"
        echo "   AWS Account ID: $AWS_ACCOUNT_ID"
        echo "   AWS Region: $AWS_REGION"
        echo "   Domain: $DOMAIN_NAME"
        
        sls deploy --config serverless-dev.yml --stage dev --region $AWS_REGION --verbose

    - name: Get deployment outputs
      run: |
        cd aws-deployment
        echo "📊 Deployment Outputs:"
        sls info --config serverless-dev.yml --stage dev --region $AWS_REGION

    - name: Verify deployment
      run: |
        echo "🔍 Verifying deployment..."
        echo "📊 Backend Service Status:"
        aws ecs describe-services \
          --cluster zurich-workflow-dev-cluster \
          --services zurich-workflow-backend-dev-service \
          --region $AWS_REGION \
          --query 'services[0].{Status:status,Running:runningCount,Desired:desiredCount}'

        echo "📊 n8n Service Status:"
        aws ecs describe-services \
          --cluster zurich-workflow-dev-cluster \
          --services zurich-workflow-n8n-dev-service \
          --region $AWS_REGION \
          --query 'services[0].{Status:status,Running:runningCount,Desired:desiredCount}'

        echo "✅ Deployment verification complete!"

    - name: Post deployment summary
      run: |
        echo "🎉 Deployment Summary:"
        echo "✅ Backend Docker image built and pushed to ECR"
        echo "✅ Infrastructure deployed via Serverless Framework"
        echo "✅ ECS service running on Fargate"
        echo ""
        echo "🔗 Access your application:"
        ALB_DNS=$(aws elbv2 describe-load-balancers \
          --names zurich-workflow-dev-alb \
          --region $AWS_REGION \
          --query 'LoadBalancers[0].DNSName' \
          --output text 2>/dev/null || echo "ALB not found")
        
        if [ "$ALB_DNS" != "ALB not found" ]; then
          echo "🌐 Backend API: http://$ALB_DNS"
          echo "🔍 Health Check: http://$ALB_DNS/health"
          echo "📚 API Docs: http://$ALB_DNS/docs"
          echo "🔧 n8n Workflow Editor: http://$ALB_DNS:5678"
          echo "🪝 n8n Webhook Base: http://$ALB_DNS:5678/webhook"
          echo ""
          echo "📋 n8n Setup Instructions:"
          echo "1. Open n8n at: http://$ALB_DNS:5678"
          echo "2. Create your admin account"
          echo "3. Import workflows or create new ones"
          echo "4. Use webhook URL: http://$ALB_DNS:5678/webhook/YOUR_WEBHOOK_ID"
        else
          echo "⚠️  ALB DNS not available yet - check AWS Console"
        fi
