name: 🔐 Update AWS Secrets

on:
  workflow_dispatch:
    inputs:
      openai_api_key:
        description: 'OpenAI API Key'
        required: true
        type: string
      support_token:
        description: 'Support System API Token'
        required: true
        type: string
      supabase_service_role_key:
        description: 'Supabase Service Role Key'
        required: true
        type: string
      supabase_db_password:
        description: 'Supabase Database Password'
        required: true
        type: string
      jwt_secret:
        description: 'JWT Secret (optional - will generate if empty)'
        required: false
        type: string
      session_secret:
        description: 'Session Secret (optional - will generate if empty)'
        required: false
        type: string

env:
  AWS_REGION: us-east-1
  PROJECT_NAME: zurich-workflow

jobs:
  update-secrets:
    name: 🔐 Update Application Secrets
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4

    - name: 🔧 Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: 🔑 Generate secrets if not provided
      id: generate-secrets
      run: |
        JWT_SECRET="${{ github.event.inputs.jwt_secret }}"
        SESSION_SECRET="${{ github.event.inputs.session_secret }}"
        
        if [ -z "$JWT_SECRET" ]; then
          JWT_SECRET=$(openssl rand -base64 32)
          echo "Generated JWT secret"
        fi
        
        if [ -z "$SESSION_SECRET" ]; then
          SESSION_SECRET=$(openssl rand -base64 32)
          echo "Generated session secret"
        fi
        
        echo "jwt_secret=$JWT_SECRET" >> $GITHUB_OUTPUT
        echo "session_secret=$SESSION_SECRET" >> $GITHUB_OUTPUT

    - name: 🔐 Update AWS Secrets Manager
      run: |
        # Create the secrets JSON
        SECRETS_JSON=$(cat << EOF
        {
          "OPENAI_API_KEY": "${{ github.event.inputs.openai_api_key }}",
          "SUPPORT_TOKEN": "${{ github.event.inputs.support_token }}",
          "SUPABASE_SERVICE_ROLE_KEY": "${{ github.event.inputs.supabase_service_role_key }}",
          "JWT_SECRET": "${{ steps.generate-secrets.outputs.jwt_secret }}",
          "SESSION_SECRET": "${{ steps.generate-secrets.outputs.session_secret }}",
          "N8N_DB_POSTGRESDB_HOST": "aws-0-ca-central-1.pooler.supabase.com",
          "N8N_DB_POSTGRESDB_PORT": "6543",
          "N8N_DB_POSTGRESDB_DATABASE": "postgres",
          "N8N_DB_POSTGRESDB_USER": "postgres.tlduggpohclrgxbvuzhd",
          "N8N_DB_POSTGRESDB_PASSWORD": "03BgIvVhAeCaMF14"
        }
        EOF
        )
        
        # Update the secret
        aws secretsmanager update-secret \
          --secret-id $PROJECT_NAME-app-secrets \
          --secret-string "$SECRETS_JSON"
        
        echo "✅ Secrets updated successfully!"

    - name: 🔄 Force ECS service update
      run: |
        # Get cluster name
        CLUSTER_NAME=$(aws ecs list-clusters --query 'clusterArns[?contains(@, `'$PROJECT_NAME'`)]' --output text | cut -d'/' -f2)
        
        if [ -n "$CLUSTER_NAME" ]; then
          echo "Forcing service updates to pick up new secrets..."
          
          # Update backend service
          aws ecs update-service \
            --cluster $CLUSTER_NAME \
            --service $PROJECT_NAME-backend \
            --force-new-deployment || echo "Backend service not found"
          
          # Update n8n service
          aws ecs update-service \
            --cluster $CLUSTER_NAME \
            --service $PROJECT_NAME-n8n \
            --force-new-deployment || echo "n8n service not found"
          
          echo "✅ Services updated to use new secrets!"
        else
          echo "⚠️ No ECS cluster found. Secrets updated but services not restarted."
        fi

    - name: ✅ Summary
      run: |
        echo "🎉 Secrets update completed!"
        echo ""
        echo "📋 Updated secrets:"
        echo "   ✅ OpenAI API Key"
        echo "   ✅ Support System Token"
        echo "   ✅ Supabase Service Role Key"
        echo "   ✅ Supabase Database Password"
        echo "   ✅ JWT Secret"
        echo "   ✅ Session Secret"
        echo "   ✅ n8n Database Configuration"
        echo ""
        echo "🔄 ECS services will restart with new secrets within 5-10 minutes."
