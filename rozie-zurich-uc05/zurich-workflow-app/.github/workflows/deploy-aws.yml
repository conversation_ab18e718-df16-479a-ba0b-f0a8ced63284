name: 🚀 Deploy Zurich Workflow to AWS ECS

on:
  push:
    branches: [ main, production ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'production'
        type: choice
        options:
        - production
        - staging

env:
  AWS_REGION: us-east-1
  PROJECT_NAME: zurich-workflow
  DOMAIN_NAME: rozieai-zurich-uc05.com
  ECR_REGISTRY: ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.us-east-1.amazonaws.com

jobs:
  # =============================================================================
  # BUILD AND TEST
  # =============================================================================
  build-and-test:
    name: 🔨 Build & Test
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4

    - name: 🐍 Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: 📦 Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: 🧪 Run Python tests
      run: |
        # Add your test commands here
        echo "Running Python tests..."
        # python -m pytest tests/ -v

    - name: 🟢 Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'

    - name: 📦 Install Node.js dependencies
      working-directory: ./zurich-dashboard
      run: |
        npm ci

    - name: 🧪 Run Node.js tests
      working-directory: ./zurich-dashboard
      run: |
        # Add your test commands here
        echo "Running Node.js tests..."
        # npm test

    - name: ✅ Build validation
      run: |
        echo "✅ All builds and tests passed!"

  # =============================================================================
  # INFRASTRUCTURE DEPLOYMENT WITH SERVERLESS
  # =============================================================================
  deploy-infrastructure:
    name: 🏗️ Deploy Infrastructure
    runs-on: ubuntu-latest
    needs: build-and-test
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/production'

    outputs:
      cluster-name: ${{ steps.serverless.outputs.cluster-name }}
      alb-dns: ${{ steps.serverless.outputs.alb-dns }}

    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4

    - name: 🔧 Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: 🟢 Set up Node.js for Serverless
      uses: actions/setup-node@v4
      with:
        node-version: '18'

    - name: 📦 Install Serverless Framework v3
      run: |
        npm install -g serverless@3

    - name: 🚀 Deploy Development Infrastructure
      working-directory: ./aws-deployment
      env:
        AWS_ACCOUNT_ID: ${{ secrets.AWS_ACCOUNT_ID }}
        AWS_REGION: ${{ env.AWS_REGION }}
      run: |
        chmod +x deploy-dev.sh
        ./deploy-dev.sh

    - name: 📤 Export Development Outputs
      id: serverless
      working-directory: ./aws-deployment
      run: |
        source deployment-outputs-dev.env
        echo "cluster-name=$ECS_CLUSTER_NAME" >> $GITHUB_OUTPUT
        echo "alb-dns=$ALB_DNS_NAME" >> $GITHUB_OUTPUT
        echo "backend-url=$BACKEND_URL" >> $GITHUB_OUTPUT
        echo "vpc-id=$VPC_ID" >> $GITHUB_OUTPUT

  # =============================================================================
  # BUILD AND PUSH DOCKER IMAGES
  # =============================================================================
  build-and-push-images:
    name: 🐳 Build & Push Images
    runs-on: ubuntu-latest
    needs: [build-and-test, deploy-infrastructure]
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/production'
    
    strategy:
      matrix:
        service: [backend, frontend, dashboard]
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4

    - name: 🔧 Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: 🔐 Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2

    - name: 🐳 Build and push Docker image - Backend
      if: matrix.service == 'backend'
      run: |
        docker build -t $ECR_REGISTRY/$PROJECT_NAME/backend:latest .
        docker push $ECR_REGISTRY/$PROJECT_NAME/backend:latest
        docker tag $ECR_REGISTRY/$PROJECT_NAME/backend:latest $ECR_REGISTRY/$PROJECT_NAME/backend:${{ github.sha }}
        docker push $ECR_REGISTRY/$PROJECT_NAME/backend:${{ github.sha }}

    - name: 🐳 Build and push Docker image - Frontend
      if: matrix.service == 'frontend'
      run: |
        docker build -f frontend.Dockerfile -t $ECR_REGISTRY/$PROJECT_NAME/frontend:latest .
        docker push $ECR_REGISTRY/$PROJECT_NAME/frontend:latest
        docker tag $ECR_REGISTRY/$PROJECT_NAME/frontend:latest $ECR_REGISTRY/$PROJECT_NAME/frontend:${{ github.sha }}
        docker push $ECR_REGISTRY/$PROJECT_NAME/frontend:${{ github.sha }}

    - name: 🐳 Build and push Docker image - Dashboard
      if: matrix.service == 'dashboard'
      run: |
        docker build -f zurich-dashboard/Dockerfile -t $ECR_REGISTRY/$PROJECT_NAME/dashboard:latest ./zurich-dashboard
        docker push $ECR_REGISTRY/$PROJECT_NAME/dashboard:latest
        docker tag $ECR_REGISTRY/$PROJECT_NAME/dashboard:latest $ECR_REGISTRY/$PROJECT_NAME/dashboard:${{ github.sha }}
        docker push $ECR_REGISTRY/$PROJECT_NAME/dashboard:${{ github.sha }}

  # =============================================================================
  # DEPLOY ECS SERVICES
  # =============================================================================
  deploy-services:
    name: 🚀 Deploy ECS Services
    runs-on: ubuntu-latest
    needs: [deploy-infrastructure, build-and-push-images]
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/production'
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4

    - name: 🔧 Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: 🔧 Get Terraform outputs
      working-directory: ./aws-deployment/terraform
      run: |
        terraform init
        echo "ECS_CLUSTER_NAME=$(terraform output -raw ecs_cluster_name)" >> $GITHUB_ENV
        echo "ECS_TASK_EXECUTION_ROLE_ARN=$(terraform output -raw ecs_task_execution_role_arn)" >> $GITHUB_ENV
        echo "ECS_TASK_ROLE_ARN=$(terraform output -raw ecs_task_role_arn)" >> $GITHUB_ENV
        echo "APP_SECRETS_ARN=$(terraform output -raw app_secrets_secret_arn)" >> $GITHUB_ENV
        echo "EFS_FILE_SYSTEM_ID=$(terraform output -raw efs_file_system_id)" >> $GITHUB_ENV
        echo "EFS_ACCESS_POINT_ID=$(terraform output -raw efs_access_point_id)" >> $GITHUB_ENV
        echo "BACKEND_TARGET_GROUP_ARN=$(terraform output -raw backend_target_group_arn)" >> $GITHUB_ENV
        echo "FRONTEND_TARGET_GROUP_ARN=$(terraform output -raw frontend_target_group_arn)" >> $GITHUB_ENV
        echo "DASHBOARD_TARGET_GROUP_ARN=$(terraform output -raw dashboard_target_group_arn)" >> $GITHUB_ENV
        echo "N8N_TARGET_GROUP_ARN=$(terraform output -raw n8n_target_group_arn)" >> $GITHUB_ENV
        echo "PRIVATE_SUBNET_IDS=$(terraform output -json private_subnet_ids | jq -r '.[]' | tr '\n' ',' | sed 's/,$//')" >> $GITHUB_ENV
        echo "ECS_SECURITY_GROUP_ID=$(terraform output -raw ecs_security_group_id)" >> $GITHUB_ENV

    - name: 📝 Create Backend Task Definition
      run: |
        cat > backend-task-definition.json << EOF
        {
          "family": "$PROJECT_NAME-backend",
          "networkMode": "awsvpc",
          "requiresCompatibilities": ["FARGATE"],
          "cpu": "1024",
          "memory": "2048",
          "executionRoleArn": "$ECS_TASK_EXECUTION_ROLE_ARN",
          "taskRoleArn": "$ECS_TASK_ROLE_ARN",
          "containerDefinitions": [
            {
              "name": "backend",
              "image": "$ECR_REGISTRY/$PROJECT_NAME/backend:${{ github.sha }}",
              "portMappings": [{"containerPort": 8000, "protocol": "tcp"}],
              "essential": true,
              "environment": [
                {"name": "SERVICE_NAME", "value": "zurich-backend"},
                {"name": "PORT", "value": "8000"},
                {"name": "FASTAPI_ENV", "value": "production"},
                {"name": "LOG_LEVEL", "value": "INFO"},
                {"name": "SUPABASE_URL", "value": "https://tlduggpohclrgxbvuzhd.supabase.co"},
                {"name": "SUPABASE_ANON_KEY", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRsZHVnZ3BvaGNscmd4YnZ1emhkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA3MDEwMzgsImV4cCI6MjA2NjI3NzAzOH0.Eud_SDfGulErh3yhqjaIMqM37eghuz-PVeRxknzxRfk"},
                {"name": "SUPPORT_SUBDOMAIN", "value": "d3v-rozieai5417"},
                {"name": "SUPPORT_EMAIL", "value": "<EMAIL>"}
              ],
              "secrets": [
                {"name": "OPENAI_API_KEY", "valueFrom": "$APP_SECRETS_ARN:OPENAI_API_KEY::"},
                {"name": "SUPPORT_TOKEN", "valueFrom": "$APP_SECRETS_ARN:SUPPORT_TOKEN::"},
                {"name": "SUPABASE_SERVICE_ROLE_KEY", "valueFrom": "$APP_SECRETS_ARN:SUPABASE_SERVICE_ROLE_KEY::"}
              ],
              "logConfiguration": {
                "logDriver": "awslogs",
                "options": {
                  "awslogs-group": "/ecs/$PROJECT_NAME",
                  "awslogs-region": "$AWS_REGION",
                  "awslogs-stream-prefix": "backend"
                }
              }
            }
          ]
        }
        EOF

    - name: 📝 Create n8n Task Definition
      run: |
        cat > n8n-task-definition.json << EOF
        {
          "family": "$PROJECT_NAME-n8n",
          "networkMode": "awsvpc",
          "requiresCompatibilities": ["FARGATE"],
          "cpu": "512",
          "memory": "1024",
          "executionRoleArn": "$ECS_TASK_EXECUTION_ROLE_ARN",
          "taskRoleArn": "$ECS_TASK_ROLE_ARN",
          "volumes": [
            {
              "name": "n8n-data",
              "efsVolumeConfiguration": {
                "fileSystemId": "$EFS_FILE_SYSTEM_ID",
                "accessPointId": "$EFS_ACCESS_POINT_ID",
                "transitEncryption": "ENABLED"
              }
            }
          ],
          "containerDefinitions": [
            {
              "name": "n8n",
              "image": "n8nio/n8n:latest",
              "portMappings": [{"containerPort": 5678, "protocol": "tcp"}],
              "essential": true,
              "environment": [
                {"name": "N8N_HOST", "value": "0.0.0.0"},
                {"name": "N8N_PORT", "value": "5678"},
                {"name": "N8N_PROTOCOL", "value": "http"},
                {"name": "NODE_ENV", "value": "production"},
                {"name": "GENERIC_TIMEZONE", "value": "UTC"},
                {"name": "DB_TYPE", "value": "postgresdb"}
              ],
              "secrets": [
                {"name": "DB_POSTGRESDB_HOST", "valueFrom": "$APP_SECRETS_ARN:N8N_DB_POSTGRESDB_HOST::"},
                {"name": "DB_POSTGRESDB_PORT", "valueFrom": "$APP_SECRETS_ARN:N8N_DB_POSTGRESDB_PORT::"},
                {"name": "DB_POSTGRESDB_DATABASE", "valueFrom": "$APP_SECRETS_ARN:N8N_DB_POSTGRESDB_DATABASE::"},
                {"name": "DB_POSTGRESDB_USER", "valueFrom": "$APP_SECRETS_ARN:N8N_DB_POSTGRESDB_USER::"},
                {"name": "DB_POSTGRESDB_PASSWORD", "valueFrom": "$APP_SECRETS_ARN:N8N_DB_POSTGRESDB_PASSWORD::"}
              ],
              "mountPoints": [
                {"sourceVolume": "n8n-data", "containerPath": "/home/<USER>/.n8n"}
              ],
              "logConfiguration": {
                "logDriver": "awslogs",
                "options": {
                  "awslogs-group": "/ecs/$PROJECT_NAME",
                  "awslogs-region": "$AWS_REGION",
                  "awslogs-stream-prefix": "n8n"
                }
              }
            }
          ]
        }
        EOF

    - name: 🚀 Register and Deploy Backend Service
      run: |
        # Register task definition
        aws ecs register-task-definition --cli-input-json file://backend-task-definition.json
        
        # Update or create service
        if aws ecs describe-services --cluster $ECS_CLUSTER_NAME --services $PROJECT_NAME-backend --query 'services[0].serviceName' --output text 2>/dev/null | grep -q $PROJECT_NAME-backend; then
          echo "Updating existing backend service..."
          aws ecs update-service \
            --cluster $ECS_CLUSTER_NAME \
            --service $PROJECT_NAME-backend \
            --task-definition $PROJECT_NAME-backend \
            --force-new-deployment
        else
          echo "Creating new backend service..."
          aws ecs create-service \
            --cluster $ECS_CLUSTER_NAME \
            --service-name $PROJECT_NAME-backend \
            --task-definition $PROJECT_NAME-backend \
            --desired-count 2 \
            --launch-type FARGATE \
            --network-configuration "awsvpcConfiguration={subnets=[$PRIVATE_SUBNET_IDS],securityGroups=[$ECS_SECURITY_GROUP_ID],assignPublicIp=DISABLED}" \
            --load-balancers "targetGroupArn=$BACKEND_TARGET_GROUP_ARN,containerName=backend,containerPort=8000"
        fi

    - name: 🚀 Register and Deploy n8n Service
      run: |
        # Register task definition
        aws ecs register-task-definition --cli-input-json file://n8n-task-definition.json
        
        # Update or create service
        if aws ecs describe-services --cluster $ECS_CLUSTER_NAME --services $PROJECT_NAME-n8n --query 'services[0].serviceName' --output text 2>/dev/null | grep -q $PROJECT_NAME-n8n; then
          echo "Updating existing n8n service..."
          aws ecs update-service \
            --cluster $ECS_CLUSTER_NAME \
            --service $PROJECT_NAME-n8n \
            --task-definition $PROJECT_NAME-n8n \
            --force-new-deployment
        else
          echo "Creating new n8n service..."
          aws ecs create-service \
            --cluster $ECS_CLUSTER_NAME \
            --service-name $PROJECT_NAME-n8n \
            --task-definition $PROJECT_NAME-n8n \
            --desired-count 1 \
            --launch-type FARGATE \
            --network-configuration "awsvpcConfiguration={subnets=[$PRIVATE_SUBNET_IDS],securityGroups=[$ECS_SECURITY_GROUP_ID],assignPublicIp=DISABLED}" \
            --load-balancers "targetGroupArn=$N8N_TARGET_GROUP_ARN,containerName=n8n,containerPort=5678"
        fi

    - name: ⏳ Wait for services to stabilize
      run: |
        echo "Waiting for services to stabilize..."
        aws ecs wait services-stable --cluster $ECS_CLUSTER_NAME --services $PROJECT_NAME-backend $PROJECT_NAME-n8n

    - name: ✅ Deployment Summary
      run: |
        cd aws-deployment/terraform
        DOMAIN_NAME=$(terraform output -raw domain_name)
        ALB_DNS=$(terraform output -raw alb_dns_name)
        NAMESERVERS=$(terraform output -json nameservers)

        echo "🎉 Deployment completed successfully!"
        echo ""
        echo "🌐 Your Custom Domain: $DOMAIN_NAME"
        echo ""
        echo "📋 Application URLs:"
        echo "   🏠 Main App: https://$DOMAIN_NAME"
        echo "   🔧 Backend API: https://$DOMAIN_NAME/api"
        echo "   📊 Dashboard: https://$DOMAIN_NAME/dashboard"
        echo "   🔄 n8n Workflows: https://$DOMAIN_NAME/n8n"
        echo ""
        echo "🌐 Alternative Subdomain URLs:"
        echo "   🔧 API: https://api.$DOMAIN_NAME"
        echo "   📊 Dashboard: https://dashboard.$DOMAIN_NAME"
        echo "   🔄 n8n: https://n8n.$DOMAIN_NAME"
        echo ""
        echo "⚠️  IMPORTANT: DNS Setup Required!"
        echo "   If you created a new hosted zone, update your domain registrar with these nameservers:"
        if [ "$NAMESERVERS" != "null" ]; then
          echo "$NAMESERVERS" | jq -r '.[]' | sed 's/^/   - /'
        else
          echo "   (Using existing hosted zone - no nameserver changes needed)"
        fi
        echo ""
        echo "🔍 Technical Details:"
        echo "   ALB DNS: $ALB_DNS"
        echo "   SSL Certificate: Automatically provisioned"
        echo "   HTTP → HTTPS: Automatic redirect"
        echo ""
        echo "📊 Service Status:"
        aws ecs describe-services --cluster $ECS_CLUSTER_NAME --services $PROJECT_NAME-backend $PROJECT_NAME-n8n --query 'services[*].[serviceName,runningCount,desiredCount]' --output table
