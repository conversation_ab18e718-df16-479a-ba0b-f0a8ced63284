name: Manual Deploy Backend Only

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'dev'
        type: choice
        options:
          - dev
          - staging
          - prod
      force_rebuild:
        description: 'Force rebuild even if no changes'
        required: false
        default: 'true'
        type: boolean

env:
  AWS_REGION: ca-central-1
  ECS_SERVICE: rozieai-zurich-uc05
  ECS_CLUSTER: dev-scc-demo
  ECS_TASK_DEFINITION: rozieai-zurich-uc05

jobs:
  deploy-backend-manual:
    name: Manual Backend Deployment
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment }}

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v1

    - name: Build and push Backend image
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        IMAGE_TAG: manual-backend-${{ github.sha }}
        ENVIRONMENT: ${{ github.event.inputs.environment }}
      run: |
        echo "🏗️ Building Backend image for $ENVIRONMENT..."
        docker build -f Dockerfile -t $ECR_REGISTRY/rozieai-zrh-uc05-backend-$ENVIRONMENT:$IMAGE_TAG .
        docker push $ECR_REGISTRY/rozieai-zrh-uc05-backend-$ENVIRONMENT:$IMAGE_TAG
        echo "✅ Backend image pushed: $ECR_REGISTRY/rozieai-zrh-uc05-backend-$ENVIRONMENT:$IMAGE_TAG"

    - name: Download task definition
      run: |
        aws ecs describe-task-definition --task-definition $ECS_TASK_DEFINITION --query taskDefinition > task-definition.json

    - name: Update Backend container in task definition
      id: task-def-backend
      uses: aws-actions/amazon-ecs-render-task-definition@v1
      with:
        task-definition: task-definition.json
        container-name: backend
        image: ${{ steps.login-ecr.outputs.registry }}/rozieai-zrh-uc05-backend-${{ github.event.inputs.environment }}:manual-backend-${{ github.sha }}

    - name: Deploy to Amazon ECS
      uses: aws-actions/amazon-ecs-deploy-task-definition@v1
      with:
        task-definition: ${{ steps.task-def-backend.outputs.task-definition }}
        service: ${{ env.ECS_SERVICE }}
        cluster: ${{ env.ECS_CLUSTER }}
        wait-for-service-stability: true

    - name: Manual Backend deployment success
      run: |
        echo "✅ Manual Backend deployment completed successfully!"
        echo "🌐 Backend API: https://rozieai-zrh-uc05.dev-scc-demo.rozie.ai/docs"
        echo "📦 Environment: ${{ github.event.inputs.environment }}"
        echo "🔧 Force rebuild: ${{ github.event.inputs.force_rebuild }}"
