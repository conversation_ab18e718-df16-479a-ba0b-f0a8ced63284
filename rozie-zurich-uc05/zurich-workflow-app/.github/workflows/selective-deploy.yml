name: Selective Component Deployment

on:
  push:
    branches:
      - zurich/dinesh/zurich_workflow_uc05
      - zurich/dinesh/zurich_workflow_uc05_dev
  workflow_dispatch:
    inputs:
      deploy_all:
        description: 'Deploy all components'
        required: false
        default: 'false'
        type: boolean
      deploy_frontend:
        description: 'Deploy frontend only'
        required: false
        default: 'false'
        type: boolean
      deploy_backend:
        description: 'Deploy backend only'
        required: false
        default: 'false'
        type: boolean
      deploy_dashboard:
        description: 'Deploy dashboard only'
        required: false
        default: 'false'
        type: boolean
      deploy_n8n:
        description: 'Deploy N8N only'
        required: false
        default: 'false'
        type: boolean
      deploy_nginx:
        description: 'Deploy nginx only'
        required: false
        default: 'false'
        type: boolean

env:
  AWS_REGION: ca-central-1
  ECS_SERVICE: rozieai-zurich-uc05
  ECS_CLUSTER: dev-scc-demo
  ECS_TASK_DEFINITION: rozieai-zurich-uc05

jobs:
  detect-changes:
    name: Detect Changed Components
    runs-on: ubuntu-latest
    outputs:
      frontend-changed: ${{ steps.changes.outputs.frontend }}
      backend-changed: ${{ steps.changes.outputs.backend }}
      dashboard-changed: ${{ steps.changes.outputs.dashboard }}
      n8n-changed: ${{ steps.changes.outputs.n8n }}
      nginx-changed: ${{ steps.changes.outputs.nginx }}
      deploy-all: ${{ github.event.inputs.deploy_all == 'true' }}
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4
      with:
        fetch-depth: 2

    - name: Detect changes
      uses: dorny/paths-filter@v2
      id: changes
      with:
        filters: |
          frontend:
            - 'frontend/**'
            - 'frontend.Dockerfile'
          backend:
            - 'backend/**'
            - 'Dockerfile'
            - 'requirements.txt'
            - 'baml_models/**'
          dashboard:
            - 'zurich-dashboard/**'
          n8n:
            - 'n8n.Dockerfile'
            - 'docker-compose.yml'
          nginx:
            - 'nginx/**'

    - name: Show detected changes
      run: |
        echo "🔍 Change Detection Results:"
        echo "Frontend changed: ${{ steps.changes.outputs.frontend }}"
        echo "Backend changed: ${{ steps.changes.outputs.backend }}"
        echo "Dashboard changed: ${{ steps.changes.outputs.dashboard }}"
        echo "N8N changed: ${{ steps.changes.outputs.n8n }}"
        echo "Nginx changed: ${{ steps.changes.outputs.nginx }}"
        echo "Deploy all: ${{ github.event.inputs.deploy_all }}"

  deploy-frontend:
    name: Deploy Frontend & Nginx
    runs-on: ubuntu-latest
    needs: detect-changes
    if: |
      needs.detect-changes.outputs.frontend-changed == 'true' ||
      needs.detect-changes.outputs.nginx-changed == 'true' ||
      needs.detect-changes.outputs.deploy-all == 'true' ||
      github.event.inputs.deploy_frontend == 'true' ||
      github.event.inputs.deploy_nginx == 'true'
    environment: development

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2

    - name: Build and push Frontend image
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        IMAGE_TAG: v${{ github.sha }}
      run: |
        echo "🏗️ Building Frontend image..."
        docker build -f frontend.Dockerfile -t $ECR_REGISTRY/rozieai-zrh-uc05-frontend-dev:$IMAGE_TAG .
        docker push $ECR_REGISTRY/rozieai-zrh-uc05-frontend-dev:$IMAGE_TAG
        echo "✅ Frontend image pushed: $ECR_REGISTRY/rozieai-zrh-uc05-frontend-dev:$IMAGE_TAG"

    - name: Build and push Nginx image
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        IMAGE_TAG: v${{ github.sha }}
      run: |
        echo "🏗️ Building Nginx image..."
        docker build -f nginx/Dockerfile -t $ECR_REGISTRY/rozieai-zrh-uc05-nginx-dev:$IMAGE_TAG ./nginx
        docker push $ECR_REGISTRY/rozieai-zrh-uc05-nginx-dev:$IMAGE_TAG
        echo "✅ Nginx image pushed: $ECR_REGISTRY/rozieai-zrh-uc05-nginx-dev:$IMAGE_TAG"

    - name: Download task definition
      run: |
        aws ecs describe-task-definition --task-definition $ECS_TASK_DEFINITION --query taskDefinition > task-definition.json

    - name: Update Frontend container in task definition
      id: task-def-frontend
      uses: aws-actions/amazon-ecs-render-task-definition@v1
      with:
        task-definition: task-definition.json
        container-name: frontend
        image: ${{ steps.login-ecr.outputs.registry }}/rozieai-zrh-uc05-frontend-dev:v${{ github.sha }}

    - name: Update Nginx container in task definition
      id: task-def-nginx
      uses: aws-actions/amazon-ecs-render-task-definition@v1
      with:
        task-definition: ${{ steps.task-def-frontend.outputs.task-definition }}
        container-name: nginx
        image: ${{ steps.login-ecr.outputs.registry }}/rozieai-zrh-uc05-nginx-dev:v${{ github.sha }}

    - name: Deploy to Amazon ECS
      uses: aws-actions/amazon-ecs-deploy-task-definition@v1
      with:
        task-definition: ${{ steps.task-def-nginx.outputs.task-definition }}
        service: ${{ env.ECS_SERVICE }}
        cluster: ${{ env.ECS_CLUSTER }}
        wait-for-service-stability: true

    - name: Frontend deployment success
      run: |
        echo "✅ Frontend deployment completed successfully!"
        echo "🌐 Frontend URL: https://rozieai-zrh-uc05.dev-scc-demo.rozie.ai/"

  deploy-backend:
    name: Deploy Backend
    runs-on: ubuntu-latest
    needs: detect-changes
    if: |
      needs.detect-changes.outputs.backend-changed == 'true' ||
      needs.detect-changes.outputs.deploy-all == 'true' ||
      github.event.inputs.deploy_backend == 'true'
    environment: development

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2

    - name: Build and push Backend image
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        IMAGE_TAG: v${{ github.sha }}
      run: |
        echo "🏗️ Building Backend image..."
        docker build -f Dockerfile -t $ECR_REGISTRY/rozieai-zrh-uc05-backend-dev:$IMAGE_TAG .
        docker push $ECR_REGISTRY/rozieai-zrh-uc05-backend-dev:$IMAGE_TAG
        echo "✅ Backend image pushed: $ECR_REGISTRY/rozieai-zrh-uc05-backend-dev:$IMAGE_TAG"

    - name: Download task definition
      run: |
        aws ecs describe-task-definition --task-definition $ECS_TASK_DEFINITION --query taskDefinition > task-definition.json

    - name: Update Backend container in task definition
      id: task-def-backend
      uses: aws-actions/amazon-ecs-render-task-definition@v1
      with:
        task-definition: task-definition.json
        container-name: backend
        image: ${{ steps.login-ecr.outputs.registry }}/rozieai-zrh-uc05-backend-dev:v${{ github.sha }}

    - name: Deploy to Amazon ECS
      uses: aws-actions/amazon-ecs-deploy-task-definition@v1
      with:
        task-definition: ${{ steps.task-def-backend.outputs.task-definition }}
        service: ${{ env.ECS_SERVICE }}
        cluster: ${{ env.ECS_CLUSTER }}
        wait-for-service-stability: true

    - name: Backend deployment success
      run: |
        echo "✅ Backend deployment completed successfully!"
        echo "🌐 Backend API: https://rozieai-zrh-uc05.dev-scc-demo.rozie.ai/docs"

  deploy-dashboard:
    name: Deploy Dashboard
    runs-on: ubuntu-latest
    needs: detect-changes
    if: |
      needs.detect-changes.outputs.dashboard-changed == 'true' ||
      needs.detect-changes.outputs.deploy-all == 'true' ||
      github.event.inputs.deploy_dashboard == 'true'
    environment: development

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2

    - name: Build and push Dashboard image
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        IMAGE_TAG: v${{ github.sha }}
      run: |
        echo "🏗️ Building Dashboard image..."
        docker build -f zurich-dashboard/Dockerfile -t $ECR_REGISTRY/rozieai-zrh-uc05-dashboard-dev:$IMAGE_TAG ./zurich-dashboard
        docker push $ECR_REGISTRY/rozieai-zrh-uc05-dashboard-dev:$IMAGE_TAG
        echo "✅ Dashboard image pushed: $ECR_REGISTRY/rozieai-zrh-uc05-dashboard-dev:$IMAGE_TAG"

    - name: Download task definition
      run: |
        aws ecs describe-task-definition --task-definition $ECS_TASK_DEFINITION --query taskDefinition > task-definition.json

    - name: Update Dashboard container in task definition
      id: task-def-dashboard
      uses: aws-actions/amazon-ecs-render-task-definition@v1
      with:
        task-definition: task-definition.json
        container-name: dashboard
        image: ${{ steps.login-ecr.outputs.registry }}/rozieai-zrh-uc05-dashboard-dev:v${{ github.sha }}

    - name: Deploy to Amazon ECS
      uses: aws-actions/amazon-ecs-deploy-task-definition@v1
      with:
        task-definition: ${{ steps.task-def-dashboard.outputs.task-definition }}
        service: ${{ env.ECS_SERVICE }}
        cluster: ${{ env.ECS_CLUSTER }}
        wait-for-service-stability: true

    - name: Dashboard deployment success
      run: |
        echo "✅ Dashboard deployment completed successfully!"
        echo "🌐 Dashboard URL: https://rozieai-zrh-uc05.dev-scc-demo.rozie.ai/dashboard/"

  deploy-n8n:
    name: Deploy N8N
    runs-on: ubuntu-latest
    needs: detect-changes
    if: |
      needs.detect-changes.outputs.n8n-changed == 'true' ||
      needs.detect-changes.outputs.deploy-all == 'true' ||
      github.event.inputs.deploy_n8n == 'true'
    environment: development

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2

    - name: Build and push N8N image
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        IMAGE_TAG: v${{ github.sha }}
      run: |
        echo "🏗️ Building N8N image..."
        docker build -f n8n.Dockerfile -t $ECR_REGISTRY/rozieai-zrh-uc05-n8n-dev:$IMAGE_TAG .
        docker push $ECR_REGISTRY/rozieai-zrh-uc05-n8n-dev:$IMAGE_TAG
        echo "✅ N8N image pushed: $ECR_REGISTRY/rozieai-zrh-uc05-n8n-dev:$IMAGE_TAG"

    - name: Download task definition
      run: |
        aws ecs describe-task-definition --task-definition $ECS_TASK_DEFINITION --query taskDefinition > task-definition.json

    - name: Update N8N container in task definition
      id: task-def-n8n
      uses: aws-actions/amazon-ecs-render-task-definition@v1
      with:
        task-definition: task-definition.json
        container-name: n8n
        image: ${{ steps.login-ecr.outputs.registry }}/rozieai-zrh-uc05-n8n-dev:v${{ github.sha }}

    - name: Deploy to Amazon ECS
      uses: aws-actions/amazon-ecs-deploy-task-definition@v1
      with:
        task-definition: ${{ steps.task-def-n8n.outputs.task-definition }}
        service: ${{ env.ECS_SERVICE }}
        cluster: ${{ env.ECS_CLUSTER }}
        wait-for-service-stability: true

    - name: N8N deployment success
      run: |
        echo "✅ N8N deployment completed successfully!"
        echo "🌐 N8N URL: https://n8n-zurich.dev-scc-demo.rozie.ai"

  deployment-summary:
    name: Deployment Summary
    runs-on: ubuntu-latest
    needs: [detect-changes, deploy-frontend, deploy-backend, deploy-dashboard, deploy-n8n]
    if: always()
    
    steps:
    - name: Deployment Summary
      run: |
        echo "📋 DEPLOYMENT SUMMARY"
        echo "===================="
        echo "Frontend: ${{ needs.deploy-frontend.result || 'skipped' }}"
        echo "Backend: ${{ needs.deploy-backend.result || 'skipped' }}"
        echo "Dashboard: ${{ needs.deploy-dashboard.result || 'skipped' }}"
        echo "N8N: ${{ needs.deploy-n8n.result || 'skipped' }}"
        echo ""
        echo "🌐 Access URLs:"
        echo "Frontend: https://rozieai-zrh-uc05.dev-scc-demo.rozie.ai/"
        echo "Backend API: https://rozieai-zrh-uc05.dev-scc-demo.rozie.ai/docs"
        echo "Dashboard: https://rozieai-zrh-uc05.dev-scc-demo.rozie.ai/dashboard/"
        echo "N8N: https://n8n-zurich.dev-scc-demo.rozie.ai"
