#!/bin/bash

# =============================================================================
# STANDALONE N8N DEPLOYMENT SCRIPT
# =============================================================================
# This script deploys N8N without nginx reverse proxy to eliminate
# path prefix and WebSocket connection issues
# =============================================================================

set -e

echo "🚀 Deploying Standalone N8N..."

# Stop existing N8N if running
echo "📦 Stopping existing N8N containers..."
docker-compose -f docker-compose.n8n-standalone.yml down || true

# Remove old containers and volumes if needed
echo "🧹 Cleaning up old containers..."
docker container rm n8n-standalone 2>/dev/null || true

# Pull latest N8N image
echo "📥 Pulling latest N8N image..."
docker pull n8nio/n8n:latest

# Start standalone N8N
echo "🎯 Starting standalone N8N..."
docker-compose -f docker-compose.n8n-standalone.yml up -d

# Wait for N8N to start
echo "⏳ Waiting for N8N to start..."
sleep 30

# Check N8N status
echo "🔍 Checking N8N status..."
docker-compose -f docker-compose.n8n-standalone.yml ps

# Test N8N endpoint
echo "🌐 Testing N8N endpoint..."
curl -f http://localhost:5678/healthz || echo "❌ N8N health check failed"

echo "✅ Standalone N8N deployment complete!"
echo ""
echo "🔗 Access N8N at:"
echo "   Local:  http://localhost:5678"
echo "   Remote: https://rozieai-zrh-uc05.dev-scc-demo.rozie.ai:5678"
echo ""
echo "📋 To check logs:"
echo "   docker logs n8n-standalone -f"
echo ""
echo "🛑 To stop:"
echo "   docker-compose -f docker-compose.n8n-standalone.yml down"
