# N8N Dockerfile for Zurich Workflow
FROM n8nio/n8n:latest

# Install curl for health checks
USER root
RUN apk add --no-cache curl

# Ensure proper permissions for n8n data directory
RUN mkdir -p /home/<USER>/.n8n && \
    chown -R 1000:1000 /home/<USER>/.n8n && \
    chmod -R 755 /home/<USER>/.n8n

# Switch back to node user
USER node

# Official AWS ECS Fargate N8N Environment Variables
ENV N8N_HOST=0.0.0.0
ENV N8N_PORT=5678
ENV N8N_PROTOCOL=https
ENV N8N_PATH=/n8n/
ENV N8N_USER_FOLDER=/home/<USER>

# Health check - use a simpler endpoint that's more reliable
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:5678/ || exit 1

# Expose port
EXPOSE 5678

# Use the default entrypoint from the base image
